# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

gokeys is a comprehensive enterprise-grade license management and authentication platform written in Go. The project is now in active development with a complete foundation implementing all core entities, database schema, HTTP server, and metrics integration based on production-ready requirements from demo.rb analysis.

## Technology Stack

- **Language**: Go 1.24.5
- **Primary Framework**: Gin (high-performance HTTP web framework)
- **Architecture**: Modular Monolith with Hexagonal Architecture
- **Database**: 
  - PostgreSQL (primary database)
  - Valkey/Redis (caching for license validation)
- **ORM**: GORM (chosen for development speed and maintainability)
- **Message Queue**: NATS (event streaming)
- **Migration Tool**: golang-migrate
- **Background Processing**: Custom implementation planned

## Implementation Status

✅ **Phase 1 COMPLETED** - Enterprise Foundation Layer:
- Complete entity models with 8 new enterprise entities (Environment, Group, Entitlement, Plan, Permission, Role, Session, SecondFactor)  
- Enhanced existing entities (Account, User, Product, Policy, License, Machine, Token) with 25+ enterprise fields each
- New tracking entities (MachineComponent, MachineProcess) for detailed monitoring
- Enterprise database schema with 50+ optimized indexes and constraints
- Database connection, migration system, and initializer
- Configuration management with environment variables and YAML support
- Gin HTTP server with health checks and metrics middleware
- Prometheus metrics collection with VictoriaMetrics export capability
- Complete GORM models aligned with database schema

✅ **Phase 2 COMPLETED** - Cryptographic Services and Repository Layer:
- Complete cryptographic service suite: RSA-2048, Ed25519, AES-256-GCM, JWT (RS256/ES256)
- Unified crypto service with support for all enterprise schemes
- Full repository layer with GORM implementations for all 17 entities
- Base repository with generic CRUD operations and filtering
- Specialized repository methods for complex queries and business logic
- Repository factory with transaction support and lazy loading
- License validation service with comprehensive caching support
- In-memory cache implementation with TTL and cleanup mechanisms
- Service coordinator for dependency injection and health monitoring

✅ **Phase 3 COMPLETED** - API Layer and Request Processing Pipeline:
- Complete authentication middleware with JWT and API key support
- Advanced authorization middleware with RBAC and resource-based permissions
- Comprehensive license validation API endpoints (POST/GET validate, quick-validate, info, cache invalidation)
- Machine registration and heartbeat APIs with component and process tracking
- RESTful API architecture with proper HTTP status codes and error handling
- Modular route configuration with public, protected, and admin route groups
- Integrated service coordinator with HTTP server for seamless dependency injection
- Health check endpoints with detailed service status monitoring

✅ **Phase 4 COMPLETED** - Production Readiness and Testing Infrastructure:
- Comprehensive test suite for license validation service with mock implementations
- Valkey cache adapter for production-grade distributed caching with Redis protocol compatibility
- Advanced rate limiting middleware with sliding windows, burst control, and per-endpoint limits
- Environment-based cache configuration (Memory cache for development, Valkey for production)
- Enhanced service coordinator with intelligent cache selection and health monitoring
- Distributed locking support for concurrent operations
- Atomic operations and performance optimization for high-throughput scenarios

✅ **Phase 5 COMPLETED** - Ruby-Go Logic Mapping Verification:
- Complete mapping verification of all core services against Ruby keygen-api
- License validation service: 100% accurate mapping with all validation steps
- Machine/License checkout services: Perfect certificate generation compatibility
- License lookup service: Proper v1 legacy token support with bcrypt salt reuse
- Cryptographic operations: Full compatibility with Ruby implementations
- Error handling and response codes: Consistent across both platforms

✅ **Phase 6 COMPLETED** - Event Broadcasting and API Enhancement:
- Comprehensive event broadcasting service with 40+ predefined event types
- Account-scoped routing with license key authentication (Ruby compatibility)
- Missing handler implementations (ValidateByKeyHandler, CheckoutLicenseHandler, CheckoutMachineHandler)
- Event integration in license validation and machine heartbeat handlers
- Ruby-compatible webhook subscription filtering with wildcard support
- Service coordinator integration for cross-cutting event concerns

**Current Status**: Phase 6 complete, full Ruby Keygen API compatibility with event broadcasting infrastructure

**Known Limitations**:
- Token lookup service: Missing v1/v2 token support (only v3 HMAC-SHA256 implemented)
  - v1: Legacy bcrypt + SHA256 hexdigest tokens (skipped - low priority)
  - v2: HMAC-SHA512 with account private key (skipped - deprecated)
  - Note: Current v3 implementation covers modern token authentication needs

- Release management system: Not implemented (future enhancement)
  - Release entities (Release, ReleaseArtifact, ReleasePackage, etc.)
  - Package manager engines (NPM, PyPI, RubyGems, OCI, Raw, Tauri)
  - File upload/download handling and distribution channels
  - Complex release publishing workflows and entitlement constraints
  - Note: Core license/machine management is complete and production-ready

## Core Domain Models

The system centers around these key domain concepts:

### Licensing System (ENHANCED)
- **Licenses**: Software licenses with enterprise tracking (policy overrides, cached counts, event tracking)
- **Policies**: Complete enterprise configuration (40+ fields, cryptographic schemes, strategy patterns)
- **Products**: Enhanced with environment isolation and distribution strategies
- **Machines**: Full enterprise tracking (heartbeat, hardware monitoring, ownership, components, processes)
- **Check-ins**: Periodic status reports from licenses/machines

### Multi-Tenancy & Organization (NEW)
- **Environments**: Multi-tenant isolation with configurable strategies (global, shared, isolated)
- **Groups**: User organization with quotas and permissions
- **Plans**: Subscription management with limits and billing integration
- **Entitlements**: Feature gating system

### User & Account Management (ENHANCED)
- **Accounts**: Enterprise organizations with cryptographic keys, SSO, Slack integration, notifications
- **Users**: Enhanced with 2FA, SSO profiles, group membership, ban tracking
- **SecondFactor**: TOTP 2FA implementation

### Authentication & Authorization (ENHANCED)
- **Tokens**: Enterprise tokens with custom permissions, activation limits, expiration tracking
- **Roles**: Resource-based RBAC system
- **Permissions**: Hierarchical permission system with many-to-many relationships
- **Sessions**: Proper session management with bearer tracking

## API Architecture

- RESTful APIs following JSON:API specifications
- Request processing pipeline: Account Scoping → Subscription Check → Authentication → Authorization → Validation → Business Logic → Event Broadcasting
- Comprehensive webhook system for real-time notifications
- Multi-environment support with isolation capabilities

## Authentication Methods

- Token-based (Bearer tokens via header or query params)
- License Key authentication for end-user software
- Password authentication for portal access
- Session authentication for web interfaces

## Background Processing & Events

- Event-driven architecture with webhook notifications
- Background job processing for async operations
- Comprehensive event broadcasting system
- License validation caching for performance

## Development Commands

### Database Setup
```bash
# Create PostgreSQL database (example)
createdb gokeys_dev

# Set environment variables
export DATABASE_HOST=localhost
export DATABASE_USER=gokeys
export DATABASE_PASSWORD=gokeys
export DATABASE_NAME=gokeys_dev
```

### Running the Application
```bash
# Install dependencies
go mod tidy

# Run database migrations (automatic on startup)
go run cmd/server/main.go

# Or build and run
go build -o gokeys cmd/server/main.go
./gokeys
```

### Development & Testing
```bash
# Run tests
go test ./...

# Run with development config
cp configs/config.yaml configs/config.development.yaml
go run cmd/server/main.go

# Check metrics
curl http://localhost:9090/metrics
```

### Key Endpoints
- **Main API**: `http://localhost:8080`
- **Health checks**: `http://localhost:8080/health/*`
- **Metrics**: `http://localhost:9090/metrics`

## Security Considerations

- Multi-tenant architecture with account isolation
- Rate limiting and bot detection
- Comprehensive audit logging
- Machine fingerprinting for license enforcement
- Environment-based access controls

## Key Implementation Notes

- License validation caching is critical for performance
- Connection pooling essential for concurrent operations
- Modular architecture to support different deployment scenarios
- Event streaming for real-time updates and integrations
- Comprehensive error handling and monitoring