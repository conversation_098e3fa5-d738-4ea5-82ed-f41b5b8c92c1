#!/bin/bash

# GoKeys API Deployment Script
# Triển khai API hoàn chỉnh với permission management

set -e

echo "🚀 Starting GoKeys API Deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="gokeys"
BUILD_DIR="./build"
CONFIG_DIR="./configs"
DOCS_DIR="./docs"
PORT="${PORT:-8080}"
METRICS_PORT="${METRICS_PORT:-9090}"
ENV="${ENV:-development}"

# Database configuration
DB_HOST="${DB_HOST:-localhost}"
DB_PORT="${DB_PORT:-5432}"
DB_USER="${DB_USER:-gokeys}"
DB_PASSWORD="${DB_PASSWORD:-gokeys}"
DB_NAME="${DB_NAME:-gokeys_dev}"

echo -e "${BLUE}📋 Deployment Configuration:${NC}"
echo "  - Environment: $ENV"
echo "  - API Port: $PORT"
echo "  - Metrics Port: $METRICS_PORT"
echo "  - Database: $DB_USER@$DB_HOST:$DB_PORT/$DB_NAME"
echo ""

# Create build directory
echo -e "${YELLOW}📁 Creating build directory...${NC}"
mkdir -p $BUILD_DIR

# Build the application
echo -e "${YELLOW}🔨 Building GoKeys API...${NC}"
go build -o $BUILD_DIR/$PROJECT_NAME cmd/server/main.go

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build successful!${NC}"
else
    echo -e "${RED}❌ Build failed!${NC}"
    exit 1
fi

# Create configuration file if it doesn't exist
echo -e "${YELLOW}⚙️  Setting up configuration...${NC}"
mkdir -p $CONFIG_DIR

if [ ! -f "$CONFIG_DIR/config.yaml" ]; then
    cat > "$CONFIG_DIR/config.yaml" << EOF
# GoKeys API Configuration
server:
  host: "0.0.0.0"
  port: $PORT
  environment: "$ENV"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

database:
  host: "$DB_HOST"
  port: $DB_PORT
  user: "$DB_USER"
  password: "$DB_PASSWORD"
  name: "$DB_NAME"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300

security:
  jwt_private_key: "your-jwt-private-key-here"
  jwt_public_key: "your-jwt-public-key-here"
  api_key_salt: "your-api-key-salt-here"

metrics:
  enabled: true
  port: $METRICS_PORT
  path: "/metrics"

cache:
  type: "memory" # or "valkey" for production
  ttl: 300 # 5 minutes
  cleanup_interval: 600 # 10 minutes

logging:
  level: "info"
  format: "json"
EOF
    echo -e "${GREEN}✅ Configuration file created: $CONFIG_DIR/config.yaml${NC}"
else
    echo -e "${GREEN}✅ Using existing configuration: $CONFIG_DIR/config.yaml${NC}"
fi

# Database setup
echo -e "${YELLOW}🗄️  Setting up database...${NC}"

# Check if database is accessible
if command -v psql &> /dev/null; then
    echo "Testing database connection..."
    if PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;" &> /dev/null; then
        echo -e "${GREEN}✅ Database connection successful!${NC}"
    else
        echo -e "${RED}❌ Database connection failed!${NC}"
        echo "Please ensure PostgreSQL is running and credentials are correct."
        echo "You can create the database with:"
        echo "  createdb -h $DB_HOST -p $DB_PORT -U $DB_USER $DB_NAME"
        # Don't exit - let the application handle database setup
    fi
else
    echo -e "${YELLOW}⚠️  psql not found, skipping connection test${NC}"
fi

# Create systemd service file (optional)
if [ "$ENV" = "production" ]; then
    echo -e "${YELLOW}🔧 Creating systemd service...${NC}"
    sudo tee /etc/systemd/system/gokeys.service > /dev/null << EOF
[Unit]
Description=GoKeys License Management API
After=network.target postgresql.service

[Service]
Type=simple
User=gokeys
Group=gokeys
WorkingDirectory=$(pwd)
ExecStart=$(pwd)/$BUILD_DIR/$PROJECT_NAME
Restart=always
RestartSec=10
Environment=CONFIG_PATH=$(pwd)/$CONFIG_DIR/config.yaml

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$(pwd)

[Install]
WantedBy=multi-user.target
EOF
    
    echo -e "${GREEN}✅ Systemd service created: /etc/systemd/system/gokeys.service${NC}"
    sudo systemctl daemon-reload
fi

# Create startup script
echo -e "${YELLOW}📄 Creating startup script...${NC}"
cat > start-gokeys.sh << 'EOF'
#!/bin/bash

# GoKeys API Startup Script

set -e

PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$PROJECT_DIR"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🚀 Starting GoKeys API...${NC}"

# Set configuration path
export CONFIG_PATH="./configs/config.yaml"

# Check if binary exists
if [ ! -f "./build/gokeys" ]; then
    echo -e "${RED}❌ Binary not found! Please run deploy-api.sh first.${NC}"
    exit 1
fi

# Check if config exists
if [ ! -f "$CONFIG_PATH" ]; then
    echo -e "${RED}❌ Configuration file not found: $CONFIG_PATH${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Configuration: $CONFIG_PATH${NC}"
echo -e "${YELLOW}🌐 Starting server...${NC}"

# Start the application
exec ./build/gokeys
EOF

chmod +x start-gokeys.sh
echo -e "${GREEN}✅ Startup script created: start-gokeys.sh${NC}"

# Create API test script
echo -e "${YELLOW}🧪 Creating API test script...${NC}"
cat > test-api.sh << 'EOF'
#!/bin/bash

# GoKeys API Test Script

set -e

# Configuration
API_BASE="http://localhost:8080/api/v1"
ADMIN_TOKEN="your-admin-token-here"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🧪 Testing GoKeys API Endpoints...${NC}"

# Health check
echo -e "${YELLOW}Testing health endpoints...${NC}"
curl -s "$API_BASE/../health/live" | jq '.' || echo "Health check failed"
curl -s "$API_BASE/../health/ready" | jq '.' || echo "Readiness check failed"

# API endpoints (requires authentication)
echo -e "${YELLOW}Testing authenticated endpoints...${NC}"

# Test permission statistics (admin only)
echo -e "Testing permission statistics..."
curl -s -X GET "$API_BASE/admin/permissions/stats" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" | jq '.' || echo "Permission stats test failed"

# Test permission check
echo -e "Testing permission check..."
curl -s -X POST "$API_BASE/admin/permissions/check" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "scope": "S",
    "resource_type": "license",
    "action": "read"
  }' | jq '.' || echo "Permission check test failed"

echo -e "${GREEN}✅ API tests completed!${NC}"
echo -e "${YELLOW}Note: Update ADMIN_TOKEN in test-api.sh for full testing${NC}"
EOF

chmod +x test-api.sh
echo -e "${GREEN}✅ API test script created: test-api.sh${NC}"

# Create Docker setup (optional)
echo -e "${YELLOW}🐳 Creating Docker configuration...${NC}"
cat > Dockerfile << 'EOF'
# Multi-stage build for GoKeys API
FROM golang:1.21-alpine AS builder

# Install dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o gokeys cmd/server/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates tzdata

# Create app user
RUN adduser -D -s /bin/sh gokeys

# Set working directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/gokeys .
COPY --from=builder /app/configs ./configs

# Change ownership
RUN chown -R gokeys:gokeys /app
USER gokeys

# Expose ports
EXPOSE 8080 9090

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health/live || exit 1

# Run the application
CMD ["./gokeys"]
EOF

cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  gokeys-api:
    build: .
    ports:
      - "8080:8080"
      - "9090:9090"
    environment:
      - CONFIG_PATH=/app/configs/config.yaml
      - DATABASE_HOST=postgres
      - DATABASE_USER=gokeys
      - DATABASE_PASSWORD=gokeys
      - DATABASE_NAME=gokeys_dev
    depends_on:
      - postgres
      - valkey
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=gokeys
      - POSTGRES_PASSWORD=gokeys
      - POSTGRES_DB=gokeys_dev
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gokeys"]
      interval: 30s
      timeout: 10s
      retries: 3

  valkey:
    image: valkey/valkey:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "valkey-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
EOF

echo -e "${GREEN}✅ Docker configuration created${NC}"

# Create API documentation server
echo -e "${YELLOW}📚 Creating documentation server...${NC}"
cat > serve-docs.sh << 'EOF'
#!/bin/bash

# Simple documentation server

PORT=${1:-3000}
DOCS_DIR="./docs"

if [ ! -d "$DOCS_DIR" ]; then
    echo "❌ Documentation directory not found: $DOCS_DIR"
    exit 1
fi

echo "📚 Serving documentation on http://localhost:$PORT"
echo "📄 Available docs:"
ls -la $DOCS_DIR/*.md

# Use Python's built-in server or Node.js if available
if command -v python3 &> /dev/null; then
    cd $DOCS_DIR && python3 -m http.server $PORT
elif command -v python &> /dev/null; then
    cd $DOCS_DIR && python -m SimpleHTTPServer $PORT
else
    echo "❌ Python not found. Please install Python or use a web server to serve $DOCS_DIR"
fi
EOF

chmod +x serve-docs.sh
echo -e "${GREEN}✅ Documentation server script created: serve-docs.sh${NC}"

# Final deployment summary
echo ""
echo -e "${GREEN}🎉 GoKeys API Deployment Complete!${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Configure your database credentials in $CONFIG_DIR/config.yaml"
echo "2. Update JWT keys and API salt in the configuration"
echo "3. Start the API:"
echo -e "   ${YELLOW}./start-gokeys.sh${NC}"
echo ""
echo -e "${BLUE}🔧 Available Scripts:${NC}"
echo -e "  ${YELLOW}./start-gokeys.sh${NC}     - Start the API server"
echo -e "  ${YELLOW}./test-api.sh${NC}        - Test API endpoints"
echo -e "  ${YELLOW}./serve-docs.sh${NC}      - Serve documentation"
echo ""
echo -e "${BLUE}🐳 Docker Deployment:${NC}"
echo -e "  ${YELLOW}docker-compose up -d${NC} - Start with Docker"
echo ""
echo -e "${BLUE}📚 Documentation:${NC}"
echo "  - API Docs: $DOCS_DIR/permission-management-api.md"
echo "  - Authorization Guide: $DOCS_DIR/authorization-optimization-guide.md"
echo ""
echo -e "${BLUE}🌐 API Endpoints:${NC}"
echo "  - Health: http://localhost:$PORT/health/live"
echo "  - API: http://localhost:$PORT/api/v1/"
echo "  - Metrics: http://localhost:$METRICS_PORT/metrics"
echo "  - Admin: http://localhost:$PORT/api/v1/admin/"
echo ""
echo -e "${GREEN}✅ Ready for production deployment!${NC}"
EOF