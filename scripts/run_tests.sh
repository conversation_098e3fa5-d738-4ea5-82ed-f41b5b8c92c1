#!/bin/bash

# run_tests.sh - Script để chạy toàn bộ tests cho GoKeyScore
# Bao gồm unit tests, integration tests, và business logic tests

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

print_status "GoKeyScore Test Runner"
print_status "Project Root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Check if go.mod exists
if [ ! -f "go.mod" ]; then
    print_error "go.mod not found. Please run from project root."
    exit 1
fi

# Function to run tests with coverage
run_tests() {
    local test_path=$1
    local test_name=$2
    
    print_status "Running $test_name..."
    
    if [ -d "$test_path" ]; then
        # Run tests with verbose output and coverage
        if go test -v -race -coverprofile=coverage.out "$test_path"; then
            print_success "$test_name passed!"
            
            # Show coverage if coverage file exists
            if [ -f "coverage.out" ]; then
                coverage=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
                print_status "Coverage: $coverage"
                rm coverage.out
            fi
        else
            print_error "$test_name failed!"
            return 1
        fi
    else
        print_warning "$test_name directory not found: $test_path"
    fi
}

# Function to run specific package tests
run_package_tests() {
    local package_path=$1
    local package_name=$2
    
    print_status "Running $package_name tests..."
    
    if go test -v -race -coverprofile=coverage.out "$package_path"; then
        print_success "$package_name tests passed!"
        
        # Show coverage if coverage file exists
        if [ -f "coverage.out" ]; then
            coverage=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
            print_status "Coverage: $coverage"
            rm coverage.out
        fi
    else
        print_error "$package_name tests failed!"
        return 1
    fi
}

# Main test execution
main() {
    print_status "Starting GoKeyScore test suite..."
    
    # Check Go version
    go_version=$(go version)
    print_status "Go version: $go_version"
    
    # Download dependencies
    print_status "Downloading dependencies..."
    go mod download
    
    # Tidy up dependencies
    print_status "Tidying dependencies..."
    go mod tidy
    
    # Run entity tests (business logic)
    print_status "=== ENTITY BUSINESS LOGIC TESTS ==="
    run_package_tests "./internal/domain/entities" "Entity Business Logic"
    
    # Run service tests
    print_status "=== SERVICE TESTS ==="
    run_package_tests "./internal/domain/services/..." "Services"
    
    # Run repository tests (if they exist)
    print_status "=== REPOSITORY TESTS ==="
    if [ -d "./internal/adapters/repositories" ]; then
        run_package_tests "./internal/adapters/repositories/..." "Repositories"
    else
        print_warning "Repository tests not found"
    fi
    
    # Run handler tests (if they exist)
    print_status "=== HANDLER TESTS ==="
    if [ -d "./internal/adapters/http/handlers" ]; then
        run_package_tests "./internal/adapters/http/handlers" "HTTP Handlers"
    else
        print_warning "Handler tests not found"
    fi
    
    # Run integration tests
    print_status "=== INTEGRATION TESTS ==="
    run_tests "./tests/integration" "Integration Tests"
    
    # Run all tests together for final verification
    print_status "=== FINAL VERIFICATION ==="
    print_status "Running all tests together..."
    
    if go test -v -race ./...; then
        print_success "All tests passed!"
    else
        print_error "Some tests failed in final verification!"
        return 1
    fi
    
    # Generate coverage report
    print_status "=== COVERAGE REPORT ==="
    print_status "Generating coverage report..."
    
    if go test -coverprofile=coverage.out ./...; then
        go tool cover -html=coverage.out -o coverage.html
        print_success "Coverage report generated: coverage.html"
        
        # Show total coverage
        total_coverage=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
        print_success "Total Coverage: $total_coverage"
        
        # Clean up
        rm coverage.out
    else
        print_warning "Could not generate coverage report"
    fi
    
    print_success "🎉 All tests completed successfully!"
    print_status "Test summary:"
    print_status "✅ Entity business logic tests"
    print_status "✅ Service tests"
    print_status "✅ Repository tests"
    print_status "✅ Handler tests"
    print_status "✅ Integration tests"
    print_status "✅ Final verification"
    print_status "✅ Coverage report generated"
}

# Function to run specific test
run_specific_test() {
    local test_pattern=$1
    print_status "Running specific test: $test_pattern"
    
    if go test -v -race -run "$test_pattern" ./...; then
        print_success "Specific test passed: $test_pattern"
    else
        print_error "Specific test failed: $test_pattern"
        return 1
    fi
}

# Function to run tests in watch mode
run_watch_mode() {
    print_status "Running tests in watch mode..."
    print_status "Press Ctrl+C to stop"
    
    # Simple watch implementation
    while true; do
        clear
        print_status "Running tests... ($(date))"
        
        if go test -v ./...; then
            print_success "Tests passed at $(date)"
        else
            print_error "Tests failed at $(date)"
        fi
        
        print_status "Waiting 5 seconds before next run..."
        sleep 5
    done
}

# Parse command line arguments
case "${1:-}" in
    "specific")
        if [ -z "${2:-}" ]; then
            print_error "Please provide test pattern: ./run_tests.sh specific TestName"
            exit 1
        fi
        run_specific_test "$2"
        ;;
    "watch")
        run_watch_mode
        ;;
    "coverage")
        print_status "Running tests with coverage only..."
        go test -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html
        total_coverage=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
        print_success "Coverage report generated: coverage.html"
        print_success "Total Coverage: $total_coverage"
        rm coverage.out
        ;;
    "help"|"-h"|"--help")
        echo "GoKeyScore Test Runner"
        echo ""
        echo "Usage:"
        echo "  ./run_tests.sh              Run all tests"
        echo "  ./run_tests.sh specific <pattern>  Run specific test pattern"
        echo "  ./run_tests.sh watch         Run tests in watch mode"
        echo "  ./run_tests.sh coverage      Generate coverage report only"
        echo "  ./run_tests.sh help          Show this help"
        echo ""
        echo "Examples:"
        echo "  ./run_tests.sh specific TestOrganization"
        echo "  ./run_tests.sh specific TestPolicy"
        echo "  ./run_tests.sh watch"
        ;;
    "")
        main
        ;;
    *)
        print_error "Unknown command: $1"
        print_status "Use './run_tests.sh help' for usage information"
        exit 1
        ;;
esac
