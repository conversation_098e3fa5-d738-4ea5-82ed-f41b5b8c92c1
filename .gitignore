# Dependencies
vendor
frontend/node_modules

# Build artifacts
dist
bin/
build/
tmp/
*.exe
*.exe~
*.dll
*.so
*.dylib

# Next.js
frontend/**/.next/
frontend/**/.vercel/
frontend/**/out/

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out
coverage/

# Go workspace file
go.work

# Environment files
.env
.env.local
.env.*.local
*.env

# Logs
logs/
*.log
server.log

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Air live reload
.air.toml

# Temporary files
handlers.test
main
server
goskey
demo.rb

# Claude AI files

.specstory/

# Docker
.dockerignore

# Generated documentation
docs/swagger.json
docs/swagger.yaml
