# GoKeys Implementation Plan

## Overview
GoKeys is an enterprise-grade license management platform built with Go, designed to provide comprehensive software licensing, user management, and audit capabilities. This plan outlines the complete implementation roadmap based on analysis of production-ready requirements.

## Technology Stack
- **Language**: Go 1.24.5
- **Framework**: Gin (high-performance HTTP)
- **Architecture**: Modular Monolith with Hexagonal Architecture
- **Database**: PostgreSQL (primary) + Valkey/Redis (caching)
- **ORM**: GORM
- **Message Queue**: NATS (event streaming)
- **Monitoring**: VictoriaMetrics + Grafana
- **Migration**: golang-migrate
- **Cryptography**: Ed25519, RSA-2048, AES-256-GCM, JWT (RS256/ES256)
- **Security**: Argon2id password hashing, TOTP for 2FA

## Core Domain Models Analysis

### Current vs Required (Based on Production Schema)

#### ✅ Basic Entities (Implemented)
- Account, User, Product, Policy, License, Machine, Token, CheckIn

#### 🔄 Entities Requiring Major Expansion
All current entities need significant field additions to match enterprise requirements.

#### 🆕 Missing Critical Entities
- Environment (multi-tenancy)
- Group (user organization) 
- Entitlement (feature gating)
- Plan (subscription management)
- Session (proper auth sessions)
- EventLog, EventType (audit trail)
- Permission, Role (RBAC)
- MachineComponent, MachineProcess (detailed tracking)
- WebhookEndpoint, WebhookEvent (notifications)
- SecondFactor (2FA support)

## Cryptographic Architecture

### Multi-Layer Cryptographic Security
GoKeys implements enterprise-grade cryptography following Keygen's proven approach:

#### Account-Level Cryptographic Keys
Each account automatically generates and maintains cryptographic key pairs:

1. **RSA Keys (2048-bit)**:
   - Generated for each account during creation
   - Used for license encryption and digital signatures
   - Private keys encrypted at rest using AES-256-GCM
   
2. **Ed25519 Keys**:
   - Modern elliptic curve cryptography for enhanced security
   - Faster signature generation and verification
   - Smaller key size with equivalent security to RSA-2048
   
3. **Secret Keys**:
   - 128-character hex secret keys for additional operations
   - Used for HMAC generation and symmetric encryption

#### License Cryptographic Schemes
Multiple schemes support different security requirements:

```go
type LicenseScheme string

const (
    LegacyEncrypt         LicenseScheme = "LEGACY_ENCRYPT"      // Backward compatibility
    RSA2048PKCS1Encrypt   LicenseScheme = "RSA_2048_PKCS1_ENCRYPT"   // RSA encryption
    RSA2048PKCS1Sign      LicenseScheme = "RSA_2048_PKCS1_SIGN"      // RSA signatures
    RSA2048PSS            LicenseScheme = "RSA_2048_PKCS1_PSS_SIGN"  // RSA-PSS signing
    RSA2048JWTRS256       LicenseScheme = "RSA_2048_JWT_RS256"       // JWT with RS256
    Ed25519Sign           LicenseScheme = "ED25519_SIGN"             // Ed25519 signatures
    Ed25519JWTES256       LicenseScheme = "ED25519_JWT_ES256"        // JWT with ES256
)
```

#### Data Encryption Strategy

1. **Application-Level Encryption**:
   - AES-256-GCM for sensitive data at rest
   - Deterministic encryption for searchable fields
   - Key derivation using Argon2id with salt

2. **License File Encryption**:
   - Hybrid encryption: AES-256-GCM + RSA/Ed25519
   - License content encrypted with ephemeral AES key
   - AES key encrypted with account's public key

3. **Transport Security**:
   - TLS 1.3 for all HTTP communications
   - Certificate pinning for critical operations
   - HSTS headers for security

#### Cryptographic Operations

1. **License Generation**:
   ```go
   // RSA Encryption flow
   func (l *License) EncryptWithRSA(publicKey *rsa.PublicKey) error
   
   // Ed25519 Signing flow  
   func (l *License) SignWithEd25519(privateKey ed25519.PrivateKey) error
   
   // JWT Generation
   func (l *License) GenerateJWT(signingKey interface{}, alg string) (string, error)
   ```

2. **License Validation**:
   ```go
   // Signature verification
   func (l *License) VerifySignature(publicKey interface{}) error
   
   // Decryption
   func (l *License) Decrypt(privateKey interface{}) error
   
   // JWT validation
   func (l *License) ValidateJWT(verifyingKey interface{}) (*Claims, error)
   ```

#### Key Management
- **Key Rotation**: Automated key rotation with backward compatibility
- **Key Escrow**: Secure key backup for account recovery  
- **HSM Support**: Hardware Security Module integration for enterprise
- **Key Derivation**: PBKDF2/Argon2id for derived keys

#### Security Implementation Details

1. **Constant-Time Operations**: All cryptographic comparisons use constant-time functions
2. **Secure Random Generation**: crypto/rand for all random value generation
3. **Memory Protection**: Secure memory wiping for sensitive data
4. **Side-Channel Protection**: Timing attack prevention in validation

## Implementation Phases

### Phase 1: Enhanced Core Foundation (3-4 weeks)

#### Week 1-2: Schema Enhancement
**Expand Existing Entities:**

1. **Account Entity** - Add enterprise fields:
   ```go
   - Slug string (URL-friendly ID)
   - PlanID uuid (subscription link)  
   - Protected bool (system protection)
   - PublicKey, PrivateKey text (crypto keys)
   - Ed25519PrivateKey, Ed25519PublicKey text (signing)
   - Domain, Subdomain string (custom domains)
   - APIVersion string (versioning)
   - Backend string (infrastructure)
   - SSO fields (organization_id, domains, session_duration, etc.)
   - Slack integration fields
   ```

2. **User Entity** - Add missing fields:
   ```go
   - PasswordResetToken string
   - PasswordResetSentAt *time.Time  
   - BannedAt *time.Time (user banning)
   - GroupID *uuid (user groups)
   - SSO integration fields (profile_id, idp_id, connection_id)
   ```

3. **Product Entity** - Enhance with:
   ```go
   - Code string (unique identifier)
   - URL string (product homepage)
   - DistributionStrategy string (how software is distributed)
   ```

4. **Policy Entity** - Add extensive configuration:
   ```go
   - Floating bool (license transferability)
   - UsePool bool (license pooling)
   - Encrypted bool (license encryption)
   - Concurrent bool (concurrent usage allowed)
   - Scheme LicenseScheme (cryptographic scheme - see above)
   - HeartbeatDuration int (heartbeat interval)
   - FingerprintUniquenessStrategy string
   - FingerprintMatchingStrategy string
   - ExpirationStrategy string (how expiration works)
   - ExpirationBasis string (what triggers expiration)
   - AuthenticationStrategy string
   - HeartbeatCullStrategy string
   - HeartbeatResurrectionStrategy string
   - TransferStrategy string (license transfer rules)
   - LeasingStrategy string (license leasing)
   - OverageStrategy string (overage handling)
   - HeartbeatBasis string
   - MachineUniquenessStrategy string
   - MachineMatchingStrategy string
   - RenewalBasis string
   - MachineLeasingStrategy string
   - ProcessLeasingStrategy string
   - ComponentUniquenessStrategy string
   - ComponentMatchingStrategy string
   - RequireProductScope bool (policy scoping)
   - RequirePolicyScope bool
   - RequireMachineScope bool
   - RequireFingerprintScope bool
   - RequireUserScope bool
   - RequireEnvironmentScope bool
   - RequireChecksumScope bool
   - RequireVersionScope bool
   - RequireComponentsScope bool
   - RequireHeartbeat bool (heartbeat requirement)
   ```

5. **License Entity** - Add tracking fields:
   ```go
   - Suspended bool (suspension state)
   - LastCheckInAt *time.Time (heartbeat tracking)
   - LastExpirationEventSentAt *time.Time
   - LastValidatedAt *time.Time
   - MachinesCount int (cached count)
   - MachinesCoreCount int (CPU core tracking)
   - MaxMachinesOverride *int (policy overrides)
   - MaxCoresOverride *int
   - MaxUsesOverride *int
   - MaxProcessesOverride *int
   - MaxUsersOverride *int
   - LicenseUsersCount int
   - LastValidatedChecksum string (software verification)
   - LastValidatedVersion string
   - GroupID *uuid
   ```

6. **Machine Entity** - Add advanced tracking:
   ```go
   - IP string (IP address tracking)
   - LastHeartbeatAt *time.Time
   - Cores int (CPU core count)
   - LastDeathEventSentAt *time.Time
   - MaxProcessesOverride *int
   - LastCheckOutAt *time.Time
   - HeartbeatJID string (job tracking)
   - OwnerID *uuid (machine ownership)
   - PolicyID uuid (direct policy reference)
   ```

#### Week 2-3: New Critical Entities

7. **Environment Entity** (Multi-tenancy):
   ```go
   type Environment struct {
       ID                string
       AccountID         string
       Name              string
       Code              string // unique per account
       IsolationStrategy string // "global", "shared", "isolated"
   }
   ```

8. **Plan Entity** (Subscription Management):
   ```go
   type Plan struct {
       ID                             string
       Name                           string
       PlanID                         string // external billing system ID
       Price                          int
       MaxUsers, MaxPolicies          int
       MaxLicenses, MaxProducts       int
       MaxAdmins                      int
       TrialDuration                  int
       MaxReqs                        int // API rate limits
       Interval                       string // billing interval
       RequestLogRetentionDuration    int
       EventLogRetentionDuration      int
       MaxStorage, MaxTransfer        int64
       MaxUpload                      int64
       Private                        bool
   }
   ```

9. **Session Entity** (Proper Session Management):
   ```go
   type Session struct {
       ID           string
       AccountID    string
       EnvironmentID *string
       TokenID      *string
       BearerType   string
       BearerID     string
       IP           string
       UserAgent    string
       LastUsedAt   *time.Time
       ExpiresAt    time.Time
   }
   ```

#### Week 3-4: Cryptographic Foundation & RBAC

10. **Cryptographic Services Implementation**:
    ```go
    // Account key management
    type AccountCrypto struct {
        RSAPrivateKey     string // PEM encoded, encrypted at rest
        RSAPublicKey      string // PEM encoded  
        Ed25519PrivateKey string // hex encoded, encrypted at rest
        Ed25519PublicKey  string // hex encoded
        SecretKey         string // 128-char hex, encrypted at rest
    }
    
    // License cryptographic operations
    type LicenseCrypto interface {
        // RSA operations
        EncryptWithRSA(data []byte, publicKey *rsa.PublicKey) ([]byte, error)
        DecryptWithRSA(encData []byte, privateKey *rsa.PrivateKey) ([]byte, error)
        SignWithRSA(data []byte, privateKey *rsa.PrivateKey) ([]byte, error)
        VerifyRSASignature(data, signature []byte, publicKey *rsa.PublicKey) error
        
        // Ed25519 operations  
        SignWithEd25519(data []byte, privateKey ed25519.PrivateKey) ([]byte, error)
        VerifyEd25519Signature(data, signature []byte, publicKey ed25519.PublicKey) error
        
        // JWT operations
        GenerateJWT(claims jwt.Claims, signingKey interface{}, alg string) (string, error)
        ValidateJWT(tokenString string, verifyingKey interface{}) (*jwt.Token, error)
        
        // Hybrid encryption for license files
        EncryptLicenseFile(data []byte, publicKey interface{}) (*EncryptedLicense, error)
        DecryptLicenseFile(encLicense *EncryptedLicense, privateKey interface{}) ([]byte, error)
    }
    
    // Key generation and management
    type KeyManager interface {
        GenerateAccountKeys() (*AccountCrypto, error)
        RotateKeys(accountID string) error
        GetSigningKey(accountID string, scheme LicenseScheme) (interface{}, error)
        GetVerifyingKey(accountID string, scheme LicenseScheme) (interface{}, error)
    }
    ```

11. **Permission & Role Entities**:
    ```go
    type Permission struct {
        ID     string
        Action string // "license.read", "user.create", etc.
    }
    
    type Role struct {
        ID           string
        AccountID    string
        Name         string
        ResourceType *string // polymorphic roles
        ResourceID   *string
    }
    ```

11. **Update all entities** to include `environment_id` for multi-tenancy

12. **Database migrations** for all schema changes

13. **Update repository interfaces** for new entities and fields

### Phase 2: Advanced Features (3-4 weeks)

#### Week 5-6: User Management & Feature Gating

1. **Group Entity** (User Organization):
   ```go
   type Group struct {
       ID           string
       AccountID    string
       EnvironmentID *string
       Name         string
       MaxUsers     *int
       MaxLicenses  *int
       MaxMachines  *int
       Metadata     GroupMetadata `gorm:"type:jsonb"`
   }
   ```

2. **Entitlement Entity** (Feature Gating):
   ```go
   type Entitlement struct {
       ID           string
       AccountID    string
       EnvironmentID *string
       Name         string
       Code         string // feature identifier
       Metadata     EntitlementMetadata `gorm:"type:jsonb"`
   }
   ```

3. **Join Tables**:
   - GroupOwners (user -> group ownership)
   - GroupPermissions (group -> permissions)
   - LicenseUsers (license -> user assignments)
   - LicenseEntitlements (license -> feature entitlements)
   - PolicyEntitlements (policy -> default entitlements)
   - RolePermissions (role -> permissions)
   - TokenPermissions (token -> custom permissions)

#### Week 6-7: Event System & Audit Trail

4. **Event System**:
   ```go
   type EventType struct {
       ID    string
       Event string // "license.created", "user.updated", etc.
   }
   
   type EventLog struct {
       ID             string
       AccountID      string
       EnvironmentID  *string
       EventTypeID    string
       ResourceType   string
       ResourceID     string
       WhodunnitType  *string // polymorphic actor
       WhodunnitID    *string
       RequestLogID   *string
       IdempotencyKey *string
       Metadata       EventMetadata `gorm:"type:jsonb"`
       CreatedDate    time.Time // for partitioning
   }
   ```

5. **Request Logging**:
   ```go
   type RequestLog struct {
       ID              string
       AccountID       *string
       EnvironmentID   *string
       URL             string
       Method          string
       IP              string
       UserAgent       string
       Status          string
       RequestorType   *string
       RequestorID     *string
       RequestBody     *string
       ResponseBody    *string
       RequestHeaders  RequestHeaders `gorm:"type:jsonb"`
       ResponseHeaders ResponseHeaders `gorm:"type:jsonb"`
       RunTime         float64
       QueueTime       float64
       CreatedDate     time.Time
   }
   ```

#### Week 7-8: Enhanced Machine Management

6. **Machine Component Tracking**:
   ```go
   type MachineComponent struct {
       ID           string
       AccountID    string
       EnvironmentID *string
       MachineID    string
       Fingerprint  string
       Name         string
       Metadata     ComponentMetadata `gorm:"type:jsonb"`
   }
   ```

7. **Process-Level Licensing**:
   ```go
   type MachineProcess struct {
       ID                    string
       AccountID             string
       EnvironmentID         *string
       MachineID             string
       PID                   string
       LastHeartbeatAt       time.Time
       LastDeathEventSentAt  *time.Time
       HeartbeatJID          string
       Metadata              ProcessMetadata `gorm:"type:jsonb"`
   }
   ```

### Phase 3: Enterprise Features (2-3 weeks)

#### Week 9-10: Webhooks & Notifications

1. **Webhook System**:
   ```go
   type WebhookEndpoint struct {
       ID                 string
       AccountID          string
       EnvironmentID      *string
       ProductID          *string // product-scoped webhooks
       URL                string
       Subscriptions      []string `gorm:"type:text[]"` // event filters
       SignatureAlgorithm string
       APIVersion         string
   }
   
   type WebhookEvent struct {
       ID                   string
       AccountID            *string
       EnvironmentID        *string
       WebhookEndpointID    string
       EventTypeID          string
       Payload              string
       Status               string
       JID                  string // job ID
       LastResponseCode     *int
       LastResponseBody     *string
       IdempotencyToken     string
       APIVersion           string
   }
   ```

2. **Metrics Collection**:
   ```go
   type Metric struct {
       ID           string
       AccountID    *string
       EventTypeID  string
       Data         MetricData `gorm:"type:jsonb"`
       CreatedDate  time.Time // for partitioning
   }
   ```

#### Week 10-11: Security & Billing

3. **Two-Factor Authentication**:
   ```go
   type SecondFactor struct {
       ID             string
       AccountID      string
       EnvironmentID  *string
       UserID         string
       Secret         string // encrypted TOTP secret
       Enabled        bool
       LastVerifiedAt *time.Time
   }
   ```

4. **Billing Integration**:
   ```go
   type Billing struct {
       ID                      string
       AccountID               string
       CustomerID              string // external billing system
       SubscriptionID          string
       SubscriptionStatus      string
       SubscriptionPeriodStart *time.Time
       SubscriptionPeriodEnd   *time.Time
       CardBrand               string
       CardLast4               string
       CardExpiryDate          *time.Time
       CardAddedAt             *time.Time
       State                   string
       ReferralID              *string
   }
   
   type Receipt struct {
       ID        string
       BillingID string
       InvoiceID string
       Amount    int
       Paid      bool
   }
   ```

## Infrastructure & Configuration

### Database Design
- **Partitioning**: EventLogs and Metrics by date
- **Indexing**: Comprehensive indexing strategy for performance
- **Constraints**: Foreign keys, unique constraints, check constraints
- **JSONB**: Extensive use for flexible metadata storage

### API Architecture
- **Request Pipeline**: Account Scoping → Subscription Check → Authentication → Authorization → Validation → Business Logic → Event Broadcasting
- **Rate Limiting**: Per-account, per-endpoint rate limiting
- **API Versioning**: Support for multiple API versions
- **JSON:API**: Standard response format

### Event-Driven Architecture
- **Event Broadcasting**: All mutations trigger events
- **Webhook Delivery**: Reliable delivery with retry logic
- **Background Processing**: NATS for event streaming
- **Audit Trail**: Complete event logging

### Security
- **Multi-tenancy**: Environment-based isolation
- **RBAC**: Hierarchical permission system
- **API Authentication**: Multiple methods (Token, License Key, Session, Password)
- **Rate Limiting**: Rack::Attack-style protection
- **Encryption**: Ed25519 for signatures, configurable license encryption

### Performance & Scalability
- **Caching**: Valkey for license validation, session storage
- **Connection Pooling**: Database connection management
- **Background Jobs**: Priority queue processing
- **VictoriaMetrics**: Time-series metrics storage

## Development Milestones

### Phase 1 Deliverables
- [ ] Enhanced entity models with all production fields
- [ ] Environment system implementation
- [ ] Basic RBAC with permissions and roles
- [ ] Session management system
- [ ] Updated database migrations
- [ ] Repository layer updates

### Phase 2 Deliverables  
- [ ] Groups and entitlements system
- [ ] Complete event logging and audit trail
- [ ] Request logging infrastructure
- [ ] Enhanced machine tracking (components, processes)
- [ ] Join table implementations

### Phase 3 Deliverables
- [ ] Webhook system with reliable delivery
- [ ] Metrics collection and VictoriaMetrics integration
- [ ] Two-factor authentication
- [ ] Billing system integration
- [ ] Performance optimization

## Success Criteria
1. **Functional**: All core license management operations working
2. **Performance**: License validation < 50ms p95
3. **Security**: Enterprise-grade authentication and authorization
4. **Scalability**: Handle 10k+ concurrent license validations
5. **Observability**: Complete audit trail and metrics
6. **Reliability**: 99.9% uptime SLA capability

## Risk Mitigation
1. **Complexity**: Incremental development with working software at each phase
2. **Performance**: Early performance testing and optimization
3. **Data Migration**: Careful schema migration planning
4. **Security**: Security review at each phase
5. **Testing**: Comprehensive test coverage for critical paths

## Team Requirements
- **Backend Engineers**: 3-4 developers
- **DevOps**: 1 engineer for infrastructure
- **QA**: 1 engineer for testing
- **Timeline**: 8-11 weeks total

This plan provides a comprehensive roadmap for building an enterprise-grade license management platform that matches production requirements while maintaining development velocity and code quality.