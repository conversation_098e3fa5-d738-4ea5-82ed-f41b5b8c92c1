package events

import (
	"context"
	"fmt"
	"time"

)

// EventType represents types of events that can be broadcast
type EventType string

const (
	// License events
	EventLicenseCreated             EventType = "license.created"
	EventLicenseUpdated             EventType = "license.updated"
	EventLicenseDeleted             EventType = "license.deleted"
	EventLicenseValidationSucceeded EventType = "license.validation.succeeded"
	EventLicenseValidationFailed    EventType = "license.validation.failed"
	EventLicenseCheckedOut          EventType = "license.checked-out"
	EventLicenseCheckedIn           EventType = "license.check-in"
	EventLicenseRevoked             EventType = "license.revoked"
	EventLicenseSuspended           EventType = "license.suspended"
	EventLicenseReinstated          EventType = "license.reinstated"
	EventLicenseRenewed             EventType = "license.renewed"
	EventLicenseUsageIncremented    EventType = "license.usage.incremented"
	EventLicenseUsageDecremented    EventType = "license.usage.decremented"
	EventLicenseUsageReset          EventType = "license.usage.reset"

	// Machine events
	EventMachineCreated              EventType = "machine.created"
	EventMachineUpdated              EventType = "machine.updated"
	EventMachineDeleted              EventType = "machine.deleted"
	EventMachineHeartbeatPing        EventType = "machine.heartbeat.ping"
	EventMachineHeartbeatReset       EventType = "machine.heartbeat.reset"
	EventMachineHeartbeatResurrected EventType = "machine.heartbeat.resurrected"
	EventMachineCheckedOut           EventType = "machine.checked-out"

	// User events
	EventUserCreated       EventType = "user.created"
	EventUserUpdated       EventType = "user.updated"
	EventUserDeleted       EventType = "user.deleted"
	EventUserPasswordReset EventType = "user.password-reset"
	EventUserBanned        EventType = "user.banned"
	EventUserUnbanned      EventType = "user.unbanned"

	// Product events
	EventProductCreated EventType = "product.created"
	EventProductUpdated EventType = "product.updated"
	EventProductDeleted EventType = "product.deleted"

	// Policy events
	EventPolicyCreated EventType = "policy.created"
	EventPolicyUpdated EventType = "policy.updated"
	EventPolicyDeleted EventType = "policy.deleted"

	// Organization events
	EventOrganizationCreated EventType = "organization.created"
	EventOrganizationUpdated EventType = "organization.updated"
	EventOrganizationDeleted EventType = "organization.deleted"

)

// EventMeta represents metadata for an event
type EventMeta map[string]interface{}

// EventResource represents a resource that triggered an event
type EventResource interface {
	GetID() string
	GetType() string
}

// Organization represents a simplified organization for event context
type Organization struct {
	ID string
}

// BroadcastService handles event broadcasting with simple logging
type BroadcastService struct {
	// Simplified service for logging events only
}

// NewBroadcastService creates a new event broadcast service
func NewBroadcastService() *BroadcastService {
	return &BroadcastService{}
}

// BroadcastEvent logs an event for the core entities
func (bs *BroadcastService) BroadcastEvent(
	ctx context.Context,
	event EventType,
	organization *Organization,
	resource EventResource,
	meta EventMeta,
) error {
	// Simple event logging for core entities only
	fmt.Printf("[EVENT] %s | Organization: %s | Resource: %s (%s) | Time: %s\n",
		event,
		organization.ID,
		resource.GetType(),
		resource.GetID(),
		time.Now().UTC().Format(time.RFC3339),
	)

	// Log metadata if present
	if len(meta) > 0 {
		fmt.Printf("[EVENT_META] %s | Meta: %+v\n", event, meta)
	}

	return nil
}


// Helper function to create a simple event resource
type SimpleEventResource struct {
	ID   string
	Type string
}

func (r SimpleEventResource) GetID() string   { return r.ID }
func (r SimpleEventResource) GetType() string { return r.Type }

// CoreEntity represents a simplified entity with ID for event resources
type CoreEntity interface {
	GetEntityID() string
}

// MakeEventResource creates an event resource from a core entity
func MakeEventResource(entityType string, entityID string) EventResource {
	return SimpleEventResource{ID: entityID, Type: entityType}
}

// MakeEventResourceFromEntity creates an event resource from an entity with ID
func MakeEventResourceFromEntity(entity CoreEntity, entityType string) EventResource {
	return SimpleEventResource{ID: entity.GetEntityID(), Type: entityType}
}
