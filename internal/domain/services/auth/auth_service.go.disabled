package auth

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
)

// AuthService provides streamlined authentication and token management  
type AuthService struct {
	userRepo      repositories.UserRepository
	sessionRepo   repositories.SessionRepository
	apiTokenRepo  repositories.APITokenRepository
	cryptoService *crypto.CryptoService
	jwtPrivateKey string
	jwtPublicKey  string
}

// NewAuthService creates a new clean auth service
func NewAuthService(
	userRepo repositories.UserRepository,
	sessionRepo repositories.SessionRepository,
	apiTokenRepo repositories.APITokenRepository,
	cryptoService *crypto.CryptoService,
	jwtPrivateKey, jwtPublicKey string,
) *AuthService {
	return &AuthService{
		userRepo:      userRepo,
		sessionRepo:   sessionRepo,
		apiTokenRepo:  apiTokenRepo,
		cryptoService: cryptoService,
		jwtPrivateKey: jwtPrivateKey,
		jwtPublicKey:  jwtPublicKey,
	}
}

// ==================== JWT TOKEN MANAGEMENT ====================

// GenerateJWT creates a JWT token with minimal claims
func (s *AuthService) GenerateJWT(userID, sessionID, orgID string) (string, error) {
	claims := map[string]interface{}{
		"user_id":    userID,
		"session_id": sessionID,
	}
	
	// Add org_id only if provided
	if orgID != "" {
		claims["org_id"] = orgID
	}

	tokenOpts := crypto.TokenOptions{
		Subject:      userID,
		Issuer:       "gokeys",
		ExpiresIn:    15 * time.Minute, // Short-lived access token
		CustomClaims: claims,
	}

	return s.cryptoService.CreateJWTToken(crypto.SchemeJWTRS256, s.jwtPrivateKey, tokenOpts)
}

// GenerateRefreshToken creates a long-lived refresh token
func (s *AuthService) GenerateRefreshToken(userID, sessionID string) (string, error) {
	tokenOpts := crypto.TokenOptions{
		Subject:   userID,
		Issuer:    "gokeys",
		ExpiresIn: 7 * 24 * time.Hour, // 7 days
		CustomClaims: map[string]interface{}{
			"user_id":    userID,
			"session_id": sessionID,
			"token_type": "refresh",
		},
	}

	return s.cryptoService.CreateJWTToken(crypto.SchemeJWTRS256, s.jwtPrivateKey, tokenOpts)
}

// VerifyJWT verifies a JWT token and returns claims
func (s *AuthService) VerifyJWT(token string) (map[string]interface{}, error) {
	return s.cryptoService.VerifyJWTToken(token, s.jwtPublicKey)
}

// RefreshAccessToken creates new access token from valid refresh token
func (s *AuthService) RefreshAccessToken(ctx context.Context, refreshToken string) (string, error) {
	// Verify refresh token
	claims, err := s.VerifyJWT(refreshToken)
	if err != nil {
		return "", fmt.Errorf("invalid refresh token: %w", err)
	}

	// Check if it's actually a refresh token
	if tokenType, ok := claims["token_type"].(string); !ok || tokenType != "refresh" {
		return "", fmt.Errorf("not a refresh token")
	}

	// Extract user info
	userID, ok := claims["user_id"].(string)
	if !ok {
		return "", fmt.Errorf("invalid user_id in token")
	}

	sessionID, _ := claims["session_id"].(string)

	// Verify user still exists and is active
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return "", fmt.Errorf("invalid user ID format: %w", err)
	}

	user, err := s.userRepo.GetByID(ctx, userUUID)
	if err != nil {
		return "", fmt.Errorf("user not found: %w", err)
	}

	if !user.IsActive() || user.IsBanned() {
		return "", fmt.Errorf("user account is inactive or banned")
	}

	// Get organization context if session exists
	var orgID string
	if sessionID != "" {
		if sessionUUID, err := uuid.Parse(sessionID); err == nil {
			if session, err := s.sessionRepo.GetByID(ctx, sessionUUID); err == nil && !session.IsExpired() {
				// TODO: Get organization from session or user default
				// orgID = session.OrganizationID or user.DefaultOrganizationID
			}
		}
	}

	// Generate new access token
	return s.GenerateJWT(userID, sessionID, orgID)
}

// ==================== API TOKEN MANAGEMENT ====================

// CreateAPIToken creates a new API token
func (s *AuthService) CreateAPIToken(ctx context.Context, req *CreateAPITokenRequest) (*entities.APIToken, string, error) {
	// Validate request
	if err := s.validateAPITokenRequest(req); err != nil {
		return nil, "", fmt.Errorf("invalid request: %w", err)
	}

	// Create API token entity
	apiToken := &entities.APIToken{
		UserID:         &req.UserID,
		OrganizationID: req.OrganizationID,
		Name:           req.Name,
		Description:    req.Description,
		Scopes:         req.Scopes,
		Active:         true,
		ExpiresAt:      req.ExpiresAt,
		CreatedByIP:    req.CreatedByIP,
		UserAgent:      req.UserAgent,
	}

	// Generate secure token string
	tokenString, err := s.generateSecureTokenString()
	if err != nil {
		return nil, "", fmt.Errorf("failed to generate token: %w", err)
	}

	// Hash token for storage
	apiToken.TokenHash = s.hashToken(tokenString)

	// Save to database
	if err := s.apiTokenRepo.Create(ctx, apiToken); err != nil {
		return nil, "", fmt.Errorf("failed to save API token: %w", err)
	}

	return apiToken, tokenString, nil
}

// ValidateAPIToken validates an API token and returns claims
func (s *AuthService) ValidateAPIToken(token string) (map[string]interface{}, error) {
	// Hash token to find in database
	tokenHash := s.hashToken(token)

	// Find token by hash
	apiToken, err := s.apiTokenRepo.GetByTokenHash(context.Background(), tokenHash)
	if err != nil {
		return nil, fmt.Errorf("token not found or invalid")
	}

	// Check if token is active and not expired
	if !apiToken.IsActive() {
		return nil, fmt.Errorf("token is inactive or expired")
	}

	// Update last used (fire and forget)
	go func() {
		apiToken.UpdateLastUsed("")
		s.apiTokenRepo.Update(context.Background(), apiToken)
	}()

	// Build claims for authorization middleware
	claims := map[string]interface{}{
		"sub":        apiToken.UserID.String(),
		"user_id":    apiToken.UserID.String(),
		"token_type": "api_token",
		"scopes":     apiToken.Scopes,
	}

	// Add organization context if available
	if apiToken.OrganizationID != nil {
		claims["org_id"] = apiToken.OrganizationID.String()
	}

	// Convert scopes to permissions (simplified)
	permissions := make([]string, 0, len(apiToken.Scopes))
	for _, scope := range apiToken.Scopes {
		// Build permission key format: scope:resource_type:action
		for _, action := range scope.Actions {
			permKey := fmt.Sprintf("%s:%s:%s", scope.Scope, scope.ResourceType, action)
			permissions = append(permissions, permKey)
		}
	}
	claims["permissions"] = permissions

	return claims, nil
}

// RevokeAPIToken revokes an API token
func (s *AuthService) RevokeAPIToken(ctx context.Context, tokenID uuid.UUID) error {
	apiToken, err := s.apiTokenRepo.GetByID(ctx, tokenID)
	if err != nil {
		return fmt.Errorf("token not found: %w", err)
	}

	apiToken.Active = false
	return s.apiTokenRepo.Update(ctx, apiToken)
}

// ==================== HELPER METHODS ====================

// generateSecureTokenString generates a secure random token string
func (s *AuthService) generateSecureTokenString() (string, error) {
	// Generate secure random bytes
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Encode with prefix for identification
	return fmt.Sprintf("ldr_%s", hex.EncodeToString(tokenBytes)), nil
}

// hashToken creates SHA-256 hash of token for database storage
func (s *AuthService) hashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// validateAPITokenRequest validates API token creation request
func (s *AuthService) validateAPITokenRequest(req *CreateAPITokenRequest) error {
	if req.UserID == uuid.Nil {
		return fmt.Errorf("user ID is required")
	}
	if req.Name == "" {
		return fmt.Errorf("token name is required")
	}
	if len(req.Scopes) == 0 {
		return fmt.Errorf("at least one scope is required")
	}
	return nil
}

// ==================== REQUEST TYPES ====================

// CreateAPITokenRequest represents a request to create an API token
type CreateAPITokenRequest struct {
	UserID         uuid.UUID                `json:"user_id"`
	OrganizationID *uuid.UUID               `json:"organization_id,omitempty"`
	Name           string                   `json:"name"`
	Description    string                   `json:"description,omitempty"`
	Scopes         []entities.APITokenScope `json:"scopes"`
	ExpiresAt      *time.Time               `json:"expires_at,omitempty"`
	CreatedByIP    string                   `json:"created_by_ip,omitempty"`
	UserAgent      string                   `json:"user_agent,omitempty"`
}