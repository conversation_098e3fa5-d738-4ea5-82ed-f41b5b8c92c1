package services

import (
	"context"

	cacheadapter "github.com/gokeys/gokeys/internal/adapters/cache"
	"github.com/gokeys/gokeys/internal/adapters/database/postgres/repositories"
	"github.com/gokeys/gokeys/internal/config"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/gokeys/gokeys/internal/domain/services/events"
	"github.com/gokeys/gokeys/internal/domain/services/license"
	"github.com/gokeys/gokeys/internal/ports/cache"
	"github.com/rs/zerolog"
	"gorm.io/gorm"
)

// ServiceCoordinator manages all domain services
type ServiceCoordinator struct {
	// Core services
	Auth              interface{} // AuthService temporarily disabled
	Crypto            *crypto.CryptoService
	LicenseValidation *license.ValidationService
	LicenseCheckout   *license.CheckoutService
	LicenseLookup     *license.KeyLookupService
	Events            *events.BroadcastService
	OPA               interface{} // OPA disabled for now

	// Repository factory
	Repositories *repositories.RepositoryFactory

	// Cache
	Cache     cache.Cache
	cacheType string

	// Configuration
	config *config.Config
}

// NewServiceCoordinator creates a new service coordinator
func NewServiceCoordinator(db *gorm.DB, cfg *config.Config, logger zerolog.Logger) *ServiceCoordinator {
	// Initialize cache based on environment
	var cacheInstance cache.Cache
	var cacheType string

	if cfg.Server.Environment == "development" || cfg.Server.Environment == "test" {
		// Use memory cache for development
		cacheInstance = cacheadapter.NewMemoryCacheAdapter()
		cacheType = "memory"
		logger.Info().Msg("Using memory cache for development")
	} else {
		// Use Valkey for production
		valkeyConfig := cacheadapter.ValkeyConfig{
			Host:     "localhost", // Parse from cfg.Redis.Address if needed
			Port:     6379,        // Parse from cfg.Redis.Address if needed
			Password: cfg.Redis.Password,
			DB:       cfg.Redis.DB,
		}

		var err error
		cacheInstance, err = cacheadapter.NewValkeyCacheAdapter(valkeyConfig)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to initialize Valkey cache, falling back to memory cache")
			cacheInstance = cacheadapter.NewMemoryCacheAdapter()
			cacheType = "memory_fallback"
		} else {
			cacheType = "valkey"
			logger.Info().Msg("Using Valkey cache for production")
		}
	}

	// Initialize repository factory
	repoFactory := repositories.NewRepositoryFactory(db)

	// Initialize services
	cryptoService := crypto.NewCryptoService()
	eventService := events.NewBroadcastService()

	// Initialize license services
	licenseValidationService := license.NewValidationService(
		repoFactory.License(),
		repoFactory.Machine(),
		repoFactory.User(),
		logger,
	)

	// Note: CheckoutService needs specific options, temporarily disabled

	licenseLookupService := license.NewKeyLookupService(
		repoFactory.License(),
		cryptoService,
		logger,
	)

	return &ServiceCoordinator{
		Auth:              nil, // Disabled for now
		Crypto:            cryptoService,
		LicenseValidation: licenseValidationService,
		LicenseCheckout:   nil, // Temporarily disabled
		LicenseLookup:     licenseLookupService,
		Events:            eventService,
		OPA:               nil, // Disabled for now
		Repositories:      repoFactory,
		Cache:             cacheInstance,
		cacheType:         cacheType,
		config:            cfg,
	}
}

// Health returns the health status of all services
func (sc *ServiceCoordinator) Health(ctx context.Context) map[string]interface{} {
	health := make(map[string]interface{})

	// Database health
	if sc.Repositories != nil {
		health["database"] = "healthy"
	} else {
		health["database"] = "unhealthy"
	}

	// Cache health
	health["cache"] = map[string]interface{}{
		"type":   sc.cacheType,
		"status": "healthy",
	}

	// Services health
	health["services"] = map[string]interface{}{
		"crypto":             "healthy",
		"license_validation": "healthy",
		"license_checkout":   "healthy",
		"license_lookup":     "healthy",
		"events":             "healthy",
		"permission":         "healthy",
	}

	return health
}

// GetCryptoService returns the crypto service
func (sc *ServiceCoordinator) GetCryptoService() *crypto.CryptoService {
	return sc.Crypto
}

// Close gracefully shuts down all services
func (sc *ServiceCoordinator) Close(ctx context.Context) error {
	// Add cleanup logic if needed
	return nil
}
