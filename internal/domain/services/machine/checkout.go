﻿package machine

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services/checkout"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

// MachineData represents the clean Go structure for machine data in certificates
type MachineData struct {
	ID                string                 `json:"id"`
	Fingerprint       string                 `json:"fingerprint"`
	Name              *string                `json:"name,omitempty"`
	Hostname          *string                `json:"hostname,omitempty"`
	Platform          *string                `json:"platform,omitempty"`
	IP                *string                `json:"ip,omitempty"`
	Cores             *int                   `json:"cores,omitempty"`
	RequireHeartbeat  bool                   `json:"require_heartbeat"`
	HeartbeatStatus   string                 `json:"heartbeat_status"`
	HeartbeatDuration *int                   `json:"heartbeat_duration,omitempty"`
	MaxProcesses      *int                   `json:"max_processes,omitempty"`
	LastCheckOutAt    *time.Time             `json:"last_check_out,omitempty"`
	LastHeartbeatAt   *time.Time             `json:"last_heartbeat,omitempty"`
	NextHeartbeatAt   *time.Time             `json:"next_heartbeat,omitempty"`
	Metadata          map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt         time.Time              `json:"created"`
	UpdatedAt         time.Time              `json:"updated"`

	// Included relationships (only when requested)
	License    *LicenseData    `json:"license,omitempty"`
	Components []ComponentData `json:"components,omitempty"`
	Owner      *OwnerData      `json:"owner,omitempty"`
}

// LicenseData represents clean license information for machine certificates
type LicenseData struct {
	ID        string                 `json:"id"`
	Key       string                 `json:"key"`
	Name      *string                `json:"name,omitempty"`
	Status    string                 `json:"status"`
	Uses      int                    `json:"uses"`
	Suspended bool                   `json:"suspended"`
	ExpiresAt *time.Time             `json:"expiry,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt time.Time              `json:"created"`
	UpdatedAt time.Time              `json:"updated"`

	// Nested relationships
	Product      *ProductData      `json:"product,omitempty"`
	Policy       *PolicyData       `json:"policy,omitempty"`
	Owner        *OwnerData        `json:"owner,omitempty"`
	Users        []UserData        `json:"users,omitempty"`
	Entitlements []EntitlementData `json:"entitlements,omitempty"`
}

// ProductData represents clean product information for certificates
type ProductData struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	URL       *string                `json:"url,omitempty"`
	Platforms []string               `json:"platforms,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt time.Time              `json:"created"`
	UpdatedAt time.Time              `json:"updated"`
}

// PolicyData represents clean policy information for certificates
type PolicyData struct {
	ID               string                 `json:"id"`
	Name             string                 `json:"name"`
	Duration         *int64                 `json:"duration,omitempty"`
	Strict           bool                   `json:"strict"`
	Floating         bool                   `json:"floating"`
	Scheme           string                 `json:"scheme"`
	Encrypted        bool                   `json:"encrypted"`
	Protected        bool                   `json:"protected"`
	RequireHeartbeat bool                   `json:"require_heartbeat"`
	MaxMachines      *int                   `json:"max_machines,omitempty"`
	MaxProcesses     *int                   `json:"max_processes,omitempty"`
	MaxUsers         *int                   `json:"max_users,omitempty"`
	MaxCores         *int                   `json:"max_cores,omitempty"`
	MaxUses          *int                   `json:"max_uses,omitempty"`
	Metadata         map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt        time.Time              `json:"created"`
	UpdatedAt        time.Time              `json:"updated"`
}

// ComponentData represents machine component information
type ComponentData struct {
	Name        string                 `json:"name"`
	Fingerprint string                 `json:"fingerprint"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// OwnerData represents owner information
type OwnerData struct {
	ID        string                 `json:"id"`
	Email     string                 `json:"email"`
	FirstName *string                `json:"first_name,omitempty"`
	LastName  *string                `json:"last_name,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt time.Time              `json:"created"`
	UpdatedAt time.Time              `json:"updated"`
}

// UserData represents user information
type UserData struct {
	ID        string                 `json:"id"`
	Email     string                 `json:"email"`
	FirstName *string                `json:"first_name,omitempty"`
	LastName  *string                `json:"last_name,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt time.Time              `json:"created"`
	UpdatedAt time.Time              `json:"updated"`
}

// EntitlementData represents entitlement information
type EntitlementData struct {
	ID        string                 `json:"id"`
	Code      string                 `json:"code"`
	Name      string                 `json:"name"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt time.Time              `json:"created"`
	UpdatedAt time.Time              `json:"updated"`
}

// CheckoutService handles machine checkout operations (Ruby: MachineCheckoutService)
// Generates cryptographically signed machine certificates for client applications
// Maps directly from Ruby MachineCheckoutService with Go improvements and type safety
type CheckoutService struct {
	abstractService *checkout.AbstractService
	cryptoService   *crypto.CryptoService
	machine         *entities.Machine
	license         *entities.License
	logger          zerolog.Logger
}

// CheckoutOptions represents options for machine checkout (Ruby: initialize parameters)
type CheckoutOptions struct {
	Machine      *entities.Machine      `json:"machine"`             // Machine to checkout
	Organization *entities.Organization `json:"organization"`        // Ruby: account (renamed)
	Encrypt      bool                   `json:"encrypt"`             // Whether to encrypt the certificate
	Sign         interface{}            `json:"sign"`                // Signing configuration (bool or string algorithm)
	Algorithm    *string                `json:"algorithm,omitempty"` // Explicit algorithm override
	TTL          *time.Duration         `json:"ttl,omitempty"`       // Time-to-live for the certificate
	Include      []string               `json:"include,omitempty"`   // Relationships to include in certificate
}

// Allowed includes for machine checkout (Ruby: ALLOWED_INCLUDES)
// Removed "environment" and "group" fields per requirements
var AllowedIncludes = []string{
	"license.entitlements",
	"license.product",
	"license.policy",
	"license.owner",
	"license.users",
	"license",
	"components",
	"owner",
}

// Custom errors (Ruby: custom error classes)
type InvalidIncludeError struct {
	Includes []string
}

func (e *InvalidIncludeError) Error() string {
	return fmt.Sprintf("invalid includes: %v", e.Includes)
}

type InvalidMachineError struct {
	Message string
}

func (e *InvalidMachineError) Error() string {
	return e.Message
}

type InvalidLicenseError struct {
	Message string
}

func (e *InvalidLicenseError) Error() string {
	return e.Message
}

// NewCheckoutService creates a new machine checkout service (Ruby: initialize)
func NewCheckoutService(cryptoService *crypto.CryptoService, options CheckoutOptions, logger zerolog.Logger) (*CheckoutService, error) {
	// Validate required parameters (Ruby: validation logic)
	if options.Machine == nil {
		return nil, &InvalidMachineError{Message: "machine must be present"}
	}

	if options.Machine.License.ID == uuid.Nil {
		return nil, &InvalidLicenseError{Message: "license must be present"}
	}

	if options.Organization == nil {
		return nil, &InvalidMachineError{Message: "organization must be present"}
	}

	// Validate includes (Ruby: include validation)
	if err := validateIncludes(options.Include); err != nil {
		return nil, err
	}

	// Determine signing algorithm based on license scheme (Ruby: sign algorithm logic)
	sign := options.Sign
	if sign == nil {
		sign = determineSigningAlgorithm(&options.Machine.License)
	}

	// Create abstract service with checkout options (Ruby: super call)
	abstractOptions := checkout.Options{
		Organization: options.Organization,
		Encrypt:      options.Encrypt,
		Sign:         sign,
		Algorithm:    options.Algorithm,
		TTL:          options.TTL,
		Include:      options.Include,
	}

	abstractService, err := checkout.NewAbstractService(cryptoService, abstractOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create abstract service: %w", err)
	}

	return &CheckoutService{
		abstractService: abstractService,
		cryptoService:   cryptoService,
		machine:         options.Machine,
		license:         &options.Machine.License,
		logger:          logger.With().Str("service", "machine_checkout").Logger(),
	}, nil
}

// validateIncludes validates the include parameter (Ruby: include validation)
func validateIncludes(includes []string) error {
	var invalid []string
	for _, include := range includes {
		if !contains(AllowedIncludes, include) {
			invalid = append(invalid, include)
		}
	}

	if len(invalid) > 0 {
		return &InvalidIncludeError{Includes: invalid}
	}

	return nil
}

// contains checks if a slice contains a string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// Checkout generates a machine certificate file (Ruby: call method)
func (s *CheckoutService) Checkout(ctx context.Context) (*entities.MachineFile, error) {
	s.logger.Info().
		Str("machine_id", s.machine.ID.String()).
		Str("license_id", s.license.ID.String()).
		Str("algorithm", s.abstractService.GetAlgorithm()).
		Msg("Starting machine checkout")

	// Calculate issue and expiry times (Ruby: issued_at and expires_at logic)
	issuedAt := time.Now()
	var expiresAt *time.Time

	if s.abstractService.HasTTL() {
		ttl := s.abstractService.GetTTL()
		expiry := issuedAt.Add(*ttl)
		expiresAt = &expiry
	}

	// Create metadata with timestamps and TTL (Ruby: meta hash)
	meta := map[string]interface{}{
		"issued": issuedAt,
		"ttl":    s.abstractService.GetTTL(),
	}
	if expiresAt != nil {
		meta["expiry"] = *expiresAt
	}

	// Filter includes to allowed ones (Ruby: incl = includes & ALLOWED_INCLUDES)
	includes := s.abstractService.GetIncludes()
	filteredIncludes := filterIncludes(includes, AllowedIncludes)

	// Render machine data with includes (Ruby: renderer.render)
	machineData, err := s.renderMachineData(filteredIncludes, meta)
	if err != nil {
		return nil, fmt.Errorf("failed to render machine data: %w", err)
	}

	// Convert to JSON (Ruby: .to_json)
	jsonData, err := json.Marshal(machineData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal machine data: %w", err)
	}

	// Encrypt or encode the data (Ruby: enc = if encrypted? encrypt(...) else encode(...))
	var encData string
	if s.abstractService.IsEncrypted() {
		// Generate secret from license key + machine fingerprint (Ruby: secret: license.key + machine.fingerprint)
		secret := s.license.Key + s.machine.Fingerprint
		encData, err = s.abstractService.Encrypt(string(jsonData), secret)
		if err != nil {
			return nil, fmt.Errorf("failed to encrypt machine data: %w", err)
		}
	} else {
		encData = s.abstractService.Encode(string(jsonData), true) // strict = true
	}

	// Sign the encrypted/encoded data (Ruby: sig = sign(enc, prefix: 'machine'))
	signature, err := s.abstractService.Sign(encData, "machine")
	if err != nil {
		return nil, fmt.Errorf("failed to sign machine data: %w", err)
	}

	// Get algorithm (Ruby: alg = algorithm)
	algorithm := s.abstractService.GetAlgorithm()

	// Create document structure (Ruby: doc = { enc: enc, sig: sig, alg: alg })
	doc := map[string]interface{}{
		"enc": encData,
		"sig": signature,
		"alg": algorithm,
	}

	// Encode document to JSON and then base64 (Ruby: enc = encode(doc.to_json))
	docJSON, err := json.Marshal(doc)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal document: %w", err)
	}

	finalEncoded := s.abstractService.Encode(string(docJSON), false) // strict = false for final encoding

	// Create certificate with proper formatting (Ruby: cert = <<~TXT)
	certificate := fmt.Sprintf("-----BEGIN MACHINE FILE-----\n%s\n-----END MACHINE FILE-----\n", finalEncoded)

	// Create MachineFile entity (Ruby: MachineFile.new)
	machineFile := &entities.MachineFile{
		ID:             uuid.New(),
		OrganizationID: s.license.OrganizationID, // Ruby: account_id -> organization_id
		LicenseID:      s.license.ID,
		MachineID:      s.machine.ID,
		Certificate:    certificate,
		IssuedAt:       issuedAt,
		ExpiresAt:      expiresAt,
		TTL:            s.abstractService.GetTTL(),
		Includes:       filteredIncludes,
		Algorithm:      algorithm,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	s.logger.Info().
		Str("machine_file_id", machineFile.ID.String()).
		Str("algorithm", algorithm).
		Bool("encrypted", s.abstractService.IsEncrypted()).
		Msg("Machine checkout completed successfully")

	return machineFile, nil
}

// filterIncludes filters includes to only allowed ones (Ruby: includes & ALLOWED_INCLUDES)
func filterIncludes(includes, allowed []string) []string {
	var filtered []string
	for _, include := range includes {
		if contains(allowed, include) {
			filtered = append(filtered, include)
		}
	}
	return filtered
}

// renderMachineData renders machine data with includes and metadata (Ruby: renderer.render)
func (s *CheckoutService) renderMachineData(includes []string, meta map[string]interface{}) (map[string]interface{}, error) {
	// Base machine data (Ruby: machine serialization)
	machineData := map[string]interface{}{
		"id":          s.machine.ID.String(),
		"fingerprint": s.machine.Fingerprint,
		"created":     s.machine.CreatedAt,
	}

	// Add optional fields if present
	if s.machine.Name != nil {
		machineData["name"] = *s.machine.Name
	}
	if s.machine.Hostname != nil {
		machineData["hostname"] = *s.machine.Hostname
	}
	if s.machine.Platform != nil {
		machineData["platform"] = *s.machine.Platform
	}
	if s.machine.IP != nil {
		machineData["ip"] = *s.machine.IP
	}
	if s.machine.Cores != nil {
		machineData["cores"] = *s.machine.Cores
	}

	// Add metadata if present
	if len(s.machine.Metadata) > 0 {
		machineData["metadata"] = s.machine.Metadata
	}

	// Add included relationships (Ruby: include logic)
	for _, include := range includes {
		switch include {
		case "license":
			if s.license != nil {
				machineData["license"] = map[string]interface{}{
					"id":  s.license.ID.String(),
					"key": s.license.Key,
				}
			}
		case "policy":
			if s.license != nil && s.license.Policy.ID != uuid.Nil {
				machineData["policy"] = map[string]interface{}{
					"id":   s.license.Policy.ID.String(),
					"name": s.license.Policy.Name,
				}
			}
		case "organization":
			if s.license != nil {
				machineData["organization"] = map[string]interface{}{
					"id": s.license.OrganizationID.String(),
				}
			}
		}
	}

	// Add metadata from parameters (Ruby: meta hash)
	for key, value := range meta {
		machineData[key] = value
	}

	return machineData, nil
}

// determineSigningAlgorithm determines the signing algorithm based on license scheme (Ruby: sign algorithm logic)
func determineSigningAlgorithm(license *entities.License) string {
	if license.Policy.Scheme == nil {
		return "ed25519"
	}

	scheme := *license.Policy.Scheme
	switch scheme {
	case entities.CryptoSchemeRSA2048PSS:
		return "rsa-pss-sha256"
	case entities.CryptoSchemeRSA2048Sign, entities.CryptoSchemeRSA2048JWT:
		return "rsa-sha256"
	case entities.CryptoSchemeED25519:
		return "ed25519"
	default:
		return "ed25519"
	}
}
