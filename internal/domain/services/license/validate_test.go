package license

import (
	"context"
	"testing"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock repositories for testing
type MockLicenseRepository struct {
	mock.Mock
}

func (m *MockLicenseRepository) Create(ctx context.Context, entity *entities.License) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockLicenseRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.License, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) Update(ctx context.Context, entity *entities.License) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockLicenseRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLicenseRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLicenseRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.License, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.License), args.Get(1).(int64), args.Error(2)
}

func (m *MockLicenseRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockLicenseRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockLicenseRepository) GetByKey(ctx context.Context, key string) (*entities.License, error) {
	args := m.Called(ctx, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) GetByPolicy(ctx context.Context, policyID uuid.UUID) ([]*entities.License, error) {
	args := m.Called(ctx, policyID)
	return args.Get(0).([]*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) GetExpiring(ctx context.Context, organizationID uuid.UUID, beforeDate time.Time) ([]*entities.License, error) {
	args := m.Called(ctx, organizationID, beforeDate)
	return args.Get(0).([]*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) UpdateLastValidated(ctx context.Context, licenseID uuid.UUID) error {
	args := m.Called(ctx, licenseID)
	return args.Error(0)
}

func (m *MockLicenseRepository) IncrementValidationCount(ctx context.Context, licenseID uuid.UUID) error {
	args := m.Called(ctx, licenseID)
	return args.Error(0)
}

type MockMachineRepository struct {
	mock.Mock
}

func (m *MockMachineRepository) Create(ctx context.Context, entity *entities.Machine) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockMachineRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Machine, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*entities.Machine), args.Error(1)
}

func (m *MockMachineRepository) Update(ctx context.Context, entity *entities.Machine) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockMachineRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockMachineRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockMachineRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.Machine, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.Machine), args.Get(1).(int64), args.Error(2)
}

func (m *MockMachineRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockMachineRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockMachineRepository) GetByFingerprint(ctx context.Context, fingerprint string, licenseID uuid.UUID) (*entities.Machine, error) {
	args := m.Called(ctx, fingerprint, licenseID)
	return args.Get(0).(*entities.Machine), args.Error(1)
}

func (m *MockMachineRepository) GetByLicense(ctx context.Context, licenseID uuid.UUID) ([]*entities.Machine, error) {
	args := m.Called(ctx, licenseID)
	return args.Get(0).([]*entities.Machine), args.Error(1)
}

func (m *MockMachineRepository) UpdateHeartbeat(ctx context.Context, machineID uuid.UUID) error {
	args := m.Called(ctx, machineID)
	return args.Error(0)
}

func (m *MockMachineRepository) GetStale(ctx context.Context, olderThan time.Time) ([]*entities.Machine, error) {
	args := m.Called(ctx, olderThan)
	return args.Get(0).([]*entities.Machine), args.Error(1)
}

type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, entity *entities.User) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*entities.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, entity *entities.User) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.User, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*entities.User, error) {
	args := m.Called(ctx, email)
	return args.Get(0).(*entities.User), args.Error(1)
}

func (m *MockUserRepository) UpdateLastLogin(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

// Helper function to create test UUIDs
func newTestUUID() uuid.UUID {
	return uuid.New()
}

// Helper function to create a test license
func createTestLicense() *entities.License {
	policy := &entities.Policy{
		ID:       newTestUUID(),
		Strict:   true,
		Floating: true,
	}
	policy.SetDefaultStrategies()

	return &entities.License{
		ID:             newTestUUID(),
		OrganizationID: newTestUUID(),
		ProductID:      newTestUUID(),
		PolicyID:       policy.ID,
		Key:            "test-license-key",
		OwnerType:      entities.LicenseOwnerTypeUser,
		OwnerID:        newTestUUID(),
		Status:         entities.LicenseStatusActive,
		Policy:         *policy,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

// Helper function to create a test validation service
func createTestValidationService() (*ValidationService, *MockLicenseRepository, *MockMachineRepository, *MockUserRepository) {
	mockLicenseRepo := &MockLicenseRepository{}
	mockMachineRepo := &MockMachineRepository{}
	mockUserRepo := &MockUserRepository{}
	logger := zerolog.Nop()

	service := NewValidationService(mockLicenseRepo, mockMachineRepo, mockUserRepo, logger)
	return service, mockLicenseRepo, mockMachineRepo, mockUserRepo
}

func TestNewValidationService(t *testing.T) {
	service, _, _, _ := createTestValidationService()
	assert.NotNil(t, service)
}

func TestValidateLicense_NilLicense(t *testing.T) {
	service, _, _, _ := createTestValidationService()
	ctx := context.Background()

	result, err := service.ValidateLicense(ctx, nil, nil)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "does not exist", result.Message)
	assert.Equal(t, ValidationCodeNotFound, result.Code)
}

func TestValidateLicense_BannedLicense(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	license.Status = entities.LicenseStatusBanned

	// Mock the UpdateLastValidated call (even though validation fails, touch still happens)
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, nil)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "is banned", result.Message)
	assert.Equal(t, ValidationCodeBanned, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_SuspendedLicense(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	license.Suspended = true

	// Mock the UpdateLastValidated call (even though validation fails, touch still happens)
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, nil)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "is suspended", result.Message)
	assert.Equal(t, ValidationCodeSuspended, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_ExpiredLicense_RevokeAccess(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	// Set license to expired
	pastTime := time.Now().Add(-24 * time.Hour)
	license.ExpiresAt = &pastTime
	// Set policy to revoke access
	license.Policy.ExpirationStrategy = &[]entities.ExpirationStrategy{entities.ExpirationRevokeAccess}[0]

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, nil)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "is expired", result.Message)
	assert.Equal(t, ValidationCodeExpired, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_ExpiredLicense_AllowAccess(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	// Set license to expired
	pastTime := time.Now().Add(-24 * time.Hour)
	license.ExpiresAt = &pastTime
	// Set policy to allow access
	license.Policy.ExpirationStrategy = &[]entities.ExpirationStrategy{entities.ExpirationAllowAccess}[0]
	// Set policy as not strict to skip machine requirements
	license.Policy.Strict = false

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, nil)

	assert.NoError(t, err)
	assert.True(t, result.Valid)
	assert.Equal(t, "is expired", result.Message)
	assert.Equal(t, ValidationCodeExpired, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_ValidLicense(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	// Set license as not strict to skip machine requirements
	license.Policy.Strict = false

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, nil)

	assert.NoError(t, err)
	assert.True(t, result.Valid)
	assert.Equal(t, "is valid", result.Message)
	assert.Equal(t, ValidationCodeValid, result.Code)
	assert.NotNil(t, result.Touches)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_SkipTouch(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	// Set license as not strict to skip machine requirements
	license.Policy.Strict = false

	options := &ValidationOptions{
		SkipTouch: true,
	}

	result, err := service.ValidateLicense(ctx, license, options)

	assert.NoError(t, err)
	assert.True(t, result.Valid)
	assert.Equal(t, "is valid", result.Message)
	assert.Equal(t, ValidationCodeValid, result.Code)

	// Should not call UpdateLastValidated when SkipTouch is true
	mockLicenseRepo.AssertNotCalled(t, "UpdateLastValidated")
}

func TestValidateLicense_ProductScopeMismatch(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	differentProductID := newTestUUID()

	options := &ValidationOptions{
		Scope: &ValidationScope{
			Product: &differentProductID,
		},
	}

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, options)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "product scope does not match", result.Message)
	assert.Equal(t, ValidationCodeProductScopeMismatch, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_PolicyScopeMismatch(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	differentPolicyID := newTestUUID()

	options := &ValidationOptions{
		Scope: &ValidationScope{
			Policy: &differentPolicyID,
		},
	}

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, options)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "policy scope does not match", result.Message)
	assert.Equal(t, ValidationCodePolicyScopeMismatch, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_UserScopeMismatch(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	differentUserID := "different-user-id"

	options := &ValidationOptions{
		Scope: &ValidationScope{
			User: &differentUserID,
		},
	}

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, options)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "user scope does not match", result.Message)
	assert.Equal(t, ValidationCodeUserScopeMismatch, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_TooManyUsers(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	// Set max users to 1
	maxUsers := 1
	license.MaxUsersOverride = &maxUsers
	// Set users count to 2 (exceeds limit)
	license.LicenseUsersCount = 2
	// Set policy to not allow overage
	license.Policy.OverageStrategy = &[]entities.OverageStrategy{entities.OverageNoOverage}[0]

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, nil)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "has too many associated users", result.Message)
	assert.Equal(t, ValidationCodeTooManyUsers, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_StrictPolicy_NoMachines(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	// Set policy as strict and not floating (node-locked)
	license.Policy.Strict = true
	license.Policy.Floating = false
	// Set machines count to 0
	license.MachinesCount = 0

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, nil)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "must have exactly 1 associated machine", result.Message)
	assert.Equal(t, ValidationCodeNoMachine, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}

func TestValidateLicense_StrictPolicy_FloatingNoMachines(t *testing.T) {
	service, mockLicenseRepo, _, _ := createTestValidationService()
	ctx := context.Background()

	license := createTestLicense()
	// Set policy as strict and floating
	license.Policy.Strict = true
	license.Policy.Floating = true
	// Set machines count to 0
	license.MachinesCount = 0

	// Mock the UpdateLastValidated call
	mockLicenseRepo.On("UpdateLastValidated", ctx, license.ID).Return(nil)

	result, err := service.ValidateLicense(ctx, license, nil)

	assert.NoError(t, err)
	assert.False(t, result.Valid)
	assert.Equal(t, "must have at least 1 associated machine", result.Message)
	assert.Equal(t, ValidationCodeNoMachines, result.Code)

	mockLicenseRepo.AssertExpectations(t)
}
