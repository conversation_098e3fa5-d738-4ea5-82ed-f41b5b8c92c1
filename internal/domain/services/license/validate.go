﻿package license

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

// ValidationCode represents validation result codes (Ruby: validation symbols)
type ValidationCode string

const (
	ValidationCodeValid                    ValidationCode = "VALID"
	ValidationCodeNotFound                 ValidationCode = "NOT_FOUND"
	ValidationCodeBanned                   ValidationCode = "BANNED"
	ValidationCodeSuspended                ValidationCode = "SUSPENDED"
	ValidationCodeExpired                  ValidationCode = "EXPIRED"
	ValidationCodeOverdue                  ValidationCode = "OVERDUE"
	ValidationCodeEnvironmentScopeMismatch ValidationCode = "ENVIRONMENT_SCOPE_MISMATCH"
	ValidationCodeEnvironmentScopeRequired ValidationCode = "ENVIRONMENT_SCOPE_REQUIRED"
	ValidationCodeProductScopeMismatch     ValidationCode = "PRODUCT_SCOPE_MISMATCH"
	ValidationCodeProductScopeRequired     ValidationCode = "PRODUCT_SCOPE_REQUIRED"
	ValidationCodePolicyScopeMismatch      ValidationCode = "POLICY_SCOPE_MISMATCH"
	ValidationCodePolicyScopeRequired      ValidationCode = "POLICY_SCOPE_REQUIRED"
	ValidationCodeUserScopeMismatch        ValidationCode = "USER_SCOPE_MISMATCH"
	ValidationCodeUserScopeRequired        ValidationCode = "USER_SCOPE_REQUIRED"
	ValidationCodeEntitlementsScopeEmpty   ValidationCode = "ENTITLEMENTS_SCOPE_EMPTY"
	ValidationCodeEntitlementsMissing      ValidationCode = "ENTITLEMENTS_MISSING"
	ValidationCodeNoMachine                ValidationCode = "NO_MACHINE"
	ValidationCodeNoMachines               ValidationCode = "NO_MACHINES"
	ValidationCodeMachineScopeMismatch     ValidationCode = "MACHINE_SCOPE_MISMATCH"
	ValidationCodeMachineScopeRequired     ValidationCode = "MACHINE_SCOPE_REQUIRED"
	ValidationCodeHeartbeatNotStarted      ValidationCode = "HEARTBEAT_NOT_STARTED"
	ValidationCodeHeartbeatDead            ValidationCode = "HEARTBEAT_DEAD"
	ValidationCodeFingerprintScopeEmpty    ValidationCode = "FINGERPRINT_SCOPE_EMPTY"
	ValidationCodeFingerprintScopeMismatch ValidationCode = "FINGERPRINT_SCOPE_MISMATCH"
	ValidationCodeFingerprintScopeRequired ValidationCode = "FINGERPRINT_SCOPE_REQUIRED"
	ValidationCodeComponentsScopeEmpty     ValidationCode = "COMPONENTS_SCOPE_EMPTY"
	ValidationCodeComponentsScopeMismatch  ValidationCode = "COMPONENTS_SCOPE_MISMATCH"
	ValidationCodeComponentsScopeRequired  ValidationCode = "COMPONENTS_SCOPE_REQUIRED"
	ValidationCodeChecksumScopeMismatch    ValidationCode = "CHECKSUM_SCOPE_MISMATCH"
	ValidationCodeChecksumScopeRequired    ValidationCode = "CHECKSUM_SCOPE_REQUIRED"
	ValidationCodeVersionScopeMismatch     ValidationCode = "VERSION_SCOPE_MISMATCH"
	ValidationCodeVersionScopeRequired     ValidationCode = "VERSION_SCOPE_REQUIRED"
	ValidationCodeTooManyUsers             ValidationCode = "TOO_MANY_USERS"
	ValidationCodeTooManyMachines          ValidationCode = "TOO_MANY_MACHINES"
	ValidationCodeTooManyCores             ValidationCode = "TOO_MANY_CORES"
	ValidationCodeTooManyProcesses         ValidationCode = "TOO_MANY_PROCESSES"
)

// ValidationScope represents validation scope parameters (Ruby: scope hash)
type ValidationScope struct {
	// Environment scope (removed in Go implementation as per requirements)
	// Product scope
	Product *uuid.UUID `json:"product,omitempty"`
	// Policy scope
	Policy *uuid.UUID `json:"policy,omitempty"`
	// User scope
	User *string `json:"user,omitempty"` // Can be UUID string or email
	// Entitlements scope
	Entitlements []string `json:"entitlements,omitempty"`
	// Machine scope
	Machine *uuid.UUID `json:"machine,omitempty"`
	// Fingerprint scope
	Fingerprint  *string  `json:"fingerprint,omitempty"`
	Fingerprints []string `json:"fingerprints,omitempty"`
	// Components scope
	Components []string `json:"components,omitempty"`
	// Checksum scope
	Checksum *string `json:"checksum,omitempty"`
	// Version scope
	Version *string `json:"version,omitempty"`
}

// ValidationOptions represents validation options (Ruby: service parameters)
type ValidationOptions struct {
	Scope     *ValidationScope `json:"scope,omitempty"`
	SkipTouch bool             `json:"skip_touch,omitempty"`
}

// ValidationResult represents the result of license validation (Ruby: service return value)
type ValidationResult struct {
	Valid   bool           `json:"valid"`
	Message string         `json:"message"`
	Code    ValidationCode `json:"code"`
	// Touches for database updates (Ruby: touches hash)
	Touches map[string]interface{} `json:"touches,omitempty"`
}

// ValidationService handles license validation business logic (Ruby: LicenseValidationService)
// Maps from Ruby LicenseValidationService with Go improvements and UUID consistency
type ValidationService struct {
	licenseRepo repositories.LicenseRepository
	machineRepo repositories.MachineRepository
	userRepo    repositories.UserRepository
	logger      zerolog.Logger
}

// NewValidationService creates a new license validation service
func NewValidationService(
	licenseRepo repositories.LicenseRepository,
	machineRepo repositories.MachineRepository,
	userRepo repositories.UserRepository,
	logger zerolog.Logger,
) *ValidationService {
	return &ValidationService{
		licenseRepo: licenseRepo,
		machineRepo: machineRepo,
		userRepo:    userRepo,
		logger:      logger,
	}
}

// ValidateLicense validates a license with the given scope (Ruby: call method)
func (s *ValidationService) ValidateLicense(ctx context.Context, license *entities.License, options *ValidationOptions) (*ValidationResult, error) {
	if options == nil {
		options = &ValidationOptions{}
	}

	// Initialize touches for database updates (Ruby: @touches)
	touches := map[string]interface{}{
		"last_validated_at": time.Now(),
	}

	// Perform validation (Ruby: validate! method)
	result := s.validate(ctx, license, options.Scope)
	result.Touches = touches

	// Update database unless skip_touch is true (Ruby: touch! method)
	if !options.SkipTouch && license != nil {
		if err := s.touchLicense(ctx, license, touches); err != nil {
			s.logger.Error().Err(err).Msg("Failed to update license validation timestamp")
			// Don't fail validation due to touch error, just log it
		}
	}

	return result, nil
}

// validate performs the core validation logic (Ruby: validate! method)
func (s *ValidationService) validate(ctx context.Context, license *entities.License, scope *ValidationScope) *ValidationResult {
	// Check if license exists
	if license == nil {
		return &ValidationResult{
			Valid:   false,
			Message: "does not exist",
			Code:    ValidationCodeNotFound,
		}
	}

	// Check if license's user has been banned
	if license.IsBanned() {
		return &ValidationResult{
			Valid:   false,
			Message: "is banned",
			Code:    ValidationCodeBanned,
		}
	}

	// Check if license is suspended
	if license.IsSuspended() {
		return &ValidationResult{
			Valid:   false,
			Message: "is suspended",
			Code:    ValidationCodeSuspended,
		}
	}

	// When revoking access, first check if license is expired (i.e. higher precedence)
	if license.Policy.RevokesAccess() && license.IsExpired() {
		return &ValidationResult{
			Valid:   false,
			Message: "is expired",
			Code:    ValidationCodeExpired,
		}
	}

	// Check if license is overdue for check in
	if license.IsCheckInOverdue() {
		return &ValidationResult{
			Valid:   false,
			Message: "is overdue for check in",
			Code:    ValidationCodeOverdue,
		}
	}

	// Scope validations (quick validation skips this by setting explicitly to false)
	if scope != nil {
		if result := s.validateScope(ctx, license, scope); result != nil {
			return result
		}
	}

	// Check usage limits
	if result := s.validateUsageLimits(ctx, license, scope); result != nil {
		return result
	}

	// Check machine requirements for strict policies
	if result := s.validateMachineRequirements(ctx, license, scope); result != nil {
		return result
	}

	// Check if license is expired after checking machine requirements
	if license.IsExpired() {
		return &ValidationResult{
			Valid:   license.Policy.AllowsAccess() || license.Policy.MaintainsAccess(),
			Message: "is expired",
			Code:    ValidationCodeExpired,
		}
	}

	// All good
	return &ValidationResult{
		Valid:   true,
		Message: "is valid",
		Code:    ValidationCodeValid,
	}
}

// validateScope validates scope requirements (Ruby: scope validation logic)
func (s *ValidationService) validateScope(ctx context.Context, license *entities.License, scope *ValidationScope) *ValidationResult {
	// Check against product scope requirements
	if scope.Product != nil {
		if license.ProductID != *scope.Product {
			return &ValidationResult{
				Valid:   false,
				Message: "product scope does not match",
				Code:    ValidationCodeProductScopeMismatch,
			}
		}
	} else {
		// Check if policy requires product scope (Ruby: require_product_scope validation)
		if license.Policy.RequiresProductScope() {
			return &ValidationResult{
				Valid:   false,
				Message: "product scope is required",
				Code:    ValidationCodeProductScopeRequired,
			}
		}
	}

	// Check against policy scope requirements
	if scope.Policy != nil {
		if license.PolicyID != *scope.Policy {
			return &ValidationResult{
				Valid:   false,
				Message: "policy scope does not match",
				Code:    ValidationCodePolicyScopeMismatch,
			}
		}
	} else {
		// Check if policy requires policy scope (Ruby: require_policy_scope validation)
		if license.Policy.RequiresPolicyScope() {
			return &ValidationResult{
				Valid:   false,
				Message: "policy scope is required",
				Code:    ValidationCodePolicyScopeRequired,
			}
		}
	}

	// Check against user scope requirements
	if scope.User != nil {
		if result := s.validateUserScope(ctx, license, *scope.User); result != nil {
			return result
		}
	} else {
		// Check if policy requires user scope (Ruby: require_user_scope validation)
		if license.Policy.RequiresUserScope() {
			return &ValidationResult{
				Valid:   false,
				Message: "user scope is required",
				Code:    ValidationCodeUserScopeRequired,
			}
		}
	}

	// Check against entitlements scope requirements
	if len(scope.Entitlements) > 0 {
		if result := s.validateEntitlementsScope(ctx, license, scope.Entitlements); result != nil {
			return result
		}
	}

	// Check against machine scope requirements
	if scope.Machine != nil {
		if result := s.validateMachineScope(ctx, license, *scope.Machine, scope.User); result != nil {
			return result
		}
	} else {
		// Check if policy requires machine scope (Ruby: require_machine_scope validation)
		if license.Policy.RequiresMachineScope() {
			return &ValidationResult{
				Valid:   false,
				Message: "machine scope is required",
				Code:    ValidationCodeMachineScopeRequired,
			}
		}
	}

	// Check against fingerprint scope requirements
	if scope.Fingerprint != nil || len(scope.Fingerprints) > 0 {
		fingerprints := scope.Fingerprints
		if scope.Fingerprint != nil {
			fingerprints = append(fingerprints, *scope.Fingerprint)
		}
		if result := s.validateFingerprintScope(ctx, license, fingerprints, scope.User); result != nil {
			return result
		}
	} else {
		// Check if policy requires fingerprint scope (Ruby: require_fingerprint_scope validation)
		if license.Policy.RequiresFingerprintScope() {
			return &ValidationResult{
				Valid:   false,
				Message: "fingerprint scope is required",
				Code:    ValidationCodeFingerprintScopeRequired,
			}
		}
	}

	// Check against components scope requirements
	if len(scope.Components) > 0 {
		if result := s.validateComponentsScope(ctx, license, scope.Components, scope.Fingerprint); result != nil {
			return result
		}
	} else {
		// Check if policy requires components scope (Ruby: require_components_scope validation)
		if license.Policy.RequiresComponentsScope() {
			return &ValidationResult{
				Valid:   false,
				Message: "components scope is required",
				Code:    ValidationCodeComponentsScopeRequired,
			}
		}
	}

	// Check against checksum scope requirements
	if scope.Checksum != nil {
		if result := s.validateChecksumScope(ctx, license, *scope.Checksum); result != nil {
			return result
		}
	} else {
		// Check if policy requires checksum scope (Ruby: require_checksum_scope validation)
		if license.Policy.RequiresChecksumScope() {
			return &ValidationResult{
				Valid:   false,
				Message: "checksum scope is required",
				Code:    ValidationCodeChecksumScopeRequired,
			}
		}
	}

	// Check against version scope requirements
	if scope.Version != nil {
		if result := s.validateVersionScope(ctx, license, *scope.Version); result != nil {
			return result
		}
	} else {
		// Check if policy requires version scope (Ruby: require_version_scope validation)
		if license.Policy.RequiresVersionScope() {
			return &ValidationResult{
				Valid:   false,
				Message: "version scope is required",
				Code:    ValidationCodeVersionScopeRequired,
			}
		}
	}

	return nil
}

// validateUsageLimits validates usage limits (Ruby: usage limit validation logic)
func (s *ValidationService) validateUsageLimits(ctx context.Context, license *entities.License, scope *ValidationScope) *ValidationResult {
	// Check if license has exceeded its user limit
	if license.HasMaxUsers() {
		maxUsers := *license.GetMaxUsers()
		usersCount := license.GetUsersCount()

		if usersCount > maxUsers {
			allowOverage := s.checkUserOverage(license, usersCount, maxUsers)
			return &ValidationResult{
				Valid:   allowOverage,
				Message: "has too many associated users",
				Code:    ValidationCodeTooManyUsers,
			}
		}
	}

	// Check process limits validation (Ruby: process limits validation logic)
	if result := s.validateProcessLimits(ctx, license, scope); result != nil {
		return result
	}

	return nil
}

// validateMachineRequirements validates machine requirements for strict policies (Ruby: machine validation logic)
func (s *ValidationService) validateMachineRequirements(ctx context.Context, license *entities.License, scope *ValidationScope) *ValidationResult {
	// Check if license policy is strict i.e. requires machine tracking (and exit early if not strict).
	if !license.Policy.Strict {
		return nil
	}

	machinesCount := license.GetMachinesCount()

	// Check if license policy allows floating and if not, should have single activation
	if !license.Policy.Floating && machinesCount == 0 {
		return &ValidationResult{
			Valid:   false,
			Message: "must have exactly 1 associated machine",
			Code:    ValidationCodeNoMachine,
		}
	}

	// When node-locked, license's machine count should not surpass 1
	if !license.Policy.Floating && machinesCount > 1 {
		maxMachines := 1
		if license.HasMaxMachines() {
			maxMachines = *license.GetMaxMachines()
		}

		allowOverage := s.checkMachineOverage(license, machinesCount, maxMachines)
		if machinesCount > maxMachines {
			return &ValidationResult{
				Valid:   allowOverage,
				Message: "has too many associated machines",
				Code:    ValidationCodeTooManyMachines,
			}
		}
	}

	// When floating, license should have at least 1 activation
	if license.Policy.Floating && machinesCount == 0 {
		return &ValidationResult{
			Valid:   false,
			Message: "must have at least 1 associated machine",
			Code:    ValidationCodeNoMachines,
		}
	}

	// When floating, license's machine count should not surpass what policy allows
	if license.Policy.Floating && license.HasMaxMachines() && machinesCount > 1 {
		maxMachines := *license.GetMaxMachines()

		allowOverage := s.checkMachineOverage(license, machinesCount, maxMachines)
		if machinesCount > maxMachines {
			return &ValidationResult{
				Valid:   allowOverage,
				Message: "has too many associated machines",
				Code:    ValidationCodeTooManyMachines,
			}
		}
	}

	// Check if license has exceeded its CPU core limit
	if license.HasMaxCores() {
		maxCores := *license.GetMaxCores()
		coresCount := license.GetMachinesCoreCount()

		if coresCount > maxCores {
			allowOverage := s.checkCoreOverage(license, coresCount, maxCores)
			return &ValidationResult{
				Valid:   allowOverage,
				Message: "has too many associated machine cores",
				Code:    ValidationCodeTooManyCores,
			}
		}
	}

	// Check machine lease per user logic (Ruby: machine lease per user validation)
	if result := s.validateMachineLeasePerUser(ctx, license, scope); result != nil {
		return result
	}

	return nil
}

// validateUserScope validates user scope requirements (Ruby: user scope validation)
func (s *ValidationService) validateUserScope(ctx context.Context, license *entities.License, userIdentifier string) *ValidationResult {
	// Check if user matches license owner
	if license.GetOwnerID() == userIdentifier {
		return nil
	}

	// TODO: Check if user exists in license users
	// This would require additional repository methods to check user associations
	// For now, we'll implement basic validation

	return &ValidationResult{
		Valid:   false,
		Message: "user scope does not match",
		Code:    ValidationCodeUserScopeMismatch,
	}
}

// validateMachineScope validates machine scope requirements (Ruby: machine scope validation)
func (s *ValidationService) validateMachineScope(ctx context.Context, license *entities.License, machineID uuid.UUID, userScope *string) *ValidationResult {
	// Check if license has machines
	if license.GetMachinesCount() == 0 {
		if license.Policy.Floating {
			return &ValidationResult{
				Valid:   false,
				Message: "machine is not activated (has no associated machines)",
				Code:    ValidationCodeNoMachines,
			}
		} else {
			return &ValidationResult{
				Valid:   false,
				Message: "machine is not activated (has no associated machine)",
				Code:    ValidationCodeNoMachine,
			}
		}
	}

	// Find the specific machine by ID (Ruby: machine scope validation logic)
	machine, err := s.machineRepo.GetByID(ctx, machineID)
	if err != nil {
		s.logger.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to find machine")
		return &ValidationResult{
			Valid:   false,
			Message: "machine scope does not match",
			Code:    ValidationCodeMachineScopeMismatch,
		}
	}

	// Verify machine belongs to the license (Ruby: machine.license_id == license.id)
	if machine.LicenseID != license.ID {
		return &ValidationResult{
			Valid:   false,
			Message: "machine scope does not match",
			Code:    ValidationCodeMachineScopeMismatch,
		}
	}

	// Check machine-user ownership if user scope is provided (Ruby: machine owner validation)
	if userScope != nil {
		// Check if machine owner matches user scope (Ruby: machine.owner_id == user_identifier)
		if machine.OwnerID != nil && machine.OwnerID.String() != *userScope {
			// Also check if user exists in license users (Ruby: license.users.exists?(user_identifier))
			if license.GetOwnerID() != *userScope {
				// TODO: Check if user exists in license users by ID or email
				// This would require additional repository methods
				return &ValidationResult{
					Valid:   false,
					Message: "machine scope does not match",
					Code:    ValidationCodeMachineScopeMismatch,
				}
			}
		}
	}

	// Check machine heartbeat status if policy requires heartbeat (Ruby: heartbeat validation)
	if license.Policy.RequireHeartbeat {
		heartbeatStatus := machine.GetHeartbeatStatus()

		// Check if heartbeat has not started (Ruby: machine.heartbeat_not_started?)
		if heartbeatStatus == entities.HeartbeatStatusNotStarted {
			return &ValidationResult{
				Valid:   false,
				Message: "machine heartbeat has not started",
				Code:    ValidationCodeHeartbeatNotStarted,
			}
		}

		// Check if machine is dead (Ruby: machine.heartbeat_dead?)
		if heartbeatStatus == entities.HeartbeatStatusDead {
			return &ValidationResult{
				Valid:   false,
				Message: "machine heartbeat is dead",
				Code:    ValidationCodeHeartbeatDead,
			}
		}
	}

	return nil
}

// validateFingerprintScope validates fingerprint scope requirements (Ruby: fingerprint scope validation)
func (s *ValidationService) validateFingerprintScope(ctx context.Context, license *entities.License, fingerprints []string, userScope *string) *ValidationResult {
	if len(fingerprints) == 0 {
		return &ValidationResult{
			Valid:   false,
			Message: "fingerprint scope is empty",
			Code:    ValidationCodeFingerprintScopeEmpty,
		}
	}

	// Check if license has machines
	if license.GetMachinesCount() == 0 {
		if license.Policy.Floating {
			return &ValidationResult{
				Valid:   false,
				Message: "fingerprint is not activated (has no associated machines)",
				Code:    ValidationCodeNoMachines,
			}
		} else {
			return &ValidationResult{
				Valid:   false,
				Message: "fingerprint is not activated (has no associated machine)",
				Code:    ValidationCodeNoMachine,
			}
		}
	}

	// Get all machines for the license to check fingerprint matching (Ruby: license.machines)
	machines, err := s.machineRepo.GetByLicense(ctx, license.ID)
	if err != nil {
		s.logger.Error().Err(err).Str("license_id", license.ID.String()).Msg("Failed to get machines for license")
		return &ValidationResult{
			Valid:   false,
			Message: "fingerprint scope does not match",
			Code:    ValidationCodeFingerprintScopeMismatch,
		}
	}

	// Get machine matching strategy from policy (Ruby: policy.machine_matching_strategy)
	matchingStrategy := license.Policy.GetMachineMatchingStrategy()

	// Count matching fingerprints based on strategy (Ruby: fingerprint matching logic)
	matchingMachines := 0
	for _, machine := range machines {
		// Check if machine fingerprint matches any provided fingerprints
		for _, fingerprint := range fingerprints {
			if machine.Fingerprint == fingerprint {
				// Check machine-user ownership if user scope is provided (Ruby: machine owner validation)
				if userScope != nil {
					if machine.OwnerID != nil && machine.OwnerID.String() != *userScope {
						// Also check if user exists in license users
						if license.GetOwnerID() != *userScope {
							continue // Skip this machine if user doesn't match
						}
					}
				}

				// Check machine heartbeat status if policy requires heartbeat (Ruby: heartbeat validation)
				if license.Policy.RequireHeartbeat {
					heartbeatStatus := machine.GetHeartbeatStatus()
					if heartbeatStatus == entities.HeartbeatStatusNotStarted {
						return &ValidationResult{
							Valid:   false,
							Message: "machine heartbeat has not started",
							Code:    ValidationCodeHeartbeatNotStarted,
						}
					}
					if heartbeatStatus == entities.HeartbeatStatusDead {
						return &ValidationResult{
							Valid:   false,
							Message: "machine heartbeat is dead",
							Code:    ValidationCodeHeartbeatDead,
						}
					}
				}

				matchingMachines++
				break // Found a match for this machine, move to next machine
			}
		}
	}

	// Check if we have enough matches based on strategy (Ruby: matching strategy validation)
	totalMachines := len(machines)
	requiredMatches := matchingStrategy.RequiredMatches(totalMachines)

	if matchingMachines < requiredMatches {
		return &ValidationResult{
			Valid:   false,
			Message: "fingerprint scope does not match",
			Code:    ValidationCodeFingerprintScopeMismatch,
		}
	}

	return nil
}

// validateComponentsScope validates components scope requirements (Ruby: components scope validation)
func (s *ValidationService) validateComponentsScope(ctx context.Context, license *entities.License, components []string, fingerprint *string) *ValidationResult {
	if len(components) == 0 {
		return &ValidationResult{
			Valid:   false,
			Message: "components scope is empty",
			Code:    ValidationCodeComponentsScopeEmpty,
		}
	}

	if fingerprint == nil {
		return &ValidationResult{
			Valid:   false,
			Message: "fingerprint scope is required when using the components scope",
			Code:    ValidationCodeFingerprintScopeRequired,
		}
	}

	// Find machine by fingerprint (Ruby: machine = license.machines.find_by(fingerprint: fingerprint))
	machine, err := s.machineRepo.GetByFingerprint(ctx, *fingerprint, license.ID)
	if err != nil {
		s.logger.Error().Err(err).Str("fingerprint", *fingerprint).Msg("Failed to find machine by fingerprint")
		return &ValidationResult{
			Valid:   false,
			Message: "components scope does not match",
			Code:    ValidationCodeComponentsScopeMismatch,
		}
	}

	// Check if machine has components (Ruby: machine.components.any?)
	if !machine.HasComponents() {
		return &ValidationResult{
			Valid:   false,
			Message: "components scope does not match",
			Code:    ValidationCodeComponentsScopeMismatch,
		}
	}

	// Get component matching strategy from policy (Ruby: policy.component_matching_strategy)
	matchingStrategy := license.Policy.GetComponentMatchingStrategy()

	// Count matching components based on strategy (Ruby: component matching logic)
	matchingComponents := 0
	totalComponents := len(machine.Components)

	for _, componentName := range components {
		if machine.HasComponent(componentName) {
			matchingComponents++
		}
	}

	// Check if we have enough matches based on strategy (Ruby: matching strategy validation)
	requiredMatches := matchingStrategy.RequiredMatches(totalComponents)

	if matchingComponents < requiredMatches {
		return &ValidationResult{
			Valid:   false,
			Message: "components scope does not match",
			Code:    ValidationCodeComponentsScopeMismatch,
		}
	}

	return nil
}

// validateChecksumScope validates checksum scope requirements (Ruby: checksum scope validation)
func (s *ValidationService) validateChecksumScope(ctx context.Context, license *entities.License, checksum string) *ValidationResult {
	// INTENDED FUNCTIONALITY (Ruby lines 219-234):
	// This method should validate that the provided checksum matches an accessible artifact
	// for the license's product. The Ruby implementation performs the following steps:
	//
	// 1. Find accessible artifacts for the license's product (Ruby: license.product.artifacts.accessible_by(license))
	// 2. Check if any artifact has a matching checksum (Ruby: artifacts.exists?(checksum: checksum))
	// 3. Apply entitlement constraints if the artifact has constraints (Ruby: artifact.constraints.any?)
	// 4. Validate license has required entitlements for constrained artifacts
	// 5. Return validation failure if no matching accessible artifact is found
	//
	// MISSING IMPLEMENTATION:
	// - ArtifactRepository interface and implementation
	// - Product.GetAccessibleArtifacts() method
	// - Artifact entity with checksum validation
	// - Entitlement constraint checking logic
	//
	// For now, this validation is skipped in the Go implementation.
	// When implementing, add ArtifactRepository to ValidationService dependencies.

	s.logger.Debug().Str("checksum", checksum).Msg("Checksum scope validation skipped - artifact repository not implemented")
	return nil
}

// validateVersionScope validates version scope requirements (Ruby: version scope validation)
func (s *ValidationService) validateVersionScope(ctx context.Context, license *entities.License, version string) *ValidationResult {
	// INTENDED FUNCTIONALITY (Ruby lines 235-254):
	// This method should validate that the provided version matches an accessible release
	// for the license's product. The Ruby implementation performs the following steps:
	//
	// 1. Find accessible releases for the license's product (Ruby: license.product.releases.accessible_by(license))
	// 2. Check if any release has a matching version (Ruby: releases.exists?(version: version))
	// 3. Apply entitlement constraints if the release has constraints (Ruby: release.constraints.any?)
	// 4. Validate license has required entitlements for constrained releases
	// 5. Support semantic version matching and channel-based access control
	// 6. Return validation failure if no matching accessible release is found
	//
	// MISSING IMPLEMENTATION:
	// - ReleaseRepository interface and implementation
	// - Product.GetAccessibleReleases() method
	// - Release entity with version validation and semantic versioning
	// - Channel-based access control logic
	// - Entitlement constraint checking logic
	//
	// For now, this validation is skipped in the Go implementation.
	// When implementing, add ReleaseRepository to ValidationService dependencies.

	s.logger.Debug().Str("version", version).Msg("Version scope validation skipped - release repository not implemented")
	return nil
}

// checkUserOverage checks if user overage is allowed (Ruby: user overage logic)
func (s *ValidationService) checkUserOverage(license *entities.License, usersCount, maxUsers int) bool {
	return license.Policy.AlwaysAllowsOverage() ||
		(license.Policy.Allows125xOverage() && usersCount <= int(float64(maxUsers)*1.25)) ||
		(license.Policy.Allows15xOverage() && usersCount <= int(float64(maxUsers)*1.5)) ||
		(license.Policy.Allows2xOverage() && usersCount <= maxUsers*2)
}

// checkMachineOverage checks if machine overage is allowed (Ruby: machine overage logic)
func (s *ValidationService) checkMachineOverage(license *entities.License, machinesCount, maxMachines int) bool {
	if license.Policy.Floating {
		return license.Policy.AlwaysAllowsOverage() ||
			(license.Policy.Allows125xOverage() && machinesCount <= int(float64(maxMachines)*1.25)) ||
			(license.Policy.Allows15xOverage() && machinesCount <= int(float64(maxMachines)*1.5)) ||
			(license.Policy.Allows2xOverage() && machinesCount <= maxMachines*2)
	} else {
		// Node-locked only allows 2x overage
		return license.Policy.AlwaysAllowsOverage() ||
			(license.Policy.Allows2xOverage() && machinesCount <= maxMachines*2)
	}
}

// checkCoreOverage checks if core overage is allowed (Ruby: core overage logic)
func (s *ValidationService) checkCoreOverage(license *entities.License, coresCount, maxCores int) bool {
	return license.Policy.AlwaysAllowsOverage() ||
		(license.Policy.Allows125xOverage() && coresCount <= int(float64(maxCores)*1.25)) ||
		(license.Policy.Allows15xOverage() && coresCount <= int(float64(maxCores)*1.5)) ||
		(license.Policy.Allows2xOverage() && coresCount <= maxCores*2)
}

// validateEntitlementsScope validates entitlements scope requirements (Ruby: entitlements scope validation)
func (s *ValidationService) validateEntitlementsScope(ctx context.Context, license *entities.License, entitlements []string) *ValidationResult {
	// INTENDED FUNCTIONALITY (Ruby lines 92-101):
	// This method should validate that the license has all required entitlements
	// for the requested scope. The Ruby implementation performs the following steps:
	//
	// 1. Check if entitlements scope is empty (Ruby: entitlements.empty?)
	// 2. Get license entitlements (Ruby: license.entitlements.pluck(:code))
	// 3. Check if all requested entitlements exist in license entitlements
	// 4. Validate entitlement constraints and access permissions
	// 5. Handle entitlement inheritance from policy or product level
	// 6. Support wildcard entitlements and hierarchical entitlement matching
	// 7. Return validation failure if any required entitlement is missing
	//
	// Key Ruby logic patterns:
	// - license.entitlements.exists?(code: entitlement_code)
	// - Entitlement constraint validation and access control
	// - Hierarchical entitlement matching (parent.child entitlements)
	// - Wildcard entitlement support (* matches all)
	// - Time-based entitlement expiration checking
	//
	// MISSING IMPLEMENTATION:
	// - EntitlementRepository interface and implementation
	// - License.GetEntitlements() method
	// - Entitlement entity with code, constraints, and expiration
	// - Hierarchical entitlement matching logic
	// - Wildcard entitlement support
	// - Time-based entitlement validation
	// - Entitlement constraint checking
	//
	// BUSINESS IMPACT:
	// Entitlements provide fine-grained access control for features, modules,
	// or capabilities within licensed software. This is critical for:
	// - Feature-based licensing (e.g., premium features)
	// - Module-based access control
	// - Time-limited feature access
	// - Hierarchical permission systems
	//
	// For now, this validation is intentionally skipped in the Go implementation
	// as requested by the user. When implementing, add EntitlementRepository
	// to ValidationService dependencies and implement full entitlement logic.

	s.logger.Debug().
		Strs("entitlements", entitlements).
		Msg("Entitlements scope validation skipped - entitlement system not implemented in Go version")

	return nil
}

// validateMachineLeasePerUser validates machine lease per user logic (Ruby: machine lease per user validation)
func (s *ValidationService) validateMachineLeasePerUser(ctx context.Context, license *entities.License, scope *ValidationScope) *ValidationResult {
	// Check if policy uses machine lease per user strategy (Ruby: policy.machine_lease_per_user?)
	if !license.Policy.MachineLeasePerUser() {
		return nil // Not using per-user leasing, skip validation
	}

	// INTENDED FUNCTIONALITY (Ruby lines 276-336):
	// This method should validate machine limits on a per-user basis:
	//
	// 1. Get user identifier from scope (required for per-user validation)
	// 2. Count machines associated with the specific user for this license
	// 3. Compare against license max_machines limit per user
	// 4. Apply overage policies if machines exceed limit
	// 5. Handle user ownership validation and machine-user associations
	//
	// Key Ruby logic patterns:
	// - license.machines.where(owner_id: user_id).count
	// - Validate user exists in license users or is license owner
	// - Apply machine matching strategies for user-specific machines
	// - Check machine heartbeat status for user's machines
	//
	// MISSING IMPLEMENTATION:
	// - User scope extraction and validation
	// - Machine.GetByUserAndLicense() repository method
	// - User-specific machine counting logic
	// - Machine-user ownership validation
	// - User-based overage checking methods
	//
	// For now, this validation is skipped in the Go implementation.
	// When implementing, enhance MachineRepository with user-specific queries.

	s.logger.Debug().Msg("Machine lease per user validation skipped - user-specific machine counting not implemented")
	return nil
}

// validateProcessLimits validates process limits based on leasing strategy (Ruby: process limits validation)
func (s *ValidationService) validateProcessLimits(ctx context.Context, license *entities.License, scope *ValidationScope) *ValidationResult {
	// Check if license has max processes limit (Ruby: max_processes? check)
	if !license.HasMaxProcesses() {
		return nil // No process limits to validate
	}

	maxProcesses := *license.GetMaxProcesses()

	// Get process leasing strategy from policy (Ruby: policy.process_leasing_strategy)
	leasingStrategy := license.Policy.GetProcessLeasingStrategy()

	// INTENDED FUNCTIONALITY (Ruby lines 366-410):
	// This method should validate process limits based on the leasing strategy:
	//
	// PER_LICENSE strategy (Ruby: process_lease_per_license?):
	// - Count total active processes across all machines for the license
	// - Compare against license max_processes limit
	// - Allow overage based on policy overage settings
	//
	// PER_MACHINE strategy (Ruby: process_lease_per_machine?):
	// - Count active processes per machine
	// - Compare each machine's processes against machine max_processes limit
	// - Machine max_processes can be overridden per machine
	//
	// PER_USER strategy (Ruby: process_lease_per_user?):
	// - Count active processes per user across all machines
	// - Compare against license max_processes limit per user
	// - Requires user scope to be provided for validation
	//
	// MISSING IMPLEMENTATION:
	// - ProcessRepository interface and implementation for counting active processes
	// - Machine.GetActiveProcessesCount() method
	// - License.GetActiveProcessesCount() method
	// - User-based process counting logic
	// - Process overage checking methods
	//
	// For now, this validation is skipped in the Go implementation.
	// When implementing, add ProcessRepository to ValidationService dependencies.

	s.logger.Debug().
		Str("leasing_strategy", string(leasingStrategy)).
		Int("max_processes", maxProcesses).
		Msg("Process limits validation skipped - process repository not implemented")

	return nil
}

// touchLicense updates license validation timestamp (Ruby: touch! method)
func (s *ValidationService) touchLicense(ctx context.Context, license *entities.License, touches map[string]interface{}) error {
	// For now, just update the last validated timestamp
	// In a full implementation, this would handle all touch fields
	return s.licenseRepo.UpdateLastValidated(ctx, license.ID)
}
