package license

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"golang.org/x/crypto/bcrypt"
)

// Test helper functions
func createTestKeyLookupService() (*KeyLookupService, *MockLicenseRepository) {
	mockRepo := &MockLicenseRepository{}
	cryptoService := crypto.NewCryptoService()
	logger := zerolog.New(nil).Level(zerolog.Disabled)

	service := NewKeyLookupService(mockRepo, cryptoService, logger)
	return service, mockRepo
}

func createTestLicenseForKeyLookup() *entities.License {
	organizationID := uuid.New()
	licenseID := uuid.New()

	return &entities.License{
		ID:             licenseID,
		OrganizationID: organizationID,
		Key:            "FC1ECF-659627-58D58E-42130E-ADD88F-V3",
		Status:         entities.LicenseStatusActive,
		Uses:           0,
		Suspended:      false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

func createTestLegacyLicense() (*entities.License, string) {
	organizationID := uuid.New()
	licenseID := uuid.New()

	// Create a legacy encrypted key pattern: {license_id}-{encrypted_bits}
	originalKey := licenseID.String() + "-encrypted-bits-here"

	// Create bcrypt hash of the original key for testing
	hashedKey, _ := bcrypt.GenerateFromPassword([]byte(originalKey), bcrypt.DefaultCost)

	license := &entities.License{
		ID:             licenseID,
		OrganizationID: organizationID,
		Key:            string(hashedKey),
		Status:         entities.LicenseStatusActive,
		Uses:           0,
		Suspended:      false,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	return license, originalKey
}

func TestNewKeyLookupService(t *testing.T) {
	service, _ := createTestKeyLookupService()

	assert.NotNil(t, service)
	assert.NotNil(t, service.licenseRepo)
	assert.NotNil(t, service.cryptoService)
}

func TestKeyLookupService_ValidateOptions(t *testing.T) {
	service, _ := createTestKeyLookupService()

	t.Run("valid options", func(t *testing.T) {
		options := KeyLookupOptions{
			OrganizationID:  uuid.New(),
			Key:             "test-key",
			LegacyEncrypted: false,
		}

		err := service.validateOptions(options)
		assert.NoError(t, err)
	})

	t.Run("missing organization ID", func(t *testing.T) {
		options := KeyLookupOptions{
			OrganizationID:  uuid.Nil,
			Key:             "test-key",
			LegacyEncrypted: false,
		}

		err := service.validateOptions(options)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "organization_id is required")
	})

	t.Run("missing key", func(t *testing.T) {
		options := KeyLookupOptions{
			OrganizationID:  uuid.New(),
			Key:             "",
			LegacyEncrypted: false,
		}

		err := service.validateOptions(options)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "key is required")
	})
}

func TestKeyLookupService_LookupNormalKey(t *testing.T) {
	service, mockRepo := createTestKeyLookupService()
	ctx := context.Background()

	t.Run("successful normal key lookup", func(t *testing.T) {
		license := createTestLicenseForKeyLookup()
		options := KeyLookupOptions{
			OrganizationID:  license.OrganizationID,
			Key:             license.Key,
			LegacyEncrypted: false,
		}

		mockRepo.On("GetByKey", ctx, license.Key).Return(license, nil)

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.True(t, result.Found)
		assert.False(t, result.Rehashed)
		assert.Equal(t, license.ID, result.License.ID)
		assert.Equal(t, license.Key, result.License.Key)

		mockRepo.AssertExpectations(t)
	})

	t.Run("license not found", func(t *testing.T) {
		options := KeyLookupOptions{
			OrganizationID:  uuid.New(),
			Key:             "non-existent-key",
			LegacyEncrypted: false,
		}

		mockRepo.On("GetByKey", ctx, "non-existent-key").Return(nil, fmt.Errorf("record not found"))

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.False(t, result.Found)
		assert.Nil(t, result.License)

		mockRepo.AssertExpectations(t)
	})

	t.Run("license belongs to different organization", func(t *testing.T) {
		license := createTestLicenseForKeyLookup()
		differentOrgID := uuid.New()

		options := KeyLookupOptions{
			OrganizationID:  differentOrgID,
			Key:             license.Key,
			LegacyEncrypted: false,
		}

		mockRepo.On("GetByKey", ctx, license.Key).Return(license, nil)

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.False(t, result.Found)
		assert.Nil(t, result.License)

		mockRepo.AssertExpectations(t)
	})
}

func TestKeyLookupService_LookupLegacyEncryptedKey(t *testing.T) {
	service, mockRepo := createTestKeyLookupService()
	ctx := context.Background()

	t.Run("successful legacy encrypted key lookup", func(t *testing.T) {
		license, originalKey := createTestLegacyLicense()
		options := KeyLookupOptions{
			OrganizationID:  license.OrganizationID,
			Key:             originalKey,
			LegacyEncrypted: true,
		}

		mockRepo.On("GetByID", ctx, license.ID).Return(license, nil)
		mockRepo.On("Update", ctx, mock.AnythingOfType("*entities.License")).Return(nil)

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.True(t, result.Found)
		assert.Equal(t, license.ID, result.License.ID)
		// Note: Rehashed status depends on the bcrypt comparison logic

		mockRepo.AssertExpectations(t)
	})

	t.Run("invalid encrypted key pattern", func(t *testing.T) {
		options := KeyLookupOptions{
			OrganizationID:  uuid.New(),
			Key:             "invalid-pattern-key",
			LegacyEncrypted: true,
		}

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.False(t, result.Found)
		assert.Nil(t, result.License)
	})

	t.Run("invalid license ID in encrypted key", func(t *testing.T) {
		options := KeyLookupOptions{
			OrganizationID:  uuid.New(),
			Key:             "invalid-uuid-here-encrypted-bits",
			LegacyEncrypted: true,
		}

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.False(t, result.Found)
		assert.Nil(t, result.License)
	})

	t.Run("license not found for encrypted key", func(t *testing.T) {
		licenseID := uuid.New()
		encryptedKey := licenseID.String() + "-encrypted-bits"

		options := KeyLookupOptions{
			OrganizationID:  uuid.New(),
			Key:             encryptedKey,
			LegacyEncrypted: true,
		}

		mockRepo.On("GetByID", ctx, licenseID).Return(nil, fmt.Errorf("record not found"))

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.False(t, result.Found)
		assert.Nil(t, result.License)

		mockRepo.AssertExpectations(t)
	})

	t.Run("license belongs to different organization", func(t *testing.T) {
		license, originalKey := createTestLegacyLicense()
		differentOrgID := uuid.New()

		options := KeyLookupOptions{
			OrganizationID:  differentOrgID,
			Key:             originalKey,
			LegacyEncrypted: true,
		}

		mockRepo.On("GetByID", ctx, license.ID).Return(license, nil)

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.False(t, result.Found)
		assert.Nil(t, result.License)

		mockRepo.AssertExpectations(t)
	})

	t.Run("hashed token comparison fails", func(t *testing.T) {
		license := createTestLicenseForKeyLookup()
		wrongKey := license.ID.String() + "-wrong-encrypted-bits"

		options := KeyLookupOptions{
			OrganizationID:  license.OrganizationID,
			Key:             wrongKey,
			LegacyEncrypted: true,
		}

		mockRepo.On("GetByID", ctx, license.ID).Return(license, nil)

		result, err := service.LookupByKey(ctx, options)

		require.NoError(t, err)
		assert.False(t, result.Found)
		assert.Nil(t, result.License)

		mockRepo.AssertExpectations(t)
	})
}

func TestKeyLookupService_CompareHashedToken(t *testing.T) {
	service, _ := createTestKeyLookupService()

	t.Run("valid token comparison", func(t *testing.T) {
		plainToken := "test-token-123"
		hashedToken, err := bcrypt.GenerateFromPassword([]byte(plainToken), bcrypt.DefaultCost)
		require.NoError(t, err)

		result := service.compareHashedToken(string(hashedToken), plainToken)
		assert.True(t, result)
	})

	t.Run("invalid token comparison", func(t *testing.T) {
		plainToken := "test-token-123"
		wrongToken := "wrong-token-456"
		hashedToken, err := bcrypt.GenerateFromPassword([]byte(plainToken), bcrypt.DefaultCost)
		require.NoError(t, err)

		result := service.compareHashedToken(string(hashedToken), wrongToken)
		assert.False(t, result)
	})

	t.Run("empty tokens", func(t *testing.T) {
		result := service.compareHashedToken("", "test")
		assert.False(t, result)

		result = service.compareHashedToken("test", "")
		assert.False(t, result)

		result = service.compareHashedToken("", "")
		assert.False(t, result)
	})
}

func TestKeyLookupService_SecureCompare(t *testing.T) {
	service, _ := createTestKeyLookupService()

	t.Run("identical strings", func(t *testing.T) {
		result := service.secureCompare("test123", "test123")
		assert.True(t, result)
	})

	t.Run("different strings same length", func(t *testing.T) {
		result := service.secureCompare("test123", "test456")
		assert.False(t, result)
	})

	t.Run("different length strings", func(t *testing.T) {
		result := service.secureCompare("test", "test123")
		assert.False(t, result)

		result = service.secureCompare("test123", "test")
		assert.False(t, result)
	})

	t.Run("empty strings", func(t *testing.T) {
		result := service.secureCompare("", "")
		assert.True(t, result)

		result = service.secureCompare("", "test")
		assert.False(t, result)
	})
}
