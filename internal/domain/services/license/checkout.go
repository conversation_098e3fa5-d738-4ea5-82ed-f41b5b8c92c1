﻿package license

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services/checkout"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
)

// LicenseData represents the clean Go structure for license data in certificates
type LicenseData struct {
	ID                   string                 `json:"id"`
	Name                 *string                `json:"name,omitempty"`
	Key                  string                 `json:"key"`
	Status               string                 `json:"status"`
	Uses                 int                    `json:"uses"`
	Suspended            bool                   `json:"suspended"`
	Scheme               string                 `json:"scheme"`
	Encrypted            bool                   `json:"encrypted"`
	Strict               bool                   `json:"strict"`
	Floating             bool                   `json:"floating"`
	Protected            bool                   `json:"protected"`
	MaxMachines          *int                   `json:"max_machines,omitempty"`
	MaxProcesses         *int                   `json:"max_processes,omitempty"`
	MaxUsers             *int                   `json:"max_users,omitempty"`
	MaxCores             *int                   `json:"max_cores,omitempty"`
	MaxUses              *int                   `json:"max_uses,omitempty"`
	RequireHeartbeat     bool                   `json:"require_heartbeat"`
	RequireCheckIn       bool                   `json:"require_check_in"`
	ExpiresAt            *time.Time             `json:"expiry,omitempty"`
	LastValidatedVersion *string                `json:"version,omitempty"`
	LastValidatedAt      *time.Time             `json:"last_validated,omitempty"`
	LastCheckInAt        *time.Time             `json:"last_check_in,omitempty"`
	NextCheckInAt        *time.Time             `json:"next_check_in,omitempty"`
	LastCheckOutAt       *time.Time             `json:"last_check_out,omitempty"`
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt            time.Time              `json:"created"`
	UpdatedAt            time.Time              `json:"updated"`

	// Included relationships (only when requested)
	Product *ProductData `json:"product,omitempty"`
	Policy  *PolicyData  `json:"policy,omitempty"`
}

// ProductData represents clean product information for certificates
type ProductData struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	URL       *string                `json:"url,omitempty"`
	Platforms []string               `json:"platforms,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt time.Time              `json:"created"`
	UpdatedAt time.Time              `json:"updated"`
}

// PolicyData represents clean policy information for certificates
type PolicyData struct {
	ID                      string                 `json:"id"`
	Name                    string                 `json:"name"`
	Duration                *int64                 `json:"duration,omitempty"`
	Strict                  bool                   `json:"strict"`
	Floating                bool                   `json:"floating"`
	Scheme                  string                 `json:"scheme"`
	Encrypted               bool                   `json:"encrypted"`
	Protected               bool                   `json:"protected"`
	UsePool                 bool                   `json:"use_pool"`
	RequireProductScope     bool                   `json:"require_product_scope"`
	RequirePolicyScope      bool                   `json:"require_policy_scope"`
	RequireMachineScope     bool                   `json:"require_machine_scope"`
	RequireFingerprintScope bool                   `json:"require_fingerprint_scope"`
	RequireComponentsScope  bool                   `json:"require_components_scope"`
	RequireUserScope        bool                   `json:"require_user_scope"`
	RequireChecksumScope    bool                   `json:"require_checksum_scope"`
	RequireVersionScope     bool                   `json:"require_version_scope"`
	RequireCheckIn          bool                   `json:"require_check_in"`
	RequireHeartbeat        bool                   `json:"require_heartbeat"`
	MaxMachines             *int                   `json:"max_machines,omitempty"`
	MaxProcesses            *int                   `json:"max_processes,omitempty"`
	MaxUsers                *int                   `json:"max_users,omitempty"`
	MaxCores                *int                   `json:"max_cores,omitempty"`
	MaxUses                 *int                   `json:"max_uses,omitempty"`
	Metadata                map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt               time.Time              `json:"created"`
	UpdatedAt               time.Time              `json:"updated"`
}

// CheckoutService handles license checkout operations (Ruby: LicenseCheckoutService)
// Generates cryptographically signed license certificates for client applications
// Maps directly from Ruby LicenseCheckoutService with Go improvements and type safety
type CheckoutService struct {
	abstractService *checkout.AbstractService
	cryptoService   *crypto.CryptoService
	license         *entities.License
	logger          zerolog.Logger
}

// CheckoutOptions represents options for license checkout (Ruby: initialize parameters)
type CheckoutOptions struct {
	Organization *entities.Organization `json:"organization"`        // Ruby: account
	License      *entities.License      `json:"license"`             // License to checkout
	Encrypt      bool                   `json:"encrypt"`             // Whether to encrypt the certificate
	Sign         interface{}            `json:"sign"`                // Signing configuration (bool or string algorithm)
	Algorithm    *string                `json:"algorithm,omitempty"` // Explicit algorithm override
	TTL          *time.Duration         `json:"ttl,omitempty"`       // Time-to-live for the certificate
	Include      []string               `json:"include,omitempty"`   // Relationships to include in certificate
}

// Allowed includes for license checkout (Ruby: ALLOWED_INCLUDES)
var AllowedIncludes = []string{
	"entitlements",
	"product",
	"policy",
	"owner",
	"users",
}

// Custom errors (Ruby: custom error classes)
type InvalidIncludeError struct {
	Includes []string
}

func (e *InvalidIncludeError) Error() string {
	return fmt.Sprintf("invalid includes: %v", e.Includes)
}

type InvalidLicenseError struct {
	Message string
}

func (e *InvalidLicenseError) Error() string {
	return e.Message
}

// NewCheckoutService creates a new license checkout service (Ruby: initialize)
func NewCheckoutService(cryptoService *crypto.CryptoService, options CheckoutOptions, logger zerolog.Logger) (*CheckoutService, error) {
	// Validate required parameters (Ruby: validation logic)
	if options.License == nil {
		return nil, &InvalidLicenseError{Message: "license must be present"}
	}

	if options.Organization == nil {
		return nil, &InvalidLicenseError{Message: "organization must be present"}
	}

	// Validate includes (Ruby: include validation)
	if err := validateIncludes(options.Include); err != nil {
		return nil, err
	}

	// Determine signing algorithm based on license scheme (Ruby: sign algorithm logic)
	sign := options.Sign
	if sign == nil {
		sign = determineSigningAlgorithm(options.License)
	}

	// Create abstract service with checkout options (Ruby: super call)
	abstractOptions := checkout.Options{
		Organization: options.Organization,
		Encrypt:      options.Encrypt,
		Sign:         sign,
		Algorithm:    options.Algorithm,
		TTL:          options.TTL,
		Include:      options.Include,
	}

	abstractService, err := checkout.NewAbstractService(cryptoService, abstractOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to create abstract service: %w", err)
	}

	return &CheckoutService{
		abstractService: abstractService,
		cryptoService:   cryptoService,
		license:         options.License,
		logger:          logger.With().Str("service", "license_checkout").Logger(),
	}, nil
}

// Checkout generates a license certificate file (Ruby: call method)
func (s *CheckoutService) Checkout(ctx context.Context) (*entities.LicenseFile, error) {
	s.logger.Info().
		Str("license_id", s.license.ID.String()).
		Str("algorithm", s.abstractService.GetAlgorithm()).
		Msg("Starting license checkout")

	// Calculate issue and expiry times (Ruby: issued_at and expires_at logic)
	issuedAt := time.Now()
	var expiresAt *time.Time

	if s.abstractService.HasTTL() {
		ttl := s.abstractService.GetTTL()
		expiry := issuedAt.Add(*ttl)
		expiresAt = &expiry
	}

	// Create metadata for the certificate (Ruby: meta hash)
	meta := map[string]interface{}{
		"issued": issuedAt,
		"expiry": expiresAt,
		"ttl":    s.getTTLSeconds(),
	}

	// Filter includes to only allowed ones (Ruby: incl = includes & ALLOWED_INCLUDES)
	filteredIncludes := s.filterIncludes()

	// Render license data as JSON (Ruby: renderer.render)
	licenseData, err := s.renderLicenseData(ctx, meta, filteredIncludes)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to render license data")
		return nil, fmt.Errorf("failed to render license data: %w", err)
	}

	// Encrypt or encode the data (Ruby: encrypt/encode logic)
	encodedData, err := s.encryptOrEncode(licenseData)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to encrypt/encode license data")
		return nil, fmt.Errorf("failed to encrypt/encode license data: %w", err)
	}

	// Sign the encoded data (Ruby: sign method)
	signature, err := s.abstractService.Sign(encodedData, "license")
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to sign license data")
		return nil, fmt.Errorf("failed to sign license data: %w", err)
	}

	// Create the certificate document (Ruby: doc hash)
	doc := map[string]interface{}{
		"enc": encodedData,
		"sig": signature,
		"alg": s.abstractService.GetAlgorithm(),
	}

	// Encode the document as JSON and base64 (Ruby: encode(doc.to_json))
	docJSON, err := json.Marshal(doc)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal certificate document: %w", err)
	}

	encodedDoc := s.abstractService.Encode(string(docJSON), false)

	// Create the final certificate with PEM-like format (Ruby: cert heredoc)
	certificate := fmt.Sprintf("-----BEGIN LICENSE FILE-----\n%s\n-----END LICENSE FILE-----\n", encodedDoc)

	// Create and return the license file (Ruby: LicenseFile.new)
	licenseFile := &entities.LicenseFile{
		ID:             uuid.New(),
		OrganizationID: s.license.OrganizationID,
		LicenseID:      s.license.ID,
		Certificate:    certificate,
		IssuedAt:       issuedAt,
		ExpiresAt:      expiresAt,
		TTL:            s.getTTLSeconds(),
		Includes:       filteredIncludes,
		Algorithm:      s.abstractService.GetAlgorithm(),
		License:        s.license,
		Organization:   s.abstractService.GetOrganization(),
	}

	s.logger.Info().
		Str("license_file_id", licenseFile.ID.String()).
		Str("algorithm", licenseFile.Algorithm).
		Int("includes_count", len(licenseFile.Includes)).
		Msg("License checkout completed successfully")

	return licenseFile, nil
}

// validateIncludes validates that all includes are allowed (Ruby: include validation)
func validateIncludes(includes []string) error {
	var invalidIncludes []string

	for _, include := range includes {
		if !isAllowedInclude(include) {
			invalidIncludes = append(invalidIncludes, include)
		}
	}

	if len(invalidIncludes) > 0 {
		return &InvalidIncludeError{Includes: invalidIncludes}
	}

	return nil
}

// isAllowedInclude checks if an include is in the allowed list
func isAllowedInclude(include string) bool {
	for _, allowed := range AllowedIncludes {
		if include == allowed {
			return true
		}
	}
	return false
}

// determineSigningAlgorithm determines the signing algorithm based on license policy scheme (Ruby: sign case logic)
func determineSigningAlgorithm(license *entities.License) string {
	// Get scheme from policy (Ruby: license.policy.scheme)
	if license.Policy.Scheme == nil {
		return "ed25519" // Default to ed25519 for new implementations
	}

	// Map policy scheme to signing algorithm (Ruby: case statement)
	// Note: Skipping legacy schemes as requested - using latest techniques
	switch *license.Policy.Scheme {
	case entities.CryptoSchemeRSA2048PSS:
		return "rsa-pss-sha256"
	case entities.CryptoSchemeRSA2048Sign, entities.CryptoSchemeRSA2048JWT:
		return "rsa-sha256"
	case entities.CryptoSchemeED25519:
		return "ed25519"
	default:
		return "ed25519" // Default to ed25519 for new implementations
	}
}

// filterIncludes filters includes to only allowed ones (Ruby: includes & ALLOWED_INCLUDES)
func (s *CheckoutService) filterIncludes() []string {
	requestedIncludes := s.abstractService.GetIncludes()
	var filtered []string

	for _, include := range requestedIncludes {
		if isAllowedInclude(include) {
			filtered = append(filtered, include)
		}
	}

	return filtered
}

// getTTLSeconds returns TTL in seconds or nil (Ruby: ttl conversion)
func (s *CheckoutService) getTTLSeconds() *int {
	ttl := s.abstractService.GetTTL()
	if ttl == nil {
		return nil
	}
	seconds := int(ttl.Seconds())
	return &seconds
}

// renderLicenseData renders license data as clean JSON using Go structs
func (s *CheckoutService) renderLicenseData(ctx context.Context, meta map[string]interface{}, includes []string) (string, error) {
	// Create clean license data structure
	licenseData := s.createLicenseData(includes)

	// Create the final certificate data structure
	certificateData := struct {
		Meta    map[string]interface{} `json:"meta"`
		License LicenseData            `json:"license"`
	}{
		Meta:    meta,
		License: licenseData,
	}

	// Marshal to JSON using Go's native JSON marshaling
	jsonData, err := json.Marshal(certificateData)
	if err != nil {
		return "", fmt.Errorf("failed to marshal license data: %w", err)
	}

	return string(jsonData), nil
}

// createLicenseData creates clean license data structure using Go structs
func (s *CheckoutService) createLicenseData(includes []string) LicenseData {
	license := s.license

	// Create base license data structure
	licenseData := LicenseData{
		ID:                   license.ID.String(),
		Name:                 license.Name,
		Key:                  license.Key,
		Status:               string(license.Status),
		Uses:                 license.Uses,
		Suspended:            license.Suspended,
		Scheme:               s.getSchemeString(license),
		Encrypted:            license.Policy.Encrypted,
		Strict:               license.Policy.Strict,
		Floating:             license.Policy.Floating,
		Protected:            license.IsProtected(),
		MaxMachines:          license.GetMaxMachines(),
		MaxProcesses:         license.GetMaxProcesses(),
		MaxUsers:             license.GetMaxUsers(),
		MaxCores:             license.GetMaxCores(),
		MaxUses:              license.GetMaxUses(),
		RequireHeartbeat:     license.Policy.RequireHeartbeat,
		RequireCheckIn:       license.RequiresCheckIn(),
		ExpiresAt:            license.ExpiresAt,
		LastValidatedVersion: license.LastValidatedVersion,
		LastValidatedAt:      license.LastValidatedAt,
		LastCheckInAt:        license.LastCheckInAt,
		NextCheckInAt:        license.GetNextCheckInAt(),
		LastCheckOutAt:       license.LastCheckOutAt,
		Metadata:             license.Metadata,
		CreatedAt:            license.CreatedAt,
		UpdatedAt:            license.UpdatedAt,
	}

	// Add included relationships based on includes parameter
	for _, include := range includes {
		switch include {
		case "product":
			if license.Product.ID != uuid.Nil {
				licenseData.Product = s.createProductData(&license.Product)
			}
		case "policy":
			if license.Policy.ID != uuid.Nil {
				licenseData.Policy = s.createPolicyData(&license.Policy)
			}
		}
	}

	return licenseData
}

// getSchemeString returns the scheme as string from policy (Ruby: license.scheme)
func (s *CheckoutService) getSchemeString(license *entities.License) string {
	if license.Policy.Scheme == nil {
		return string(entities.CryptoSchemeED25519) // Default
	}
	return string(*license.Policy.Scheme)
}

// encryptOrEncode encrypts or encodes the license data (Ruby: encrypt/encode logic)
func (s *CheckoutService) encryptOrEncode(data string) (string, error) {
	if s.abstractService.IsEncrypted() {
		// Encrypt using license key as secret (Ruby: encrypt(data, secret: license.key))
		return s.abstractService.Encrypt(data, s.license.Key)
	} else {
		// Just encode (Ruby: encode(data, strict: true))
		return s.abstractService.Encode(data, true), nil
	}
}

// createProductData creates clean product data structure using Go structs
func (s *CheckoutService) createProductData(product *entities.Product) *ProductData {
	return &ProductData{
		ID:        product.ID.String(),
		Name:      product.Name,
		URL:       product.URL,
		Platforms: product.Platforms,
		Metadata:  product.Metadata,
		CreatedAt: product.CreatedAt,
		UpdatedAt: product.UpdatedAt,
	}
}

// createPolicyData creates clean policy data structure using Go structs
func (s *CheckoutService) createPolicyData(policy *entities.Policy) *PolicyData {
	return &PolicyData{
		ID:                      policy.ID.String(),
		Name:                    policy.Name,
		Duration:                policy.Duration,
		Strict:                  policy.Strict,
		Floating:                policy.Floating,
		Scheme:                  s.getPolicySchemeString(policy),
		Encrypted:               policy.Encrypted,
		Protected:               policy.Protected,
		UsePool:                 policy.UsePool,
		RequireProductScope:     policy.RequireProductScope,
		RequirePolicyScope:      policy.RequirePolicyScope,
		RequireMachineScope:     policy.RequireMachineScope,
		RequireFingerprintScope: policy.RequireFingerprintScope,
		RequireComponentsScope:  policy.RequireComponentsScope,
		RequireUserScope:        policy.RequireUserScope,
		RequireChecksumScope:    policy.RequireChecksumScope,
		RequireVersionScope:     policy.RequireVersionScope,
		RequireCheckIn:          policy.RequireCheckIn,
		RequireHeartbeat:        policy.RequireHeartbeat,
		MaxMachines:             policy.MaxMachines,
		MaxProcesses:            policy.MaxProcesses,
		MaxUsers:                policy.MaxUsers,
		MaxCores:                policy.MaxCores,
		MaxUses:                 policy.MaxUses,
		Metadata:                policy.Metadata,
		CreatedAt:               policy.CreatedAt,
		UpdatedAt:               policy.UpdatedAt,
	}
}

// getPolicySchemeString returns the policy scheme as string
func (s *CheckoutService) getPolicySchemeString(policy *entities.Policy) string {
	if policy.Scheme == nil {
		return string(entities.CryptoSchemeED25519) // Default
	}
	return string(*policy.Scheme)
}
