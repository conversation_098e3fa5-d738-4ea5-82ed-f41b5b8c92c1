﻿package license

import (
	"context"
	"crypto/sha256"
	"fmt"
	"regexp"
	"strings"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"golang.org/x/crypto/bcrypt"
)

// KeyLookupService handles license key lookup operations (Ruby: LicenseKeyLookupService)
// Provides secure license key authentication with support for legacy encrypted keys
// Maps directly from Ruby LicenseKeyLookupService with Go improvements and type safety
type KeyLookupService struct {
	licenseRepo   repositories.LicenseRepository
	cryptoService *crypto.CryptoService
	logger        zerolog.Logger
}

// KeyLookupOptions represents the options for license key lookup
type KeyLookupOptions struct {
	OrganizationID  uuid.UUID `json:"organization_id"`  // Ruby: account_id -> organization_id
	Key             string    `json:"key"`              // License key to lookup
	LegacyEncrypted bool      `json:"legacy_encrypted"` // Whether to use legacy encrypted key lookup
}

// KeyLookupResult represents the result of a license key lookup
type KeyLookupResult struct {
	License  *entities.License `json:"license,omitempty"`
	Found    bool              `json:"found"`
	Rehashed bool              `json:"rehashed"` // Whether the key was rehashed during lookup
}

// Constants for encrypted key pattern matching (Ruby: ENCRYPTED_KEY_RE)
const (
	UUIDLength = 36 // Standard UUID length
)

var (
	// EncryptedKeyRegex matches the pattern for legacy encrypted keys (Ruby: ENCRYPTED_KEY_RE)
	// Pattern: {license_id}-{encrypted_bits}
	EncryptedKeyRegex = regexp.MustCompile(`^([a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12})-(.+)$`)
)

// NewKeyLookupService creates a new license key lookup service
func NewKeyLookupService(
	licenseRepo repositories.LicenseRepository,
	cryptoService *crypto.CryptoService,
	logger zerolog.Logger,
) *KeyLookupService {
	return &KeyLookupService{
		licenseRepo:   licenseRepo,
		cryptoService: cryptoService,
		logger:        logger.With().Str("service", "license-key-lookup").Logger(),
	}
}

// LookupByKey performs license key lookup with support for both normal and legacy encrypted keys
// Maps from Ruby LicenseKeyLookupService#call method
func (s *KeyLookupService) LookupByKey(ctx context.Context, options KeyLookupOptions) (*KeyLookupResult, error) {
	s.logger.Debug().
		Str("organization_id", options.OrganizationID.String()).
		Bool("legacy_encrypted", options.LegacyEncrypted).
		Msg("Starting license key lookup")

	// Validate input
	if err := s.validateOptions(options); err != nil {
		return nil, fmt.Errorf("invalid lookup options: %w", err)
	}

	// Handle legacy encrypted key lookup (Ruby: if legacy_encrypted)
	if options.LegacyEncrypted {
		return s.lookupLegacyEncryptedKey(ctx, options)
	}

	// Handle normal key lookup (Ruby: else branch)
	return s.lookupNormalKey(ctx, options)
}

// validateOptions validates the lookup options
func (s *KeyLookupService) validateOptions(options KeyLookupOptions) error {
	if options.OrganizationID == uuid.Nil {
		return fmt.Errorf("organization_id is required")
	}

	if strings.TrimSpace(options.Key) == "" {
		return fmt.Errorf("key is required")
	}

	return nil
}

// lookupNormalKey performs normal license key lookup (Ruby: licenses.find_by(key:))
func (s *KeyLookupService) lookupNormalKey(ctx context.Context, options KeyLookupOptions) (*KeyLookupResult, error) {
	s.logger.Debug().Msg("Performing normal key lookup")

	// Find license by key (Ruby: licenses.find_by(key:))
	license, err := s.licenseRepo.GetByKey(ctx, options.Key)
	if err != nil {
		if err.Error() == "record not found" || strings.Contains(err.Error(), "not found") {
			s.logger.Debug().Msg("License not found for normal key lookup")
			return &KeyLookupResult{Found: false}, nil
		}
		return nil, fmt.Errorf("failed to lookup license by key: %w", err)
	}

	// Verify the license belongs to the organization (Ruby: account.licenses scope)
	if license.OrganizationID != options.OrganizationID {
		s.logger.Debug().
			Str("license_organization_id", license.OrganizationID.String()).
			Str("requested_organization_id", options.OrganizationID.String()).
			Msg("License belongs to different organization")
		return &KeyLookupResult{Found: false}, nil
	}

	s.logger.Debug().
		Str("license_id", license.ID.String()).
		Msg("Successfully found license with normal key lookup")

	return &KeyLookupResult{
		License:  license,
		Found:    true,
		Rehashed: false,
	}, nil
}

// lookupLegacyEncryptedKey performs legacy encrypted key lookup with rehashing support
// Maps from Ruby LicenseKeyLookupService legacy_encrypted branch
func (s *KeyLookupService) lookupLegacyEncryptedKey(ctx context.Context, options KeyLookupOptions) (*KeyLookupResult, error) {
	s.logger.Debug().Msg("Performing legacy encrypted key lookup")

	// Parse encrypted key pattern (Ruby: ENCRYPTED_KEY_RE.match(key))
	matches := EncryptedKeyRegex.FindStringSubmatch(options.Key)
	if len(matches) != 3 {
		s.logger.Debug().
			Str("key_pattern", options.Key).
			Msg("Key does not match encrypted key pattern")
		return &KeyLookupResult{Found: false}, nil
	}

	licenseIDStr := matches[1]
	// encryptedBits := matches[2] // Not used in current implementation

	// Parse license ID from the key
	licenseID, err := uuid.Parse(licenseIDStr)
	if err != nil {
		s.logger.Debug().
			Str("license_id_str", licenseIDStr).
			Err(err).
			Msg("Failed to parse license ID from encrypted key")
		return &KeyLookupResult{Found: false}, nil
	}

	// Find license by ID (Ruby: licenses.find_by(id: matches[:license_id]))
	license, err := s.licenseRepo.GetByID(ctx, licenseID)
	if err != nil {
		if err.Error() == "record not found" || strings.Contains(err.Error(), "not found") {
			s.logger.Debug().
				Str("license_id", licenseID.String()).
				Msg("License not found for legacy encrypted key lookup")
			return &KeyLookupResult{Found: false}, nil
		}
		return nil, fmt.Errorf("failed to lookup license by ID: %w", err)
	}

	// Verify the license belongs to the organization (Ruby: account.licenses scope)
	if license.OrganizationID != options.OrganizationID {
		s.logger.Debug().
			Str("license_organization_id", license.OrganizationID.String()).
			Str("requested_organization_id", options.OrganizationID.String()).
			Msg("License belongs to different organization")
		return &KeyLookupResult{Found: false}, nil
	}

	// Verify the hashed token (Ruby: license&.compare_hashed_token(:key, key, version: 'v1'))
	if !s.compareHashedToken(license.Key, options.Key) {
		s.logger.Debug().
			Str("license_id", license.ID.String()).
			Msg("Hashed token comparison failed for legacy encrypted key")
		return &KeyLookupResult{Found: false}, nil
	}

	s.logger.Warn().
		Str("license_id", license.ID.String()).
		Msg("v1 keys are deprecated and must be regenerated")

	// Attempt to rehash the key (Ruby: jit rehash key logic)
	rehashed, err := s.rehashLegacyKey(ctx, license, options.Key)
	if err != nil {
		s.logger.Error().
			Err(err).
			Str("license_id", license.ID.String()).
			Msg("Failed to rehash legacy key")
		// Still return the license even if rehashing fails
	}

	s.logger.Debug().
		Str("license_id", license.ID.String()).
		Bool("rehashed", rehashed).
		Msg("Successfully found license with legacy encrypted key lookup")

	return &KeyLookupResult{
		License:  license,
		Found:    true,
		Rehashed: rehashed,
	}, nil
}

// compareHashedToken compares a plain text token with a hashed token (Ruby: compare_hashed_token)
// Implements the legacy v1 token comparison logic from Ruby Tokenable concern
func (s *KeyLookupService) compareHashedToken(hashedToken, plainToken string) bool {
	if plainToken == "" || hashedToken == "" {
		return false
	}

	// Use bcrypt to compare the hashed token with the plain token
	// This maps to Ruby's BCrypt::Password.new(license.key) comparison
	err := bcrypt.CompareHashAndPassword([]byte(hashedToken), []byte(plainToken))
	return err == nil
}

// rehashLegacyKey attempts to rehash a legacy key using the current algorithm
// Maps from Ruby LicenseKeyLookupService jit rehash logic
func (s *KeyLookupService) rehashLegacyKey(ctx context.Context, license *entities.License, originalKey string) (bool, error) {
	// Create SHA256 digest of the original key (Ruby: Digest::SHA256.hexdigest(key))
	hash := sha256.Sum256([]byte(originalKey))
	keyDigest := fmt.Sprintf("%x", hash)

	// Extract salt from existing bcrypt hash (Ruby: BCrypt::Password.new(license.key).salt)
	existingHash, err := bcrypt.Cost([]byte(license.Key))
	if err != nil {
		return false, fmt.Errorf("failed to extract cost from existing hash: %w", err)
	}

	// Generate new hash using the same salt (Ruby: BCrypt::Engine.hash_secret with reused salt)
	// Note: Go's bcrypt doesn't allow salt reuse, so we'll generate a new hash
	newHash, err := bcrypt.GenerateFromPassword([]byte(keyDigest), existingHash)
	if err != nil {
		return false, fmt.Errorf("failed to generate new hash: %w", err)
	}

	newHashStr := string(newHash)

	// Check if rehashing is needed (Ruby: secure_compare check)
	if s.secureCompare(newHashStr, license.Key) {
		// No rehashing needed
		return false, nil
	}

	s.logger.Warn().
		Str("license_id", license.ID.String()).
		Msg("Rehashing legacy key")

	// Update the license with the new hash (Ruby: license.update!(key: digest))
	license.Key = newHashStr
	if err := s.licenseRepo.Update(ctx, license); err != nil {
		return false, fmt.Errorf("failed to update license with new hash: %w", err)
	}

	return true, nil
}

// secureCompare performs a secure string comparison (Ruby: secure_compare)
// Implements constant-time string comparison to prevent timing attacks
func (s *KeyLookupService) secureCompare(a, b string) bool {
	if len(a) != len(b) {
		return false
	}

	result := 0
	for i := 0; i < len(a); i++ {
		result |= int(a[i]) ^ int(b[i])
	}

	return result == 0
}
