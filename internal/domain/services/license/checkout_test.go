package license

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Test helper functions
func createTestOrganization() *entities.Organization {
	privateKey := "test-private-key"
	ed25519Key := "test-ed25519-key"

	return &entities.Organization{
		ID:                uuid.New(),
		Name:              "Test Organization",
		PrivateKey:        &privateKey,
		Ed25519PrivateKey: &ed25519Key,
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}
}

func createTestProduct() *entities.Product {
	return &entities.Product{
		ID:             uuid.New(),
		Name:           "Test Product",
		OrganizationID: uuid.New(),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

func createTestPolicy() *entities.Policy {
	scheme := entities.CryptoSchemeED25519
	maxMachines := 10
	maxUsers := 5

	return &entities.Policy{
		ID:             uuid.New(),
		Name:           "Test Policy",
		OrganizationID: uuid.New(),
		ProductID:      uuid.New(),
		Scheme:         &scheme,
		Encrypted:      true,
		Strict:         false,
		Floating:       true,
		MaxMachines:    &maxMachines,
		MaxUsers:       &maxUsers,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}
}

func createTestLicenseForCheckout() *entities.License {
	policy := createTestPolicy()
	product := createTestProduct()

	return &entities.License{
		ID:             uuid.New(),
		OrganizationID: policy.OrganizationID,
		ProductID:      product.ID,
		PolicyID:       policy.ID,
		Key:            "TEST-LICENSE-KEY-12345",
		Name:           &[]string{"Test License"}[0],
		OwnerType:      entities.LicenseOwnerTypeOrganization,
		OwnerID:        uuid.New(),
		Status:         entities.LicenseStatusActive,
		Suspended:      false,
		Uses:           0,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		Policy:         *policy,
		Product:        *product,
	}
}

func TestNewCheckoutService(t *testing.T) {
	cryptoService := crypto.NewCryptoService()
	logger := zerolog.New(nil).Level(zerolog.Disabled)

	t.Run("successful creation", func(t *testing.T) {
		organization := createTestOrganization()
		license := createTestLicenseForCheckout()

		options := CheckoutOptions{
			Organization: organization,
			License:      license,
			Encrypt:      true,
			Include:      []string{"product", "policy"},
		}

		service, err := NewCheckoutService(cryptoService, options, logger)

		assert.NoError(t, err)
		assert.NotNil(t, service)
		assert.Equal(t, license, service.license)
	})

	t.Run("missing license error", func(t *testing.T) {
		organization := createTestOrganization()

		options := CheckoutOptions{
			Organization: organization,
			License:      nil,
		}

		service, err := NewCheckoutService(cryptoService, options, logger)

		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "license must be present")
	})

	t.Run("missing organization error", func(t *testing.T) {
		license := createTestLicenseForCheckout()

		options := CheckoutOptions{
			Organization: nil,
			License:      license,
		}

		service, err := NewCheckoutService(cryptoService, options, logger)

		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "organization must be present")
	})

	t.Run("invalid includes error", func(t *testing.T) {
		organization := createTestOrganization()
		license := createTestLicenseForCheckout()

		options := CheckoutOptions{
			Organization: organization,
			License:      license,
			Include:      []string{"invalid_include", "another_invalid"},
		}

		service, err := NewCheckoutService(cryptoService, options, logger)

		assert.Error(t, err)
		assert.Nil(t, service)
		assert.Contains(t, err.Error(), "invalid includes")
	})
}

func TestValidateIncludes(t *testing.T) {
	t.Run("valid includes", func(t *testing.T) {
		includes := []string{"product", "policy", "entitlements"}
		err := validateIncludes(includes)
		assert.NoError(t, err)
	})

	t.Run("invalid includes", func(t *testing.T) {
		includes := []string{"product", "invalid", "policy", "another_invalid"}
		err := validateIncludes(includes)

		assert.Error(t, err)
		invalidErr, ok := err.(*InvalidIncludeError)
		assert.True(t, ok)
		assert.Contains(t, invalidErr.Includes, "invalid")
		assert.Contains(t, invalidErr.Includes, "another_invalid")
	})

	t.Run("empty includes", func(t *testing.T) {
		includes := []string{}
		err := validateIncludes(includes)
		assert.NoError(t, err)
	})
}

func TestDetermineSigningAlgorithm(t *testing.T) {
	t.Run("ED25519 scheme", func(t *testing.T) {
		license := createTestLicenseForCheckout()
		scheme := entities.CryptoSchemeED25519
		license.Policy.Scheme = &scheme

		algorithm := determineSigningAlgorithm(license)
		assert.Equal(t, "ed25519", algorithm)
	})

	t.Run("RSA PSS scheme", func(t *testing.T) {
		license := createTestLicenseForCheckout()
		scheme := entities.CryptoSchemeRSA2048PSS
		license.Policy.Scheme = &scheme

		algorithm := determineSigningAlgorithm(license)
		assert.Equal(t, "rsa-pss-sha256", algorithm)
	})

	t.Run("RSA Sign scheme", func(t *testing.T) {
		license := createTestLicenseForCheckout()
		scheme := entities.CryptoSchemeRSA2048Sign
		license.Policy.Scheme = &scheme

		algorithm := determineSigningAlgorithm(license)
		assert.Equal(t, "rsa-sha256", algorithm)
	})

	t.Run("no scheme defaults to ED25519", func(t *testing.T) {
		license := createTestLicenseForCheckout()
		license.Policy.Scheme = nil

		algorithm := determineSigningAlgorithm(license)
		assert.Equal(t, "ed25519", algorithm)
	})
}

func TestCheckoutService_CreateLicenseData(t *testing.T) {
	cryptoService := crypto.NewCryptoService()
	logger := zerolog.New(nil).Level(zerolog.Disabled)

	organization := createTestOrganization()
	license := createTestLicenseForCheckout()

	options := CheckoutOptions{
		Organization: organization,
		License:      license,
		Encrypt:      false,
		Include:      []string{"product", "policy"},
	}

	service, err := NewCheckoutService(cryptoService, options, logger)
	require.NoError(t, err)

	t.Run("basic license data creation", func(t *testing.T) {
		licenseData := service.createLicenseData([]string{})

		assert.Equal(t, license.ID.String(), licenseData.ID)
		assert.Equal(t, license.Key, licenseData.Key)
		assert.Equal(t, string(license.Status), licenseData.Status)
		assert.Equal(t, license.Uses, licenseData.Uses)
		assert.Equal(t, license.Suspended, licenseData.Suspended)
		assert.Nil(t, licenseData.Product) // No includes
		assert.Nil(t, licenseData.Policy)  // No includes
	})

	t.Run("license data with includes", func(t *testing.T) {
		includes := []string{"product", "policy"}
		licenseData := service.createLicenseData(includes)

		// Should have included relationships
		assert.NotNil(t, licenseData.Product)
		assert.NotNil(t, licenseData.Policy)

		// Verify product data
		assert.Equal(t, license.Product.ID.String(), licenseData.Product.ID)
		assert.Equal(t, license.Product.Name, licenseData.Product.Name)

		// Verify policy data
		assert.Equal(t, license.Policy.ID.String(), licenseData.Policy.ID)
		assert.Equal(t, license.Policy.Name, licenseData.Policy.Name)
	})
}

func TestCheckoutService_Checkout_Integration(t *testing.T) {
	cryptoService := crypto.NewCryptoService()
	logger := zerolog.New(nil).Level(zerolog.Disabled)

	organization := createTestOrganization()
	license := createTestLicenseForCheckout()

	t.Run("basic checkout without encryption", func(t *testing.T) {
		options := CheckoutOptions{
			Organization: organization,
			License:      license,
			Encrypt:      false,
			Include:      []string{"product"},
		}

		service, err := NewCheckoutService(cryptoService, options, logger)
		require.NoError(t, err)

		ctx := context.Background()
		licenseFile, err := service.Checkout(ctx)

		assert.NoError(t, err)
		assert.NotNil(t, licenseFile)

		// Verify license file structure
		assert.Equal(t, license.ID, licenseFile.LicenseID)
		assert.Equal(t, organization.ID, licenseFile.OrganizationID)
		assert.NotEmpty(t, licenseFile.Certificate)
		assert.Contains(t, licenseFile.Certificate, "-----BEGIN LICENSE FILE-----")
		assert.Contains(t, licenseFile.Certificate, "-----END LICENSE FILE-----")
		assert.Contains(t, licenseFile.Includes, "product")

		// Verify certificate is valid JSON structure
		lines := strings.Split(licenseFile.Certificate, "\n")
		var certContent string
		for _, line := range lines {
			if !strings.Contains(line, "-----") && strings.TrimSpace(line) != "" {
				certContent += line
			}
		}

		// Should be valid base64 encoded JSON
		assert.NotEmpty(t, certContent)
	})

	t.Run("checkout with TTL", func(t *testing.T) {
		ttl := 24 * time.Hour
		options := CheckoutOptions{
			Organization: organization,
			License:      license,
			Encrypt:      false,
			TTL:          &ttl,
		}

		service, err := NewCheckoutService(cryptoService, options, logger)
		require.NoError(t, err)

		ctx := context.Background()
		licenseFile, err := service.Checkout(ctx)

		assert.NoError(t, err)
		assert.NotNil(t, licenseFile)
		assert.NotNil(t, licenseFile.ExpiresAt)
		assert.NotNil(t, licenseFile.TTL)
		assert.Equal(t, int(ttl.Seconds()), *licenseFile.TTL)
	})
}
