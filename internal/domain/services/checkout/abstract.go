package checkout

import (
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
)

// Abstract checkout service base (maps to Ruby AbstractCheckoutService)
type AbstractService struct {
	crypto       *crypto.CryptoService
	organization *entities.Organization
	algorithm    string
	ttl          *time.Duration
	includes     []string
	privateKey   string
}

// Algorithm constants (maps to Ruby constants)
const (
	DefaultEncryptionAlgorithm = "aes-256-gcm"
	DefaultEncodingAlgorithm   = "base64"
	DefaultSigningAlgorithm    = "ed25519"
)

// Supported algorithms
var (
	EncryptionAlgorithms = []string{"aes-256-gcm"}
	EncodingAlgorithms   = []string{"base64"}
	SigningAlgorithms    = []string{"ed25519", "rsa-pss-sha256", "rsa-sha256"}
)

// Options for checkout services
type Options struct {
	Organization *entities.Organization `json:"organization"`
	Encrypt      bool                   `json:"encrypt"`
	Sign         interface{}            `json:"sign"` // can be bool or string algorithm
	Algorithm    *string                `json:"algorithm,omitempty"`
	TTL          *time.Duration         `json:"ttl,omitempty"`
	Include      []string               `json:"include,omitempty"`
}

// NewAbstractService creates a new abstract checkout service
func NewAbstractService(crypto *crypto.CryptoService, options Options) (*AbstractService, error) {
	if options.Organization == nil {
		return nil, fmt.Errorf("organization must be present")
	}

	// Validate TTL (must be >= 1 hour)
	if options.TTL != nil && *options.TTL < time.Hour {
		return nil, fmt.Errorf("TTL must be greater than or equal to 3600 seconds (1 hour)")
	}

	// Determine algorithm
	algorithm := ""
	if options.Algorithm != nil && *options.Algorithm != "" {
		algorithm = *options.Algorithm
	} else {
		// Build default algorithm (maps to Ruby algorithm building logic)
		enc := DefaultEncodingAlgorithm
		if options.Encrypt {
			enc = DefaultEncryptionAlgorithm
		}

		sig := DefaultSigningAlgorithm
		if options.Sign != nil {
			switch s := options.Sign.(type) {
			case bool:
				if !s {
					sig = ""
				}
			case string:
				if s != "" {
					sig = s
				}
			}
		}

		if sig != "" {
			algorithm = fmt.Sprintf("%s+%s", enc, sig)
		} else {
			algorithm = enc
		}
	}

	// Validate algorithm parts
	parts := strings.Split(algorithm, "+")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid algorithm format")
	}

	encAlg, sigAlg := parts[0], parts[1]

	// Validate encryption/encoding algorithm
	if !contains(EncryptionAlgorithms, encAlg) && !contains(EncodingAlgorithms, encAlg) {
		return nil, fmt.Errorf("invalid encoding algorithm: %s", encAlg)
	}

	// Validate signing algorithm
	if !contains(SigningAlgorithms, sigAlg) {
		return nil, fmt.Errorf("invalid signing algorithm: %s", sigAlg)
	}

	// Get private key based on algorithm
	privateKey := ""
	switch sigAlg {
	case "ed25519":
		privateKey = *options.Organization.Ed25519PrivateKey
	case "rsa-pss-sha256", "rsa-sha256":
		privateKey = *options.Organization.PrivateKey
	}

	return &AbstractService{
		crypto:       crypto,
		organization: options.Organization,
		algorithm:    algorithm,
		ttl:          options.TTL,
		includes:     options.Include,
		privateKey:   privateKey,
	}, nil
}

// Algorithm parsing methods (maps to Ruby algorithm_parts methods)
func (as *AbstractService) AlgorithmParts() []string {
	return strings.Split(as.algorithm, "+")
}

func (as *AbstractService) EncryptionAlgorithm() string {
	parts := as.AlgorithmParts()
	if len(parts) > 0 {
		return parts[0]
	}
	return ""
}

func (as *AbstractService) SigningAlgorithm() string {
	parts := as.AlgorithmParts()
	if len(parts) > 1 {
		return parts[1]
	}
	return ""
}

// Algorithm type checks (maps to Ruby boolean methods)
func (as *AbstractService) IsEncrypted() bool {
	return contains(EncryptionAlgorithms, as.EncryptionAlgorithm())
}

func (as *AbstractService) IsEncoded() bool {
	return contains(EncodingAlgorithms, as.EncryptionAlgorithm())
}

func (as *AbstractService) IsEd25519() bool {
	return as.SigningAlgorithm() == "ed25519"
}

func (as *AbstractService) IsRSA() bool {
	alg := as.SigningAlgorithm()
	return alg == "rsa-pss-sha256" || alg == "rsa-sha256"
}

func (as *AbstractService) HasTTL() bool {
	return as.ttl != nil
}

// Encrypt data using AES-256-GCM (maps to Ruby encrypt method)
func (as *AbstractService) Encrypt(value, secret string) (string, error) {
	if !as.IsEncrypted() {
		return "", fmt.Errorf("encryption not enabled for this algorithm")
	}

	// Use AES service for encryption
	encrypted, err := as.crypto.AES.Encrypt(base64.StdEncoding.EncodeToString([]byte(secret)), []byte(value))
	if err != nil {
		return "", err
	}

	// Return combined format for compatibility
	return encrypted.Nonce + ":" + encrypted.Ciphertext, nil
}

// Encode data using base64 (maps to Ruby encode method)
func (as *AbstractService) Encode(value string, strict bool) string {
	if strict {
		return base64.StdEncoding.EncodeToString([]byte(value))
	}

	encoded := base64.StdEncoding.EncodeToString([]byte(value))
	return strings.TrimRight(encoded, "\n")
}

// Sign data using the configured algorithm (maps to Ruby sign method)
func (as *AbstractService) Sign(value, prefix string) (string, error) {
	data := fmt.Sprintf("%s/%s", prefix, value)

	switch as.SigningAlgorithm() {
	case "ed25519":
		if as.privateKey == "" {
			return "", fmt.Errorf("Ed25519 private key not available")
		}
		signature, err := as.crypto.Ed25519.Sign(data, []byte(as.privateKey))
		if err != nil {
			return "", err
		}
		return signature, nil // Ed25519.Sign already returns base64 encoded string

	case "rsa-pss-sha256":
		if as.privateKey == "" {
			return "", fmt.Errorf("RSA private key not available")
		}
		// PSS signing uses same RSA.Sign method with different padding
		signature, err := as.crypto.RSA.Sign(as.privateKey, []byte(data))
		if err != nil {
			return "", err
		}
		return signature, nil // RSA.Sign already returns base64 encoded string

	case "rsa-sha256":
		if as.privateKey == "" {
			return "", fmt.Errorf("RSA private key not available")
		}
		signature, err := as.crypto.RSA.Sign(as.privateKey, []byte(data))
		if err != nil {
			return "", err
		}
		return signature, nil // RSA.Sign already returns base64 encoded string

	default:
		return "", fmt.Errorf("signing algorithm not supported: %s", as.SigningAlgorithm())
	}
}

// GetOrganization returns the organization
func (as *AbstractService) GetOrganization() *entities.Organization {
	return as.organization
}

// GetAlgorithm returns the algorithm string
func (as *AbstractService) GetAlgorithm() string {
	return as.algorithm
}

// GetTTL returns the TTL duration
func (as *AbstractService) GetTTL() *time.Duration {
	return as.ttl
}

// GetIncludes returns the includes slice
func (as *AbstractService) GetIncludes() []string {
	return as.includes
}

// Helper function to check if slice contains string
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
