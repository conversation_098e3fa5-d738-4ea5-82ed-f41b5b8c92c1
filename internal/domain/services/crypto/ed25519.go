package crypto

import (
	"crypto/ed25519"
	"crypto/rand"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
)

// Ed25519Service provides Ed25519 cryptographic operations
type Ed25519Service struct{}

// NewEd25519Service creates a new Ed25519 service instance
func NewEd25519Service() *Ed25519Service {
	return &Ed25519Service{}
}

// Ed25519KeyPair represents an Ed25519 key pair
type Ed25519KeyPair struct {
	PrivateKey string `json:"private_key"`
	PublicKey  string `json:"public_key"`
}

// GenerateKeyPair generates a new Ed25519 key pair
func (es *Ed25519Service) GenerateKeyPair() (*Ed25519KeyPair, error) {
	// Generate Ed25519 key pair
	publicKey, privateKey, err := ed25519.GenerateKey(rand.Reader)
	if err != nil {
		return nil, fmt.Errorf("failed to generate Ed25519 key pair: %w", err)
	}

	// Encode private key to PEM format
	privateKeyBytes, err := x509.MarshalPKCS8PrivateKey(privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal private key: %w", err)
	}

	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	// Encode public key to PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal public key: %w", err)
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return &Ed25519KeyPair{
		PrivateKey: string(privateKeyPEM),
		PublicKey:  string(publicKeyPEM),
	}, nil
}

// Sign signs data using Ed25519
func (es *Ed25519Service) Sign(privateKeyPEM string, data []byte) (string, error) {
	// Parse private key
	privateKey, err := es.parsePrivateKey(privateKeyPEM)
	if err != nil {
		return "", err
	}

	// Sign the data
	signature := ed25519.Sign(privateKey, data)

	return base64.StdEncoding.EncodeToString(signature), nil
}

// Verify verifies a signature using Ed25519
func (es *Ed25519Service) Verify(publicKeyPEM string, data []byte, signatureB64 string) error {
	// Parse public key
	publicKey, err := es.parsePublicKey(publicKeyPEM)
	if err != nil {
		return err
	}

	// Decode signature
	signature, err := base64.StdEncoding.DecodeString(signatureB64)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %w", err)
	}

	// Verify signature
	if !ed25519.Verify(publicKey, data, signature) {
		return fmt.Errorf("Ed25519 signature verification failed")
	}

	return nil
}

// ValidateKeyPair validates that a private and public key form a valid Ed25519 pair
func (es *Ed25519Service) ValidateKeyPair(privateKeyPEM, publicKeyPEM string) error {
	// Parse both keys
	privateKey, err := es.parsePrivateKey(privateKeyPEM)
	if err != nil {
		return err
	}

	publicKey, err := es.parsePublicKey(publicKeyPEM)
	if err != nil {
		return err
	}

	// Extract public key from private key
	derivedPublicKey := privateKey.Public().(ed25519.PublicKey)

	// Compare the public keys
	if !derivedPublicKey.Equal(publicKey) {
		return fmt.Errorf("private and public keys do not form a valid Ed25519 pair")
	}

	return nil
}

// GetPublicKeyFromPrivate extracts the public key from an Ed25519 private key
func (es *Ed25519Service) GetPublicKeyFromPrivate(privateKeyPEM string) (string, error) {
	// Parse private key
	privateKey, err := es.parsePrivateKey(privateKeyPEM)
	if err != nil {
		return "", err
	}

	// Extract public key
	publicKey := privateKey.Public().(ed25519.PublicKey)

	// Encode public key to PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return "", fmt.Errorf("failed to marshal public key: %w", err)
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return string(publicKeyPEM), nil
}

// parsePrivateKey parses a PEM-encoded Ed25519 private key
func (es *Ed25519Service) parsePrivateKey(privateKeyPEM string) (ed25519.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	// Parse PKCS8 private key
	privateKeyInterface, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	privateKey, ok := privateKeyInterface.(ed25519.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("key is not an Ed25519 private key")
	}

	// Validate key size (Ed25519 private keys are always 64 bytes)
	if len(privateKey) != ed25519.PrivateKeySize {
		return nil, fmt.Errorf("invalid Ed25519 private key size: %d bytes", len(privateKey))
	}

	return privateKey, nil
}

// parsePublicKey parses a PEM-encoded Ed25519 public key
func (es *Ed25519Service) parsePublicKey(publicKeyPEM string) (ed25519.PublicKey, error) {
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	publicKeyInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	publicKey, ok := publicKeyInterface.(ed25519.PublicKey)
	if !ok {
		return nil, fmt.Errorf("key is not an Ed25519 public key")
	}

	// Validate key size (Ed25519 public keys are always 32 bytes)
	if len(publicKey) != ed25519.PublicKeySize {
		return nil, fmt.Errorf("invalid Ed25519 public key size: %d bytes", len(publicKey))
	}

	return publicKey, nil
}

// GetKeyInfo returns information about an Ed25519 key
func (es *Ed25519Service) GetKeyInfo(keyPEM string) (map[string]interface{}, error) {
	info := make(map[string]interface{})

	// Try to parse as private key first
	if privateKey, err := es.parsePrivateKey(keyPEM); err == nil {
		info["type"] = "private"
		info["size"] = len(privateKey) * 8 // Convert bytes to bits
		info["algorithm"] = "Ed25519"
		info["curve"] = "edwards25519"
		return info, nil
	}

	// Try to parse as public key
	if publicKey, err := es.parsePublicKey(keyPEM); err == nil {
		info["type"] = "public"
		info["size"] = len(publicKey) * 8 // Convert bytes to bits
		info["algorithm"] = "Ed25519"
		info["curve"] = "edwards25519"
		return info, nil
	}

	return nil, fmt.Errorf("invalid Ed25519 key format")
}

// GenerateKeyPairRaw generates a raw Ed25519 key pair (without PEM encoding)
func (es *Ed25519Service) GenerateKeyPairRaw() (ed25519.PublicKey, ed25519.PrivateKey, error) {
	return ed25519.GenerateKey(rand.Reader)
}

// SignRaw signs data using a raw Ed25519 private key
func (es *Ed25519Service) SignRaw(privateKey ed25519.PrivateKey, data []byte) []byte {
	return ed25519.Sign(privateKey, data)
}

// VerifyRaw verifies a signature using a raw Ed25519 public key
func (es *Ed25519Service) VerifyRaw(publicKey ed25519.PublicKey, data, signature []byte) bool {
	return ed25519.Verify(publicKey, data, signature)
}

// EncodeToPEM encodes raw keys to PEM format
func (es *Ed25519Service) EncodeToPEM(key interface{}) (string, error) {
	var keyBytes []byte
	var keyType string
	var err error

	switch k := key.(type) {
	case ed25519.PrivateKey:
		keyBytes, err = x509.MarshalPKCS8PrivateKey(k)
		keyType = "PRIVATE KEY"
	case ed25519.PublicKey:
		keyBytes, err = x509.MarshalPKIXPublicKey(k)
		keyType = "PUBLIC KEY"
	default:
		return "", fmt.Errorf("unsupported key type for Ed25519 encoding")
	}

	if err != nil {
		return "", fmt.Errorf("failed to marshal key: %w", err)
	}

	pemBytes := pem.EncodeToMemory(&pem.Block{
		Type:  keyType,
		Bytes: keyBytes,
	})

	return string(pemBytes), nil
}

// CreateSeed generates a cryptographically secure 32-byte seed for Ed25519 key derivation
func (es *Ed25519Service) CreateSeed() ([]byte, error) {
	seed := make([]byte, ed25519.SeedSize)
	if _, err := rand.Read(seed); err != nil {
		return nil, fmt.Errorf("failed to generate Ed25519 seed: %w", err)
	}
	return seed, nil
}

// NewKeyFromSeed creates an Ed25519 private key from a seed
func (es *Ed25519Service) NewKeyFromSeed(seed []byte) (ed25519.PrivateKey, error) {
	if len(seed) != ed25519.SeedSize {
		return nil, fmt.Errorf("invalid seed size: expected %d bytes, got %d", ed25519.SeedSize, len(seed))
	}
	return ed25519.NewKeyFromSeed(seed), nil
}