package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io"
)

// AESService provides AES-256-GCM encryption operations
type AESService struct {
	keySize   int
	nonceSize int
}

// NewAESService creates a new AES-256-GCM service instance
func NewAESService() *AESService {
	return &AESService{
		keySize:   32, // 256 bits
		nonceSize: 12, // 96 bits (recommended for GCM)
	}
}

// EncryptedData represents encrypted data with metadata
type EncryptedData struct {
	Ciphertext string `json:"ciphertext"`
	Nonce      string `json:"nonce"`
	AuthTag    string `json:"auth_tag,omitempty"` // Included in ciphertext for GCM
}

// GenerateKey generates a new 256-bit AES key
func (as *AESService) GenerateKey() (string, error) {
	key := make([]byte, as.keySize)
	if _, err := io.ReadFull(rand.Reader, key); err != nil {
		return "", fmt.Errorf("failed to generate AES key: %w", err)
	}
	return base64.StdEncoding.EncodeToString(key), nil
}

// DeriveKey derives a 256-bit AES key from a password using SHA-256
func (as *AESService) DeriveKey(password string, salt []byte) string {
	// Simple key derivation using SHA-256 (consider using PBKDF2 or Argon2 for production)
	hash := sha256.Sum256(append([]byte(password), salt...))
	return base64.StdEncoding.EncodeToString(hash[:])
}

// Encrypt encrypts plaintext using AES-256-GCM
func (as *AESService) Encrypt(keyB64 string, plaintext []byte) (*EncryptedData, error) {
	// Decode the key
	key, err := base64.StdEncoding.DecodeString(keyB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode key: %w", err)
	}

	if len(key) != as.keySize {
		return nil, fmt.Errorf("invalid key size: expected %d bytes, got %d", as.keySize, len(key))
	}

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Encrypt the data
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	// In GCM mode, the last 16 bytes of ciphertext are the auth tag
	tagSize := gcm.Overhead() // 16 bytes for GCM
	if len(ciphertext) < tagSize {
		return nil, fmt.Errorf("ciphertext too short")
	}

	actualCiphertext := ciphertext[:len(ciphertext)-tagSize]
	authTag := ciphertext[len(ciphertext)-tagSize:]

	return &EncryptedData{
		Ciphertext: base64.StdEncoding.EncodeToString(actualCiphertext),
		Nonce:      base64.StdEncoding.EncodeToString(nonce),
		AuthTag:    base64.StdEncoding.EncodeToString(authTag),
	}, nil
}

// EncryptWithAAD encrypts plaintext using AES-256-GCM with Additional Authenticated Data
func (as *AESService) EncryptWithAAD(keyB64 string, plaintext, aad []byte) (*EncryptedData, error) {
	// Decode the key
	key, err := base64.StdEncoding.DecodeString(keyB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode key: %w", err)
	}

	if len(key) != as.keySize {
		return nil, fmt.Errorf("invalid key size: expected %d bytes, got %d", as.keySize, len(key))
	}

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Generate random nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Encrypt the data with AAD
	ciphertext := gcm.Seal(nil, nonce, plaintext, aad)

	return &EncryptedData{
		Ciphertext: base64.StdEncoding.EncodeToString(ciphertext),
		Nonce:      base64.StdEncoding.EncodeToString(nonce),
	}, nil
}

// Decrypt decrypts ciphertext using AES-256-GCM
func (as *AESService) Decrypt(keyB64 string, encData *EncryptedData) ([]byte, error) {
	// Decode the key
	key, err := base64.StdEncoding.DecodeString(keyB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode key: %w", err)
	}

	if len(key) != as.keySize {
		return nil, fmt.Errorf("invalid key size: expected %d bytes, got %d", as.keySize, len(key))
	}

	// Decode ciphertext, nonce, and auth tag
	ciphertext, err := base64.StdEncoding.DecodeString(encData.Ciphertext)
	if err != nil {
		return nil, fmt.Errorf("failed to decode ciphertext: %w", err)
	}

	nonce, err := base64.StdEncoding.DecodeString(encData.Nonce)
	if err != nil {
		return nil, fmt.Errorf("failed to decode nonce: %w", err)
	}

	// If AuthTag is provided separately, append it to ciphertext for GCM
	if encData.AuthTag != "" {
		authTag, err := base64.StdEncoding.DecodeString(encData.AuthTag)
		if err != nil {
			return nil, fmt.Errorf("failed to decode auth tag: %w", err)
		}
		// Append auth tag to ciphertext for GCM.Open
		ciphertext = append(ciphertext, authTag...)
	}

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Validate nonce size
	if len(nonce) != gcm.NonceSize() {
		return nil, fmt.Errorf("invalid nonce size: expected %d bytes, got %d", gcm.NonceSize(), len(nonce))
	}

	// Decrypt the data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %w", err)
	}

	return plaintext, nil
}

// DecryptWithAAD decrypts ciphertext using AES-256-GCM with Additional Authenticated Data
func (as *AESService) DecryptWithAAD(keyB64 string, encData *EncryptedData, aad []byte) ([]byte, error) {
	// Decode the key
	key, err := base64.StdEncoding.DecodeString(keyB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode key: %w", err)
	}

	if len(key) != as.keySize {
		return nil, fmt.Errorf("invalid key size: expected %d bytes, got %d", as.keySize, len(key))
	}

	// Decode ciphertext, nonce, and auth tag
	ciphertext, err := base64.StdEncoding.DecodeString(encData.Ciphertext)
	if err != nil {
		return nil, fmt.Errorf("failed to decode ciphertext: %w", err)
	}

	nonce, err := base64.StdEncoding.DecodeString(encData.Nonce)
	if err != nil {
		return nil, fmt.Errorf("failed to decode nonce: %w", err)
	}

	// If AuthTag is provided separately, append it to ciphertext for GCM
	if encData.AuthTag != "" {
		authTag, err := base64.StdEncoding.DecodeString(encData.AuthTag)
		if err != nil {
			return nil, fmt.Errorf("failed to decode auth tag: %w", err)
		}
		// Append auth tag to ciphertext for GCM.Open
		ciphertext = append(ciphertext, authTag...)
	}

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Validate nonce size
	if len(nonce) != gcm.NonceSize() {
		return nil, fmt.Errorf("invalid nonce size: expected %d bytes, got %d", gcm.NonceSize(), len(nonce))
	}

	// Decrypt the data with AAD
	plaintext, err := gcm.Open(nil, nonce, ciphertext, aad)
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %w", err)
	}

	return plaintext, nil
}

// EncryptString encrypts a string and returns base64-encoded result
func (as *AESService) EncryptString(keyB64, plaintext string) (string, error) {
	encData, err := as.Encrypt(keyB64, []byte(plaintext))
	if err != nil {
		return "", err
	}

	// Combine nonce and ciphertext for simple storage
	combined := encData.Nonce + ":" + encData.Ciphertext
	return combined, nil
}

// DecryptString decrypts a base64-encoded string
func (as *AESService) DecryptString(keyB64, encryptedData string) (string, error) {
	// Split nonce and ciphertext
	parts := splitString(encryptedData, ":")
	if len(parts) != 2 {
		return "", fmt.Errorf("invalid encrypted data format")
	}

	encData := &EncryptedData{
		Nonce:      parts[0],
		Ciphertext: parts[1],
	}

	plaintext, err := as.Decrypt(keyB64, encData)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// ValidateKey validates an AES-256 key
func (as *AESService) ValidateKey(keyB64 string) error {
	key, err := base64.StdEncoding.DecodeString(keyB64)
	if err != nil {
		return fmt.Errorf("invalid base64 encoding: %w", err)
	}

	if len(key) != as.keySize {
		return fmt.Errorf("invalid key size: expected %d bytes, got %d", as.keySize, len(key))
	}

	// Test the key by creating a cipher
	_, err = aes.NewCipher(key)
	if err != nil {
		return fmt.Errorf("invalid AES key: %w", err)
	}

	return nil
}

// GenerateKeyFromSecret creates an AES key from a secret using SHA256 (matches Ruby implementation)
func (as *AESService) GenerateKeyFromSecret(secret string) string {
	// Ruby: key = OpenSSL::Digest::SHA256.digest(secret)
	hasher := sha256.New()
	hasher.Write([]byte(secret))
	keyBytes := hasher.Sum(nil)
	return base64.StdEncoding.EncodeToString(keyBytes)
}

// GetKeyInfo returns information about an AES key
func (as *AESService) GetKeyInfo(keyB64 string) (map[string]interface{}, error) {
	if err := as.ValidateKey(keyB64); err != nil {
		return nil, err
	}

	info := map[string]interface{}{
		"algorithm": "AES",
		"mode":      "GCM",
		"key_size":  as.keySize * 8, // Convert bytes to bits
		"type":      "symmetric",
	}

	return info, nil
}

// GenerateNonce generates a random nonce for GCM mode
func (as *AESService) GenerateNonce() (string, error) {
	nonce := make([]byte, as.nonceSize)
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}
	return base64.StdEncoding.EncodeToString(nonce), nil
}

// EncryptWithNonce encrypts data using a provided nonce (use with caution - nonce reuse is dangerous)
func (as *AESService) EncryptWithNonce(keyB64 string, plaintext []byte, nonceB64 string) (*EncryptedData, error) {
	// Decode the key
	key, err := base64.StdEncoding.DecodeString(keyB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode key: %w", err)
	}

	// Decode the nonce
	nonce, err := base64.StdEncoding.DecodeString(nonceB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode nonce: %w", err)
	}

	if len(key) != as.keySize {
		return nil, fmt.Errorf("invalid key size: expected %d bytes, got %d", as.keySize, len(key))
	}

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Validate nonce size
	if len(nonce) != gcm.NonceSize() {
		return nil, fmt.Errorf("invalid nonce size: expected %d bytes, got %d", gcm.NonceSize(), len(nonce))
	}

	// Encrypt the data
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	return &EncryptedData{
		Ciphertext: base64.StdEncoding.EncodeToString(ciphertext),
		Nonce:      nonceB64,
	}, nil
}

// Helper function to split strings
func splitString(s, delimiter string) []string {
	if s == "" {
		return []string{}
	}
	
	parts := make([]string, 0)
	start := 0
	
	for i := 0; i <= len(s)-len(delimiter); i++ {
		if s[i:i+len(delimiter)] == delimiter {
			parts = append(parts, s[start:i])
			start = i + len(delimiter)
			i += len(delimiter) - 1
		}
	}
	
	parts = append(parts, s[start:])
	return parts
}