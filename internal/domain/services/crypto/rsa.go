package crypto

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
)

// RSAService provides RSA-2048 cryptographic operations
type RSAService struct {
	keySize int
}

// NewRSAService creates a new RSA service instance
func NewRSAService() *RSAService {
	return &RSAService{
		keySize: 2048,
	}
}

// KeyPair represents an RSA key pair
type KeyPair struct {
	PrivateKey string `json:"private_key"`
	PublicKey  string `json:"public_key"`
}

// GenerateKeyPair generates a new RSA-2048 key pair
func (rs *RSAService) GenerateKeyPair() (*KeyPair, error) {
	// Generate private key
	privateKey, err := rsa.GenerateKey(rand.Reader, rs.keySize)
	if err != nil {
		return nil, fmt.Errorf("failed to generate RSA private key: %w", err)
	}

	// Encode private key to PEM format
	privateKeyBytes, err := x509.MarshalPKCS8PrivateKey(privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal private key: %w", err)
	}

	privateKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PRIVATE KEY",
		Bytes: privateKeyBytes,
	})

	// Encode public key to PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal public key: %w", err)
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return &KeyPair{
		PrivateKey: string(privateKeyPEM),
		PublicKey:  string(publicKeyPEM),
	}, nil
}

// Sign signs data using RSA-PSS with SHA-256
func (rs *RSAService) Sign(privateKeyPEM string, data []byte) (string, error) {
	// Parse private key
	privateKey, err := rs.parsePrivateKey(privateKeyPEM)
	if err != nil {
		return "", err
	}

	// Hash the data
	hash := sha256.Sum256(data)

	// Sign using RSA-PSS
	signature, err := rsa.SignPSS(rand.Reader, privateKey, crypto.SHA256, hash[:], &rsa.PSSOptions{
		SaltLength: rsa.PSSSaltLengthEqualsHash,
	})
	if err != nil {
		return "", fmt.Errorf("failed to sign data: %w", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// Verify verifies a signature using RSA-PSS with SHA-256
func (rs *RSAService) Verify(publicKeyPEM string, data []byte, signatureB64 string) error {
	// Parse public key
	publicKey, err := rs.parsePublicKey(publicKeyPEM)
	if err != nil {
		return err
	}

	// Decode signature
	signature, err := base64.StdEncoding.DecodeString(signatureB64)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %w", err)
	}

	// Hash the data
	hash := sha256.Sum256(data)

	// Verify signature using RSA-PSS
	err = rsa.VerifyPSS(publicKey, crypto.SHA256, hash[:], signature, &rsa.PSSOptions{
		SaltLength: rsa.PSSSaltLengthEqualsHash,
	})
	if err != nil {
		return fmt.Errorf("signature verification failed: %w", err)
	}

	return nil
}

// Encrypt encrypts data using RSA-OAEP with SHA-256
func (rs *RSAService) Encrypt(publicKeyPEM string, plaintext []byte) (string, error) {
	// Parse public key
	publicKey, err := rs.parsePublicKey(publicKeyPEM)
	if err != nil {
		return "", err
	}

	// Check data size limit for RSA-2048 with OAEP-SHA256
	// Max size = key_size/8 - 2*hash_size - 2 = 256 - 2*32 - 2 = 190 bytes
	maxSize := (rs.keySize / 8) - 2*sha256.Size - 2
	if len(plaintext) > maxSize {
		return "", fmt.Errorf("data too large for RSA-2048 encryption (max %d bytes)", maxSize)
	}

	// Encrypt using RSA-OAEP
	ciphertext, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, publicKey, plaintext, nil)
	if err != nil {
		return "", fmt.Errorf("failed to encrypt data: %w", err)
	}

	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// Decrypt decrypts data using RSA-OAEP with SHA-256
func (rs *RSAService) Decrypt(privateKeyPEM string, ciphertextB64 string) ([]byte, error) {
	// Parse private key
	privateKey, err := rs.parsePrivateKey(privateKeyPEM)
	if err != nil {
		return nil, err
	}

	// Decode ciphertext
	ciphertext, err := base64.StdEncoding.DecodeString(ciphertextB64)
	if err != nil {
		return nil, fmt.Errorf("failed to decode ciphertext: %w", err)
	}

	// Decrypt using RSA-OAEP
	plaintext, err := rsa.DecryptOAEP(sha256.New(), rand.Reader, privateKey, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt data: %w", err)
	}

	return plaintext, nil
}

// ValidateKeyPair validates that a private and public key form a valid pair
func (rs *RSAService) ValidateKeyPair(privateKeyPEM, publicKeyPEM string) error {
	// Parse both keys
	privateKey, err := rs.parsePrivateKey(privateKeyPEM)
	if err != nil {
		return err
	}

	publicKey, err := rs.parsePublicKey(publicKeyPEM)
	if err != nil {
		return err
	}

	// Compare public key from private key with provided public key
	derivedPublicKey := &privateKey.PublicKey
	
	if derivedPublicKey.N.Cmp(publicKey.N) != 0 || derivedPublicKey.E != publicKey.E {
		return fmt.Errorf("private and public keys do not form a valid pair")
	}

	return nil
}

// GetPublicKeyFromPrivate extracts the public key from a private key
func (rs *RSAService) GetPublicKeyFromPrivate(privateKeyPEM string) (string, error) {
	// Parse private key
	privateKey, err := rs.parsePrivateKey(privateKeyPEM)
	if err != nil {
		return "", err
	}

	// Extract public key
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		return "", fmt.Errorf("failed to marshal public key: %w", err)
	}

	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyBytes,
	})

	return string(publicKeyPEM), nil
}

// parsePrivateKey parses a PEM-encoded RSA private key
func (rs *RSAService) parsePrivateKey(privateKeyPEM string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	// Try PKCS8 format first
	privateKeyInterface, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		// Fallback to PKCS1 format
		return x509.ParsePKCS1PrivateKey(block.Bytes)
	}

	privateKey, ok := privateKeyInterface.(*rsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("key is not an RSA private key")
	}

	// Validate key size
	if privateKey.N.BitLen() != rs.keySize {
		return nil, fmt.Errorf("key size is %d bits, expected %d bits", privateKey.N.BitLen(), rs.keySize)
	}

	return privateKey, nil
}

// parsePublicKey parses a PEM-encoded RSA public key
func (rs *RSAService) parsePublicKey(publicKeyPEM string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	publicKeyInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	publicKey, ok := publicKeyInterface.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("key is not an RSA public key")
	}

	// Validate key size
	if publicKey.N.BitLen() != rs.keySize {
		return nil, fmt.Errorf("key size is %d bits, expected %d bits", publicKey.N.BitLen(), rs.keySize)
	}

	return publicKey, nil
}

// GetKeyInfo returns information about an RSA key
func (rs *RSAService) GetKeyInfo(keyPEM string) (map[string]interface{}, error) {
	info := make(map[string]interface{})

	// Try to parse as private key first
	if privateKey, err := rs.parsePrivateKey(keyPEM); err == nil {
		info["type"] = "private"
		info["size"] = privateKey.N.BitLen()
		info["algorithm"] = "RSA"
		return info, nil
	}

	// Try to parse as public key
	if publicKey, err := rs.parsePublicKey(keyPEM); err == nil {
		info["type"] = "public"
		info["size"] = publicKey.N.BitLen()
		info["algorithm"] = "RSA"
		return info, nil
	}

	return nil, fmt.Errorf("invalid RSA key format")
}