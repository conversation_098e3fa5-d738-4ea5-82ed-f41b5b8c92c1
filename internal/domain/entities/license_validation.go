package entities

import (
	"fmt"
	"strings"

	"github.com/google/uuid"
)

// ===== CORE FIELD VALIDATIONS =====
// Maps from Ruby License model field validations

// ValidateKey validates the license key (Ruby: validates :key)
func (l *License) ValidateKey() error {
	return ValidateLicenseKey(l.Key)
}

// ValidateName validates the license name (Ruby: validates :name)
func (l *License) ValidateName() error {
	return ValidateLicenseName(l.Name)
}

// ValidateOwnerType validates the owner type (Ruby: validates :owner_type)
func (l *License) ValidateOwnerType() error {
	if !l.OwnerType.IsValid() {
		return fmt.Errorf("owner_type must be one of: user, organization")
	}
	return nil
}

// ValidateOwnerID validates the owner ID (Ruby: validates :owner_id)
func (l *License) ValidateOwnerID() error {
	if l.OwnerID == uuid.Nil {
		return fmt.Errorf("owner_id can't be blank")
	}
	return nil
}

// ValidateStatus validates the license status (Ruby: validates :status)
func (l *License) ValidateStatus() error {
	if !l.Status.IsValid() {
		return fmt.Errorf("status must be one of: active, expired, suspended, banned, expiring, inactive")
	}
	return nil
}

// ValidateUses validates the uses count (Ruby: validates :uses)
func (l *License) ValidateUses() error {
	return ValidateUses(l.Uses)
}

// ===== POLICY OVERRIDE VALIDATIONS =====
// Maps from Ruby License model policy override validations

// ValidateMaxMachinesOverride validates max machines override (Ruby: validates :max_machines_override)
func (l *License) ValidateMaxMachinesOverride() error {
	return ValidateMaxMachinesOverride(l.MaxMachinesOverride)
}

// ValidateMaxCoresOverride validates max cores override (Ruby: validates :max_cores_override)
func (l *License) ValidateMaxCoresOverride() error {
	return ValidateMaxCoresOverride(l.MaxCoresOverride)
}

// ValidateMaxUsesOverride validates max uses override (Ruby: validates :max_uses_override)
func (l *License) ValidateMaxUsesOverride() error {
	return ValidateMaxUsesOverride(l.MaxUsesOverride)
}

// ValidateMaxProcessesOverride validates max processes override (Ruby: validates :max_processes_override)
func (l *License) ValidateMaxProcessesOverride() error {
	return ValidateMaxProcessesOverride(l.MaxProcessesOverride)
}

// ValidateMaxUsersOverride validates max users override (Ruby: validates :max_users_override)
func (l *License) ValidateMaxUsersOverride() error {
	return ValidateMaxUsersOverride(l.MaxUsersOverride)
}

// ===== BUSINESS LOGIC VALIDATIONS =====
// Maps from Ruby License model business logic validations

// ValidateKeyUniqueness validates key uniqueness within organization (Ruby: validates :key, uniqueness)
func (l *License) ValidateKeyUniqueness() error {
	// This would typically be handled by database constraints
	// But we can add application-level validation if needed
	if strings.TrimSpace(l.Key) == "" {
		return fmt.Errorf("key can't be blank")
	}
	return nil
}

// ValidateExpiryDate validates expiry date logic (Ruby: custom validation)
func (l *License) ValidateExpiryDate() error {
	// If policy has duration, expiry should be set appropriately
	if l.Policy.Duration != nil && l.ExpiresAt == nil {
		return fmt.Errorf("expires_at must be set when policy has duration")
	}
	return nil
}

// ValidateOwnerConsistency validates owner type and ID consistency (Ruby: custom validation)
func (l *License) ValidateOwnerConsistency() error {
	if l.OwnerType == LicenseOwnerTypeUser && l.OwnerID == uuid.Nil {
		return fmt.Errorf("owner_id must be set when owner_type is user")
	}
	if l.OwnerType == LicenseOwnerTypeOrganization && l.OwnerID == uuid.Nil {
		return fmt.Errorf("owner_id must be set when owner_type is organization")
	}
	return nil
}

// ValidatePolicyConsistency validates policy relationship consistency (Ruby: custom validation)
func (l *License) ValidatePolicyConsistency() error {
	if l.PolicyID == uuid.Nil {
		return fmt.Errorf("policy_id can't be blank")
	}
	if l.ProductID == uuid.Nil {
		return fmt.Errorf("product_id can't be blank")
	}
	if l.OrganizationID == uuid.Nil {
		return fmt.Errorf("organization_id can't be blank")
	}
	return nil
}

// ValidateUsageLimits validates usage against limits (Ruby: custom validation)
func (l *License) ValidateUsageLimits() error {
	// Check if uses exceed max uses
	maxUses := l.GetMaxUses()
	if maxUses != nil && l.Uses > *maxUses {
		return fmt.Errorf("uses (%d) exceeds max_uses (%d)", l.Uses, *maxUses)
	}

	// Check if machines count exceeds max machines
	maxMachines := l.GetMaxMachines()
	if maxMachines != nil && l.MachinesCount > *maxMachines {
		return fmt.Errorf("machines_count (%d) exceeds max_machines (%d)", l.MachinesCount, *maxMachines)
	}

	// Check if users count exceeds max users
	maxUsers := l.GetMaxUsers()
	if maxUsers != nil && l.GetUsersCount() > *maxUsers {
		return fmt.Errorf("users_count (%d) exceeds max_users (%d)", l.GetUsersCount(), *maxUsers)
	}

	return nil
}

// ValidateSuspensionLogic validates suspension logic (Ruby: custom validation)
func (l *License) ValidateSuspensionLogic() error {
	// Protected licenses cannot be suspended
	if l.Suspended && l.IsProtected() {
		return fmt.Errorf("protected licenses cannot be suspended")
	}
	return nil
}

// ===== COMPREHENSIVE VALIDATION =====
// Maps from Ruby License model comprehensive validation

// Validate performs comprehensive validation (Ruby: validate method)
func (l *License) Validate() error {
	// Core field validations
	if err := l.ValidateKey(); err != nil {
		return err
	}
	if err := l.ValidateName(); err != nil {
		return err
	}
	if err := l.ValidateOwnerType(); err != nil {
		return err
	}
	if err := l.ValidateOwnerID(); err != nil {
		return err
	}
	if err := l.ValidateStatus(); err != nil {
		return err
	}
	if err := l.ValidateUses(); err != nil {
		return err
	}

	// Policy override validations
	if err := l.ValidateMaxMachinesOverride(); err != nil {
		return err
	}
	if err := l.ValidateMaxCoresOverride(); err != nil {
		return err
	}
	if err := l.ValidateMaxUsesOverride(); err != nil {
		return err
	}
	if err := l.ValidateMaxProcessesOverride(); err != nil {
		return err
	}
	if err := l.ValidateMaxUsersOverride(); err != nil {
		return err
	}

	// Business logic validations
	if err := l.ValidateKeyUniqueness(); err != nil {
		return err
	}
	if err := l.ValidateExpiryDate(); err != nil {
		return err
	}
	if err := l.ValidateOwnerConsistency(); err != nil {
		return err
	}
	if err := l.ValidatePolicyConsistency(); err != nil {
		return err
	}
	if err := l.ValidateUsageLimits(); err != nil {
		return err
	}
	if err := l.ValidateSuspensionLogic(); err != nil {
		return err
	}

	return nil
}

// ===== VALIDATION HELPERS =====
// Maps from Ruby License model validation helpers

// IsValid checks if license is valid (Ruby: valid? method)
func (l *License) IsValid() bool {
	return l.Validate() == nil
}

// GetValidationErrors returns all validation errors (Ruby: errors method)
func (l *License) GetValidationErrors() []error {
	var errors []error

	// Collect all validation errors
	if err := l.ValidateKey(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateName(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateOwnerType(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateOwnerID(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateStatus(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateUses(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateMaxMachinesOverride(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateMaxCoresOverride(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateMaxUsesOverride(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateMaxProcessesOverride(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateMaxUsersOverride(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateKeyUniqueness(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateExpiryDate(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateOwnerConsistency(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidatePolicyConsistency(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateUsageLimits(); err != nil {
		errors = append(errors, err)
	}
	if err := l.ValidateSuspensionLogic(); err != nil {
		errors = append(errors, err)
	}

	return errors
}

// HasValidationErrors checks if license has validation errors
func (l *License) HasValidationErrors() bool {
	return len(l.GetValidationErrors()) > 0
}
