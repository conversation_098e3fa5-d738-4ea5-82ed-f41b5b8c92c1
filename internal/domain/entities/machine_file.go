package entities

import (
	"fmt"
	"regexp"
	"time"

	"github.com/google/uuid"
)

// MachineFile represents a machine certificate file (Ruby: MachineFile model)
// Maps from Ruby MachineFile model with field renaming and simplification
// Renamed account_id to organization_id, removed environment_id per requirements
type MachineFile struct {
	// === CORE FIELDS (Ruby: primary attributes) ===
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null;index"` // Ruby: account_id -> organization_id
	LicenseID      uuid.UUID `json:"license_id" gorm:"type:uuid;not null;index"`      // Ruby: license_id
	MachineID      uuid.UUID `json:"machine_id" gorm:"type:uuid;not null;index"`      // Ruby: machine_id

	// === CERTIFICATE DATA (Ruby: certificate fields) ===
	Certificate string `json:"certificate" gorm:"type:text;not null"` // Ruby: certificate (PEM-formatted)
	Algorithm   string `json:"algorithm" gorm:"type:varchar(50)"`     // Ruby: algorithm (signing algorithm)

	// === TIMING FIELDS (Ruby: timing attributes) ===
	IssuedAt  time.Time      `json:"issued_at" gorm:"not null"` // Ruby: issued_at
	ExpiresAt *time.Time     `json:"expires_at,omitempty"`      // Ruby: expires_at (optional)
	TTL       *time.Duration `json:"ttl,omitempty" gorm:"-"`    // Ruby: ttl (duration, not persisted)

	// === INCLUDES (Ruby: includes array) ===
	Includes []string `json:"includes,omitempty" gorm:"type:jsonb"` // Ruby: includes (relationships included)

	// === AUDIT FIELDS (Ruby: timestamps) ===
	CreatedAt time.Time `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time `json:"updated_at" gorm:"not null"`

	// === RELATIONSHIPS (Ruby: associations for eager loading) ===
	Organization *Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	License      *License      `json:"license,omitempty" gorm:"foreignKey:LicenseID"`
	Machine      *Machine      `json:"machine,omitempty" gorm:"foreignKey:MachineID"`
}

// TableName specifies the table name for GORM
func (MachineFile) TableName() string {
	return "machine_files"
}

// Supported algorithms (Ruby: ALGORITHMS constant)
// Clean Go constants without version suffixes, mapping to latest underlying values
var SupportedMachineFileAlgorithms = []string{
	"aes-256-gcm+ed25519",
	"aes-256-gcm+rsa-pss-sha256",
	"aes-256-gcm+rsa-sha256",
	"base64+ed25519",
	"base64+rsa-pss-sha256",
	"base64+rsa-sha256",
}

// Validation patterns (Ruby: validation patterns)
var (
	MachineFileHeaderPattern = regexp.MustCompile(`\A-----BEGIN MACHINE FILE-----\n`)
	MachineFileFooterPattern = regexp.MustCompile(`-----END MACHINE FILE-----\n*\z`)
)

// ===== VALIDATION METHODS =====

// ValidateCertificateFormat validates the certificate format (Ruby: validates_format_of :certificate)
func (mf *MachineFile) ValidateCertificateFormat() error {
	if !MachineFileHeaderPattern.MatchString(mf.Certificate) {
		return fmt.Errorf("certificate has invalid prefix")
	}
	if !MachineFileFooterPattern.MatchString(mf.Certificate) {
		return fmt.Errorf("certificate has invalid suffix")
	}
	return nil
}

// ValidateAlgorithm validates the algorithm (Ruby: validates_inclusion_of :algorithm)
func (mf *MachineFile) ValidateAlgorithm() error {
	for _, supported := range SupportedMachineFileAlgorithms {
		if mf.Algorithm == supported {
			return nil
		}
	}
	return fmt.Errorf("unsupported algorithm: %s", mf.Algorithm)
}

// ValidateTTL validates the TTL (Ruby: validates_numericality_of :ttl)
func (mf *MachineFile) ValidateTTL() error {
	if mf.TTL != nil && *mf.TTL < time.Hour {
		return fmt.Errorf("TTL must be greater than or equal to 1 hour")
	}
	return nil
}

// ValidateRequiredFields validates required fields (Ruby: validates presence)
func (mf *MachineFile) ValidateRequiredFields() error {
	if mf.OrganizationID == uuid.Nil {
		return fmt.Errorf("organization_id can't be blank")
	}
	if mf.LicenseID == uuid.Nil {
		return fmt.Errorf("license_id can't be blank")
	}
	if mf.MachineID == uuid.Nil {
		return fmt.Errorf("machine_id can't be blank")
	}
	if mf.Certificate == "" {
		return fmt.Errorf("certificate can't be blank")
	}
	if mf.IssuedAt.IsZero() {
		return fmt.Errorf("issued_at can't be blank")
	}
	return nil
}

// Validate performs all validations (Ruby: validate methods)
func (mf *MachineFile) Validate() error {
	if err := mf.ValidateRequiredFields(); err != nil {
		return err
	}
	if err := mf.ValidateCertificateFormat(); err != nil {
		return err
	}
	if err := mf.ValidateAlgorithm(); err != nil {
		return err
	}
	if err := mf.ValidateTTL(); err != nil {
		return err
	}
	return nil
}

// ===== HELPER METHODS =====

// IsExpired checks if the machine file has expired (Ruby: expired? method)
func (mf *MachineFile) IsExpired() bool {
	if mf.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*mf.ExpiresAt)
}

// GetProduct returns the product through license relationship (Ruby: product method)
func (mf *MachineFile) GetProduct() *Product {
	if mf.License != nil {
		return &mf.License.Product
	}
	return nil
}

// GetOwner returns the license owner (Ruby: owner method)
func (mf *MachineFile) GetOwner() interface{} {
	if mf.License != nil {
		// Return owner information based on polymorphic ownership
		return map[string]interface{}{
			"type": mf.License.OwnerType,
			"id":   mf.License.OwnerID,
		}
	}
	return nil
}

// IsPersisted returns false as this is a value object (Ruby: persisted? method)
func (mf *MachineFile) IsPersisted() bool {
	return false
}
