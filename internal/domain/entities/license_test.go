package entities

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

// ===== TEST HELPERS =====

// createTestLicense creates a test license with default values
func createTestLicense() *License {
	now := time.Now()
	expiry := now.Add(30 * 24 * time.Hour) // 30 days from now
	protected := false

	return &License{
		ID:             uuid.New(),
		OrganizationID: uuid.New(),
		ProductID:      uuid.New(),
		PolicyID:       uuid.New(),
		Key:            "TEST-LICENSE-KEY",
		Name:           &[]string{"Test License"}[0],
		OwnerType:      LicenseOwnerTypeOrganization,
		OwnerID:        uuid.New(),
		Status:         LicenseStatusActive,
		Suspended:      false,
		Protected:      &protected,
		Uses:           5,
		ExpiresAt:      &expiry,
		LastUsed:       &now,
		CreatedAt:      now,
		UpdatedAt:      now,
		Policy: Policy{
			MaxMachines:  &[]int{10}[0],
			MaxCores:     &[]int{100}[0],
			MaxUses:      &[]int{1000}[0],
			MaxProcesses: &[]int{50}[0],
			MaxUsers:     &[]int{5}[0],
			Duration:     &[]int64{2592000}[0], // 30 days in seconds
		},
	}
}

// createExpiredLicense creates a test license that is expired
func createExpiredLicense() *License {
	license := createTestLicense()
	expiry := time.Now().Add(-24 * time.Hour) // 1 day ago
	license.ExpiresAt = &expiry
	return license
}

// createExpiringLicense creates a test license that is expiring soon
func createExpiringLicense() *License {
	license := createTestLicense()
	expiry := time.Now().Add(2 * time.Hour) // 2 hours from now (within 3 day window)
	license.ExpiresAt = &expiry
	return license
}

// createSuspendedLicense creates a test license that is suspended
func createSuspendedLicense() *License {
	license := createTestLicense()
	license.Suspended = true
	return license
}

// ===== BASIC PROPERTY TESTS =====

func TestLicense_GetID(t *testing.T) {
	license := createTestLicense()
	// Test that GetID returns a valid UUID string
	id := license.GetID()
	assert.NotEmpty(t, id)
	// Verify it's a valid UUID format
	_, err := uuid.Parse(id)
	assert.NoError(t, err)
}

func TestLicense_GetKey(t *testing.T) {
	license := createTestLicense()
	assert.Equal(t, "TEST-LICENSE-KEY", license.GetKey())
}

func TestLicense_GetName(t *testing.T) {
	license := createTestLicense()
	assert.Equal(t, "Test License", *license.GetName())
}

func TestLicense_HasName(t *testing.T) {
	license := createTestLicense()
	assert.True(t, license.HasName())

	license.Name = nil
	assert.False(t, license.HasName())

	license.Name = &[]string{""}[0]
	assert.False(t, license.HasName())
}

func TestLicense_GetOwnerType(t *testing.T) {
	license := createTestLicense()
	assert.Equal(t, LicenseOwnerTypeOrganization, license.GetOwnerType())
}

func TestLicense_GetOwnerID(t *testing.T) {
	license := createTestLicense()
	// Test that GetOwnerID returns a valid UUID string
	ownerID := license.GetOwnerID()
	assert.NotEmpty(t, ownerID)
	// Verify it's a valid UUID format
	_, err := uuid.Parse(ownerID)
	assert.NoError(t, err)
}

func TestLicense_HasOwner(t *testing.T) {
	license := createTestLicense()
	assert.True(t, license.HasOwner())

	license.OwnerID = uuid.Nil
	assert.False(t, license.HasOwner())
}

// ===== STATUS TESTS =====

func TestLicense_GetStatus(t *testing.T) {
	// Test active license
	license := createTestLicense()
	assert.Equal(t, LicenseStatusActive, license.GetStatus())

	// Test suspended license
	license = createSuspendedLicense()
	assert.Equal(t, LicenseStatusSuspended, license.GetStatus())

	// Test expired license
	license = createExpiredLicense()
	assert.Equal(t, LicenseStatusExpired, license.GetStatus())

	// Test expiring license
	license = createExpiringLicense()
	assert.Equal(t, LicenseStatusExpiring, license.GetStatus())
}

func TestLicense_IsSuspended(t *testing.T) {
	license := createTestLicense()
	assert.False(t, license.IsSuspended())

	license = createSuspendedLicense()
	assert.True(t, license.IsSuspended())
}

func TestLicense_IsExpired(t *testing.T) {
	license := createTestLicense()
	assert.False(t, license.IsExpired())

	license = createExpiredLicense()
	assert.True(t, license.IsExpired())
}

func TestLicense_IsExpiring(t *testing.T) {
	license := createTestLicense()
	assert.False(t, license.IsExpiring())

	license = createExpiringLicense()
	assert.True(t, license.IsExpiring())
}

func TestLicense_HasExpiry(t *testing.T) {
	license := createTestLicense()
	assert.True(t, license.HasExpiry())

	license.ExpiresAt = nil
	assert.False(t, license.HasExpiry())
}

// ===== USAGE TESTS =====

func TestLicense_GetUses(t *testing.T) {
	license := createTestLicense()
	assert.Equal(t, 5, license.GetUses())
}

func TestLicense_IncrementUses(t *testing.T) {
	license := createTestLicense()
	initialUses := license.GetUses()

	license.IncrementUses()

	assert.Equal(t, initialUses+1, license.GetUses())
	assert.NotNil(t, license.LastUsed)
}

func TestLicense_GetUsersCount(t *testing.T) {
	license := createTestLicense()
	license.LicenseUsersCount = 3

	// Should include owner in count
	assert.Equal(t, 4, license.GetUsersCount())

	// Test without owner
	license.OwnerID = uuid.Nil
	assert.Equal(t, 3, license.GetUsersCount())
}

func TestLicense_GetMachinesCount(t *testing.T) {
	license := createTestLicense()
	license.MachinesCount = 7
	assert.Equal(t, 7, license.GetMachinesCount())
}

func TestLicense_GetMachinesCoreCount(t *testing.T) {
	license := createTestLicense()
	license.MachinesCoreCount = 42
	assert.Equal(t, 42, license.GetMachinesCoreCount())
}

// ===== POLICY OVERRIDE TESTS =====

func TestLicense_GetMaxMachines(t *testing.T) {
	license := createTestLicense()

	// Should return policy value when no override
	assert.Equal(t, 10, *license.GetMaxMachines())

	// Should return override when set
	override := 20
	license.MaxMachinesOverride = &override
	assert.Equal(t, 20, *license.GetMaxMachines())
}

func TestLicense_HasMaxMachines(t *testing.T) {
	license := createTestLicense()
	assert.True(t, license.HasMaxMachines())

	license.Policy.MaxMachines = nil
	license.MaxMachinesOverride = nil
	assert.False(t, license.HasMaxMachines())
}

func TestLicense_SetMaxMachines(t *testing.T) {
	license := createTestLicense()
	value := 25

	license.SetMaxMachines(&value)
	assert.Equal(t, 25, *license.MaxMachinesOverride)
}

func TestLicense_GetMaxCores(t *testing.T) {
	license := createTestLicense()

	// Should return policy value when no override
	assert.Equal(t, 100, *license.GetMaxCores())

	// Should return override when set
	override := 200
	license.MaxCoresOverride = &override
	assert.Equal(t, 200, *license.GetMaxCores())
}

func TestLicense_GetMaxUses(t *testing.T) {
	license := createTestLicense()

	// Should return policy value when no override
	assert.Equal(t, 1000, *license.GetMaxUses())

	// Should return override when set
	override := 2000
	license.MaxUsesOverride = &override
	assert.Equal(t, 2000, *license.GetMaxUses())
}

func TestLicense_GetMaxProcesses(t *testing.T) {
	license := createTestLicense()

	// Should return policy value when no override
	assert.Equal(t, 50, *license.GetMaxProcesses())

	// Should return override when set
	override := 100
	license.MaxProcessesOverride = &override
	assert.Equal(t, 100, *license.GetMaxProcesses())
}

func TestLicense_GetMaxUsers(t *testing.T) {
	license := createTestLicense()

	// Should return policy value when no override
	assert.Equal(t, 5, *license.GetMaxUsers())

	// Should return override when set
	override := 10
	license.MaxUsersOverride = &override
	assert.Equal(t, 10, *license.GetMaxUsers())
}

// ===== RENEWAL TESTS =====

func TestLicense_CanRenew(t *testing.T) {
	license := createTestLicense()
	assert.True(t, license.CanRenew())

	// Cannot renew without duration
	license.Policy.Duration = nil
	assert.False(t, license.CanRenew())
}

func TestLicense_Renew(t *testing.T) {
	license := createTestLicense()
	originalExpiry := *license.ExpiresAt
	originalUpdated := license.UpdatedAt

	// Wait a bit to ensure UpdatedAt changes
	time.Sleep(1 * time.Millisecond)

	license.Renew()

	// Should update expiry and updated_at
	assert.True(t, license.ExpiresAt.After(originalExpiry))
	assert.True(t, license.UpdatedAt.After(originalUpdated))
}

// ===== SUSPENSION TESTS =====

func TestLicense_Suspend(t *testing.T) {
	license := createTestLicense()
	originalUpdated := license.UpdatedAt

	// Wait a bit to ensure UpdatedAt changes
	time.Sleep(1 * time.Millisecond)

	license.Suspend()

	assert.True(t, license.Suspended)
	assert.True(t, license.UpdatedAt.After(originalUpdated))
}

func TestLicense_Reinstate(t *testing.T) {
	license := createSuspendedLicense()
	originalUpdated := license.UpdatedAt

	// Wait a bit to ensure UpdatedAt changes
	time.Sleep(1 * time.Millisecond)

	license.Reinstate()

	assert.False(t, license.Suspended)
	assert.True(t, license.UpdatedAt.After(originalUpdated))
}

// ===== CHECK-IN TESTS =====

func TestLicense_CheckIn(t *testing.T) {
	license := createTestLicense()
	originalUpdated := license.UpdatedAt

	// Wait a bit to ensure timestamps change
	time.Sleep(1 * time.Millisecond)

	license.CheckIn()

	assert.NotNil(t, license.LastCheckInAt)
	assert.True(t, license.UpdatedAt.After(originalUpdated))
}

func TestLicense_CheckOut(t *testing.T) {
	license := createTestLicense()
	originalUpdated := license.UpdatedAt

	// Wait a bit to ensure timestamps change
	time.Sleep(1 * time.Millisecond)

	license.CheckOut()

	assert.NotNil(t, license.LastCheckOutAt)
	assert.True(t, license.UpdatedAt.After(originalUpdated))
}
