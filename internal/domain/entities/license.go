package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// License represents a license key with all metadata and rules
// In GoKeys, license is a core entity containing license key and inheriting rules from policy
// License can override policy limits and track usage/validation
// Simplified from Ruby License model - removed group and environment, changed account to organization
// Organization replaces account from Ruby
type License struct {
	// === CORE FIELDS (Ruby: primary attributes) ===
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"column:organization_id;type:uuid;not null;index"` // Ruby: account_id
	ProductID      uuid.UUID `json:"product_id" gorm:"column:product_id;type:uuid;not null;index"`
	PolicyID       uuid.UUID `json:"policy_id" gorm:"column:policy_id;type:uuid;not null;index"`
	Key            string    `json:"key" gorm:"not null;index"`
	Name           *string   `json:"name,omitempty" gorm:"size:255"`

	// === LICENSE OWNERSHIP (Ruby: polymorphic owner association) ===
	OwnerType LicenseOwnerType `json:"owner_type" gorm:"column:owner_type;size:50;not null"`
	OwnerID   uuid.UUID        `json:"owner_id" gorm:"column:owner_id;type:uuid;not null;index"`

	// === LICENSE STATE (Ruby: status and flags) ===
	Status    LicenseStatus `json:"status" gorm:"size:50;default:'active'"`
	Suspended bool          `json:"suspended" gorm:"default:false"`
	Protected *bool         `json:"protected,omitempty" gorm:"default:false"`

	// === USAGE TRACKING (Ruby: usage fields) ===
	Uses      int        `json:"uses" gorm:"default:0"`
	ExpiresAt *time.Time `json:"expires_at,omitempty" gorm:"column:expires_at"`
	LastUsed  *time.Time `json:"last_used,omitempty" gorm:"column:last_used"`

	// === POLICY OVERRIDES (Ruby: max_* override fields) ===
	// Allow per-license customization of policy limits
	MaxUsesOverride      *int `json:"max_uses_override,omitempty" gorm:"column:max_uses_override"`
	MaxMachinesOverride  *int `json:"max_machines_override,omitempty" gorm:"column:max_machines_override"`
	MaxCoresOverride     *int `json:"max_cores_override,omitempty" gorm:"column:max_cores_override"`
	MaxUsersOverride     *int `json:"max_users_override,omitempty" gorm:"column:max_users_override"`
	MaxProcessesOverride *int `json:"max_processes_override,omitempty" gorm:"column:max_processes_override"`

	// === CACHED COUNTS (Ruby: counter cache fields) ===
	// Performance optimization - cached from related tables
	MachinesCount     int `json:"machines_count" gorm:"column:machines_count;default:0"`
	MachinesCoreCount int `json:"machines_core_count" gorm:"column:machines_core_count;default:0"`
	LicenseUsersCount int `json:"license_users_count" gorm:"column:license_users_count;default:0"`

	// === HEARTBEAT AND VALIDATION TRACKING (Ruby: check-in and validation fields) ===
	LastCheckInAt         *time.Time `json:"last_check_in_at,omitempty" gorm:"column:last_check_in_at"`
	LastValidatedAt       *time.Time `json:"last_validated_at,omitempty" gorm:"column:last_validated_at"`
	LastValidatedChecksum *string    `json:"last_validated_checksum,omitempty" gorm:"column:last_validated_checksum;size:255"`
	LastValidatedVersion  *string    `json:"last_validated_version,omitempty" gorm:"column:last_validated_version;size:255"`
	LastCheckOutAt        *time.Time `json:"last_check_out_at,omitempty" gorm:"column:last_check_out_at"`

	// === EVENT TRACKING (Ruby: event notification fields) ===
	// For notification management and preventing duplicate events
	LastExpirationEventSentAt   *time.Time `json:"last_expiration_event_sent_at,omitempty" gorm:"column:last_expiration_event_sent_at"`
	LastCheckInEventSentAt      *time.Time `json:"last_check_in_event_sent_at,omitempty" gorm:"column:last_check_in_event_sent_at"`
	LastExpiringSoonEventSentAt *time.Time `json:"last_expiring_soon_event_sent_at,omitempty" gorm:"column:last_expiring_soon_event_sent_at"`
	LastCheckInSoonEventSentAt  *time.Time `json:"last_check_in_soon_event_sent_at,omitempty" gorm:"column:last_check_in_soon_event_sent_at"`

	// === METADATA (Ruby: metadata JSONB field) ===
	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// === TIMESTAMPS (Ruby: standard Rails timestamps) ===
	CreatedAt time.Time      `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"column:updated_at;not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"column:deleted_at;index"`

	// === RELATIONSHIPS (Ruby: associations) ===
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Product      Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`
	Policy       Policy       `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`
	Machines     []Machine    `json:"machines,omitempty" gorm:"foreignKey:LicenseID"`
}

// TableName override table name for GORM
// Ensures GORM uses the correct table name "licenses" in database
func (License) TableName() string {
	return "licenses"
}

// ===== BASIC PROPERTY METHODS =====
// Maps from Ruby License model basic property methods

// GetID returns the license ID
func (l *License) GetID() string {
	return l.ID.String()
}

// GetKey returns the license key
func (l *License) GetKey() string {
	return l.Key
}

// GetName returns the license name
func (l *License) GetName() *string {
	return l.Name
}

// HasName checks if license has a name
func (l *License) HasName() bool {
	return l.Name != nil && *l.Name != ""
}

// GetOwnerType returns the owner type
func (l *License) GetOwnerType() LicenseOwnerType {
	return l.OwnerType
}

// GetOwnerID returns the owner ID
func (l *License) GetOwnerID() string {
	return l.OwnerID.String()
}

// HasOwner checks if license has an owner
func (l *License) HasOwner() bool {
	return l.OwnerID != uuid.Nil
}

// ===== STATUS METHODS =====
// Maps from Ruby License model status methods

// GetStatus returns the current license status (Ruby: status method)
func (l *License) GetStatus() LicenseStatus {
	// Ruby status method logic with priority order
	switch {
	case l.IsBanned():
		return LicenseStatusBanned
	case l.IsSuspended():
		return LicenseStatusSuspended
	case l.IsExpired():
		return LicenseStatusExpired
	case l.IsExpiring():
		return LicenseStatusExpiring
	case l.IsInactive():
		return LicenseStatusInactive
	default:
		return LicenseStatusActive
	}
}

// IsBanned checks if license is banned (Ruby: banned? method)
func (l *License) IsBanned() bool {
	// In Ruby, this checks if owner is banned
	// For now, we'll implement basic logic - can be extended with owner relationship
	return l.Status == LicenseStatusBanned
}

// IsSuspended checks if license is suspended (Ruby: suspended? method)
func (l *License) IsSuspended() bool {
	return l.Suspended
}

// IsExpired checks if license is expired (Ruby: expired? method)
func (l *License) IsExpired() bool {
	return IsExpired(l.ExpiresAt)
}

// IsExpiring checks if license is expiring soon (Ruby: expiring? method)
func (l *License) IsExpiring() bool {
	return IsExpiring(l.ExpiresAt)
}

// IsActive checks if license is active (Ruby: active? method)
func (l *License) IsActive() bool {
	return IsActive(l.CreatedAt, l.LastValidatedAt, l.LastCheckOutAt, l.LastCheckInAt, time.Duration(DefaultActivityPeriod))
}

// IsInactive checks if license is inactive (Ruby: inactive? method)
func (l *License) IsInactive() bool {
	return !l.IsActive()
}

// IsProtected checks if license is protected (Ruby: protected? method)
func (l *License) IsProtected() bool {
	if l.Protected != nil {
		return *l.Protected
	}
	// Fall back to policy protection if not set
	return l.Policy.IsProtected()
}

// HasExpiry checks if license has an expiry date (Ruby: expires? method)
func (l *License) HasExpiry() bool {
	return l.ExpiresAt != nil
}

// ===== USAGE METHODS =====
// Maps from Ruby License model usage methods

// GetUses returns the current usage count
func (l *License) GetUses() int {
	return l.Uses
}

// IncrementUses increments the usage count
func (l *License) IncrementUses() {
	l.Uses++
	l.LastUsed = &[]time.Time{time.Now()}[0]
}

// GetUsersCount returns total users count (Ruby: users_count method)
func (l *License) GetUsersCount() int {
	return CalculateUsersCount(l.LicenseUsersCount, l.HasOwner())
}

// GetMachinesCount returns the machines count
func (l *License) GetMachinesCount() int {
	return l.MachinesCount
}

// GetMachinesCoreCount returns the total cores count
func (l *License) GetMachinesCoreCount() int {
	return l.MachinesCoreCount
}

// ===== POLICY OVERRIDE METHODS =====
// Maps from Ruby License model policy override methods

// GetMaxMachines returns max machines (Ruby: max_machines method)
func (l *License) GetMaxMachines() *int {
	if l.MaxMachinesOverride != nil {
		return l.MaxMachinesOverride
	}
	return l.Policy.MaxMachines
}

// HasMaxMachines checks if max machines limit is set (Ruby: max_machines? method)
func (l *License) HasMaxMachines() bool {
	maxMachines := l.GetMaxMachines()
	return maxMachines != nil
}

// SetMaxMachines sets max machines override (Ruby: max_machines= method)
func (l *License) SetMaxMachines(value *int) {
	l.MaxMachinesOverride = value
}

// GetMaxCores returns max cores (Ruby: max_cores method)
func (l *License) GetMaxCores() *int {
	if l.MaxCoresOverride != nil {
		return l.MaxCoresOverride
	}
	return l.Policy.MaxCores
}

// HasMaxCores checks if max cores limit is set (Ruby: max_cores? method)
func (l *License) HasMaxCores() bool {
	maxCores := l.GetMaxCores()
	return maxCores != nil
}

// SetMaxCores sets max cores override (Ruby: max_cores= method)
func (l *License) SetMaxCores(value *int) {
	l.MaxCoresOverride = value
}

// GetMaxUses returns max uses (Ruby: max_uses method)
func (l *License) GetMaxUses() *int {
	if l.MaxUsesOverride != nil {
		return l.MaxUsesOverride
	}
	return l.Policy.MaxUses
}

// HasMaxUses checks if max uses limit is set (Ruby: max_uses? method)
func (l *License) HasMaxUses() bool {
	maxUses := l.GetMaxUses()
	return maxUses != nil
}

// SetMaxUses sets max uses override (Ruby: max_uses= method)
func (l *License) SetMaxUses(value *int) {
	l.MaxUsesOverride = value
}

// GetMaxProcesses returns max processes (Ruby: max_processes method)
func (l *License) GetMaxProcesses() *int {
	if l.MaxProcessesOverride != nil {
		return l.MaxProcessesOverride
	}
	return l.Policy.MaxProcesses
}

// HasMaxProcesses checks if max processes limit is set (Ruby: max_processes? method)
func (l *License) HasMaxProcesses() bool {
	maxProcesses := l.GetMaxProcesses()
	return maxProcesses != nil
}

// SetMaxProcesses sets max processes override (Ruby: max_processes= method)
func (l *License) SetMaxProcesses(value *int) {
	l.MaxProcessesOverride = value
}

// GetMaxUsers returns max users (Ruby: max_users method)
func (l *License) GetMaxUsers() *int {
	if l.MaxUsersOverride != nil {
		return l.MaxUsersOverride
	}
	return l.Policy.MaxUsers
}

// HasMaxUsers checks if max users limit is set (Ruby: max_users? method)
func (l *License) HasMaxUsers() bool {
	maxUsers := l.GetMaxUsers()
	return maxUsers != nil
}

// SetMaxUsers sets max users override (Ruby: max_users= method)
func (l *License) SetMaxUsers(value *int) {
	l.MaxUsersOverride = value
}

// ===== RENEWAL METHODS =====
// Maps from Ruby License model renewal methods

// Renew renews the license (Ruby: renew! method)
func (l *License) Renew() {
	if l.Policy.Duration != nil {
		l.ExpiresAt = CalculateRenewalExpiry(
			l.ExpiresAt,
			l.Policy.Duration,
			l.Policy.RenewsFromExpiry(),
			l.Policy.RenewsFromNow(),
			l.Policy.RenewsFromNowIfExpired(),
		)
	}
	l.UpdatedAt = time.Now()
}

// CanRenew checks if license can be renewed (Ruby: renewable? method)
func (l *License) CanRenew() bool {
	return l.Policy.Duration != nil && !l.IsBanned()
}

// ===== SUSPENSION METHODS =====
// Maps from Ruby License model suspension methods

// Suspend suspends the license (Ruby: suspend! method)
func (l *License) Suspend() {
	l.Suspended = true
	l.UpdatedAt = time.Now()
}

// Reinstate reinstates the license (Ruby: reinstate! method)
func (l *License) Reinstate() {
	l.Suspended = false
	l.UpdatedAt = time.Now()
}

// ===== CHECK-IN METHODS =====
// Maps from Ruby License model check-in methods

// CheckIn performs license check-in (Ruby: check_in! method)
func (l *License) CheckIn() {
	now := time.Now()
	l.LastCheckInAt = &now
	l.UpdatedAt = now
}

// CheckOut performs license check-out (Ruby: check_out! method)
func (l *License) CheckOut() {
	now := time.Now()
	l.LastCheckOutAt = &now
	l.UpdatedAt = now
}

// RequiresCheckIn checks if license requires check-in (Ruby: requires_check_in? method)
func (l *License) RequiresCheckIn() bool {
	return l.Policy.RequiresCheckIn()
}

// IsCheckInOverdue checks if check-in is overdue (Ruby: check_in_overdue? method)
func (l *License) IsCheckInOverdue() bool {
	if !l.RequiresCheckIn() {
		return false
	}

	checkInInterval := l.getCheckInIntervalDuration()
	if checkInInterval == 0 {
		return false
	}

	return IsCheckInOverdue(l.LastCheckInAt, checkInInterval, true)
}

// GetNextCheckInAt returns next check-in time (Ruby: next_check_in_at method)
func (l *License) GetNextCheckInAt() *time.Time {
	if !l.RequiresCheckIn() {
		return nil
	}

	checkInInterval := l.getCheckInIntervalDuration()
	if checkInInterval == 0 {
		return nil
	}

	return CalculateNextCheckInAt(l.LastCheckInAt, checkInInterval, true)
}

// getCheckInIntervalDuration calculates check-in interval in duration
func (l *License) getCheckInIntervalDuration() time.Duration {
	if l.Policy.CheckInInterval == nil || l.Policy.CheckInIntervalCount == nil {
		return 0
	}

	count := time.Duration(*l.Policy.CheckInIntervalCount)

	switch *l.Policy.CheckInInterval {
	case "day":
		return count * 24 * time.Hour
	case "week":
		return count * 7 * 24 * time.Hour
	case "month":
		return count * 30 * 24 * time.Hour // Approximate
	case "year":
		return count * 365 * 24 * time.Hour // Approximate
	default:
		return 0
	}
}
