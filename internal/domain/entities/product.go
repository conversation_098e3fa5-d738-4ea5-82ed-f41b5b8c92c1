﻿package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ProductPlatforms represents a list of supported platforms as JSONB
type ProductPlatforms []string

// <PERSON><PERSON> implements the sql.Scanner interface for JSONB scanning
func (p *ProductPlatforms) Scan(value interface{}) error {
	if value == nil {
		*p = []string{}
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into ProductPlatforms", value)
	}

	if len(data) == 0 {
		*p = []string{}
		return nil
	}

	return json.Unmarshal(data, p)
}

// Value implements the driver.Valuer interface for JSONB storage
func (p ProductPlatforms) Value() (driver.Value, error) {
	if p == nil {
		return "[]", nil
	}
	return json.Marshal(p)
}

type Product struct {
	ID             uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"column:organization_id;type:uuid;not null"`
	Name           string    `json:"name" gorm:"size:255;not null"`
	Code           string    `json:"code" gorm:"size:255;not null"`
	Description    *string   `json:"description,omitempty" gorm:"type:text"`
	URL            *string   `json:"url,omitempty" gorm:"size:512"`

	// Platform and distribution
	Platforms            ProductPlatforms `json:"platforms" gorm:"type:jsonb;default:'[]'"`
	DistributionStrategy *string          `json:"distribution_strategy,omitempty" gorm:"column:distribution_strategy;size:255"`

	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"column:updated_at;not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"column:deleted_at;index"`

	// Relations
	Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
	Policies     []Policy     `json:"policies,omitempty" gorm:"foreignKey:ProductID"`
	Licenses     []License    `json:"licenses,omitempty" gorm:"foreignKey:ProductID"`
}
