package entities

import (
	"fmt"
	"slices"
	"time"
)

// ===== POLICY VALIDATION METHODS =====
// Maps from Ruby Policy model validations with Go type safety

// ValidateBasicFields validates basic policy fields (Ruby: basic validations)
func (p *Policy) ValidateBasicFields() error {
	// Name presence validation (Ruby: validates :name, presence: true)
	if p.Name == "" {
		return fmt.Errorf("name cannot be blank")
	}

	// Duration validation (Ruby: validates :duration, numericality: {...})
	if p.Duration != nil {
		if *p.Duration <= 0 {
			return fmt.Errorf("duration must be greater than 0")
		}
		if *p.Duration > 2147483647 {
			return fmt.Errorf("duration must be less than or equal to 2147483647")
		}
		if *p.Duration < int64(24*time.Hour.Seconds()) {
			return fmt.Errorf("duration must be greater than or equal to 86400 (1 day)")
		}
	}

	// Heartbeat duration validation (Ruby: validates :heartbeat_duration, numericality: {...})
	if p.HeartbeatDuration != nil {
		if *p.HeartbeatDuration <= 0 {
			return fmt.Errorf("heartbeat_duration must be greater than 0")
		}
		if *p.HeartbeatDuration > 2147483647 {
			return fmt.Errorf("heartbeat_duration must be less than or equal to 2147483647")
		}
		if *p.HeartbeatDuration < int(time.Minute.Seconds()) {
			return fmt.Errorf("heartbeat_duration must be greater than or equal to 60 (1 minute)")
		}
	}

	// Max machines validation (Ruby: validates :max_machines, numericality: {...})
	if p.MaxMachines != nil {
		if *p.MaxMachines < 0 {
			return fmt.Errorf("max_machines must be greater than or equal to 0")
		}
		if *p.MaxMachines > 2147483647 {
			return fmt.Errorf("max_machines must be less than or equal to 2147483647")
		}

		// Floating policy validation
		if p.IsFloating() && *p.MaxMachines < 1 {
			return fmt.Errorf("max_machines must be greater than or equal to 1 for floating policy")
		}

		// Node-locked policy validation
		if p.IsNodeLocked() && *p.MaxMachines != 1 {
			return fmt.Errorf("max_machines must be equal to 1 for non-floating policy")
		}
	}

	// Max cores validation (Ruby: validates :max_cores, numericality: {...})
	if p.MaxCores != nil {
		if *p.MaxCores < 1 {
			return fmt.Errorf("max_cores must be greater than or equal to 1")
		}
		if *p.MaxCores > 2147483647 {
			return fmt.Errorf("max_cores must be less than or equal to 2147483647")
		}
	}

	// Max uses validation (Ruby: validates :max_uses, numericality: {...})
	if p.MaxUses != nil {
		if *p.MaxUses < 0 {
			return fmt.Errorf("max_uses must be greater than or equal to 0")
		}
		if *p.MaxUses > 2147483647 {
			return fmt.Errorf("max_uses must be less than or equal to 2147483647")
		}
	}

	// Max processes validation (Ruby: validates :max_processes, numericality: {...})
	if p.MaxProcesses != nil {
		if *p.MaxProcesses <= 0 {
			return fmt.Errorf("max_processes must be greater than 0")
		}
		if *p.MaxProcesses > 2147483647 {
			return fmt.Errorf("max_processes must be less than or equal to 2147483647")
		}
	}

	// Max users validation (Ruby: validates :max_users, numericality: {...})
	if p.MaxUsers != nil {
		if *p.MaxUsers <= 0 {
			return fmt.Errorf("max_users must be greater than 0")
		}
		if *p.MaxUsers > 2147483647 {
			return fmt.Errorf("max_users must be less than or equal to 2147483647")
		}
	}

	// Check-in interval validation (Ruby: validates :check_in_interval, inclusion: {...})
	if p.RequiresCheckIn() {
		if p.CheckInInterval == nil {
			return fmt.Errorf("check_in_interval is required when require_check_in is true")
		}
		validIntervals := []string{"day", "week", "month", "year"}
		if !slices.Contains(validIntervals, *p.CheckInInterval) {
			return fmt.Errorf("check_in_interval must be one of: day, week, month, year")
		}

		// Check-in interval count validation (Ruby: validates :check_in_interval_count, inclusion: {...})
		if p.CheckInIntervalCount == nil {
			return fmt.Errorf("check_in_interval_count is required when require_check_in is true")
		}
		if *p.CheckInIntervalCount < 1 || *p.CheckInIntervalCount > 365 {
			return fmt.Errorf("check_in_interval_count must be a number between 1 and 365 inclusive")
		}
	}

	// Metadata validation (Ruby: validates :metadata, length: {...})
	if len(p.Metadata) > 64 {
		return fmt.Errorf("metadata has too many keys (exceeded limit of 64 keys)")
	}

	return nil
}

// ValidateStrategies validates all strategy fields (Ruby: strategy validations)
func (p *Policy) ValidateStrategies() error {
	// Crypto scheme validation (Ruby: validates :scheme, inclusion: {...})
	if p.Scheme != nil && !p.Scheme.IsValid() {
		return fmt.Errorf("unsupported encryption scheme")
	}

	// Machine uniqueness strategy validation
	if p.MachineUniquenessStrategy != nil && !p.MachineUniquenessStrategy.IsValid() {
		return fmt.Errorf("unsupported machine uniqueness strategy")
	}

	// Machine matching strategy validation
	if p.MachineMatchingStrategy != nil && !p.MachineMatchingStrategy.IsValid() {
		return fmt.Errorf("unsupported machine matching strategy")
	}

	// Component uniqueness strategy validation
	if p.ComponentUniquenessStrategy != nil && !p.ComponentUniquenessStrategy.IsValid() {
		return fmt.Errorf("unsupported component uniqueness strategy")
	}

	// Component matching strategy validation
	if p.ComponentMatchingStrategy != nil && !p.ComponentMatchingStrategy.IsValid() {
		return fmt.Errorf("unsupported component matching strategy")
	}

	// Expiration strategy validation
	if p.ExpirationStrategy != nil && !p.ExpirationStrategy.IsValid() {
		return fmt.Errorf("unsupported expiration strategy")
	}

	// Expiration basis validation
	if p.ExpirationBasis != nil && !p.ExpirationBasis.IsValid() {
		return fmt.Errorf("unsupported expiration basis")
	}

	// Renewal basis validation
	if p.RenewalBasis != nil && !p.RenewalBasis.IsValid() {
		return fmt.Errorf("unsupported renewal basis")
	}

	// Authentication strategy validation
	if p.AuthenticationStrategy != nil && !p.AuthenticationStrategy.IsValid() {
		return fmt.Errorf("unsupported authentication strategy")
	}

	// Heartbeat cull strategy validation
	if p.HeartbeatCullStrategy != nil && !p.HeartbeatCullStrategy.IsValid() {
		return fmt.Errorf("unsupported heartbeat cull strategy")
	}

	// Heartbeat resurrection strategy validation
	if p.HeartbeatResurrectionStrategy != nil && !p.HeartbeatResurrectionStrategy.IsValid() {
		return fmt.Errorf("unsupported heartbeat resurrection strategy")
	}

	// Heartbeat basis validation
	if p.HeartbeatBasis != nil && !p.HeartbeatBasis.IsValid() {
		return fmt.Errorf("unsupported heartbeat basis")
	}

	// Transfer strategy validation
	if p.TransferStrategy != nil && !p.TransferStrategy.IsValid() {
		return fmt.Errorf("unsupported transfer strategy")
	}

	// Machine leasing strategy validation
	if p.MachineLeasing != nil && !p.MachineLeasing.IsValid() {
		return fmt.Errorf("unsupported machine leasing strategy")
	}

	// Process leasing strategy validation
	if p.ProcessLeasing != nil && !p.ProcessLeasing.IsValid() {
		return fmt.Errorf("unsupported process leasing strategy")
	}

	// Overage strategy validation
	if p.OverageStrategy != nil && !p.OverageStrategy.IsValid() {
		return fmt.Errorf("unsupported overage strategy")
	}

	return nil
}

// ValidateHeartbeatCompatibility validates heartbeat strategy compatibility (Ruby: heartbeat validations)
func (p *Policy) ValidateHeartbeatCompatibility() error {
	// Heartbeat cull strategy compatibility with resurrection strategy
	// Ruby: validates :heartbeat_cull_strategy, inclusion: { in: %w[KEEP_DEAD], message: '...' }, if: :always_resurrect_dead?
	if p.AlwaysResurrectsDead() {
		if p.HeartbeatCullStrategy == nil || *p.HeartbeatCullStrategy != HeartbeatKeepDead {
			return fmt.Errorf("incompatible heartbeat cull strategy (must be KEEP_DEAD when resurrection strategy is ALWAYS_REVIVE)")
		}
	}

	return nil
}

// ValidateOverageCompatibility validates overage strategy compatibility with limits (Ruby: overage validations)
func (p *Policy) ValidateOverageCompatibility() error {
	if p.OverageStrategy == nil {
		return nil
	}

	strategy := *p.OverageStrategy

	// Node-locked policy overage validation
	if p.IsNodeLocked() {
		if strategy == OverageAllow125x {
			return fmt.Errorf("incompatible overage strategy (cannot use ALLOW_1_25X_OVERAGE for node-locked policy)")
		}
		if strategy == OverageAllow15x {
			return fmt.Errorf("incompatible overage strategy (cannot use ALLOW_1_5X_OVERAGE for node-locked policy)")
		}
	}

	// Max machines overage validation
	if p.MaxMachines != nil {
		if err := strategy.ValidateWithLimit(*p.MaxMachines, p.IsNodeLocked()); err != nil {
			return fmt.Errorf("max_machines: %w", err)
		}
	}

	// Max cores overage validation
	if p.MaxCores != nil {
		if err := strategy.ValidateWithLimit(*p.MaxCores, false); err != nil {
			return fmt.Errorf("max_cores: %w", err)
		}
	}

	// Max processes overage validation
	if p.MaxProcesses != nil {
		if err := strategy.ValidateWithLimit(*p.MaxProcesses, false); err != nil {
			return fmt.Errorf("max_processes: %w", err)
		}
	}

	// Max users overage validation
	if p.MaxUsers != nil {
		if err := strategy.ValidateWithLimit(*p.MaxUsers, false); err != nil {
			return fmt.Errorf("max_users: %w", err)
		}
	}

	return nil
}

// ValidatePoolCompatibility validates pool compatibility (Ruby: pool validations)
func (p *Policy) ValidatePoolCompatibility() error {
	if p.IsPool() {
		if p.IsEncrypted() {
			return fmt.Errorf("cannot be encrypted and use a pool")
		}
		if p.HasScheme() {
			return fmt.Errorf("cannot use a scheme and use a pool")
		}
	}

	return nil
}

// ValidateSchemeCompatibility validates scheme compatibility (Ruby: scheme validations)
func (p *Policy) ValidateSchemeCompatibility() error {
	// Skip legacy scheme validation - Go implementation uses modern schemes only
	return nil
}

// Validate performs complete policy validation (Ruby: all validations combined)
func (p *Policy) Validate() error {
	if err := p.ValidateBasicFields(); err != nil {
		return err
	}
	if err := p.ValidateStrategies(); err != nil {
		return err
	}
	if err := p.ValidateHeartbeatCompatibility(); err != nil {
		return err
	}
	if err := p.ValidateOverageCompatibility(); err != nil {
		return err
	}
	if err := p.ValidatePoolCompatibility(); err != nil {
		return err
	}
	if err := p.ValidateSchemeCompatibility(); err != nil {
		return err
	}
	return nil
}
