package entities

import (
	"fmt"
	"time"
)

// ===== CRYPTO SCHEMES =====
// Scheme mã hóa/ký số cho license keys - Go implementation uses latest techniques only

type CryptoScheme string

const (
	// Modern schemes only - Go implementation uses latest techniques
	CryptoSchemeRSA2048JWT  CryptoScheme = "RSA_2048_JWT_RS256"
	CryptoSchemeRSA2048Sign CryptoScheme = "RSA_2048_PKCS1_SIGN"     // Latest RSA signing
	CryptoSchemeRSA2048PSS  CryptoScheme = "RSA_2048_PKCS1_PSS_SIGN" // Latest RSA PSS signing
	CryptoSchemeED25519     CryptoScheme = "ED25519_SIGN"            // Modern elliptic curve
)

// IsValid validates the crypto scheme
func (cs CryptoScheme) IsValid() bool {
	switch cs {
	case CryptoSchemeRSA2048JWT, CryptoSchemeRSA2048Sign, CryptoSchemeRSA2048PSS, CryptoSchemeED25519:
		return true
	default:
		return false
	}
}

// ===== MACHINE UNIQUENESS STRATEGIES =====
// <PERSON><PERSON><PERSON> soát tính duy nhất của machine fingerprint

type MachineUniquenessStrategy string

const (
	MachineUniquePerOrganization MachineUniquenessStrategy = "UNIQUE_PER_ACCOUNT" // Ruby compatibility
	MachineUniquePerProduct      MachineUniquenessStrategy = "UNIQUE_PER_PRODUCT"
	MachineUniquePerPolicy       MachineUniquenessStrategy = "UNIQUE_PER_POLICY"
	MachineUniquePerLicense      MachineUniquenessStrategy = "UNIQUE_PER_LICENSE"
)

// IsValid validates the machine uniqueness strategy
func (mus MachineUniquenessStrategy) IsValid() bool {
	switch mus {
	case MachineUniquePerOrganization, MachineUniquePerProduct, MachineUniquePerPolicy, MachineUniquePerLicense:
		return true
	default:
		return false
	}
}

// Rank returns the uniqueness rank (higher = more restrictive)
func (mus MachineUniquenessStrategy) Rank() int {
	switch mus {
	case MachineUniquePerOrganization:
		return 4
	case MachineUniquePerProduct:
		return 3
	case MachineUniquePerPolicy:
		return 2
	case MachineUniquePerLicense:
		return 1
	default:
		return -1
	}
}

// ===== COMPONENT UNIQUENESS STRATEGIES =====
// Kiểm soát tính duy nhất của hardware component fingerprint

type ComponentUniquenessStrategy string

const (
	ComponentUniquePerOrganization ComponentUniquenessStrategy = "UNIQUE_PER_ACCOUNT" // Ruby compatibility
	ComponentUniquePerProduct      ComponentUniquenessStrategy = "UNIQUE_PER_PRODUCT"
	ComponentUniquePerPolicy       ComponentUniquenessStrategy = "UNIQUE_PER_POLICY"
	ComponentUniquePerLicense      ComponentUniquenessStrategy = "UNIQUE_PER_LICENSE"
	ComponentUniquePerMachine      ComponentUniquenessStrategy = "UNIQUE_PER_MACHINE"
)

// IsValid validates the component uniqueness strategy
func (cus ComponentUniquenessStrategy) IsValid() bool {
	switch cus {
	case ComponentUniquePerOrganization, ComponentUniquePerProduct, ComponentUniquePerPolicy, ComponentUniquePerLicense, ComponentUniquePerMachine:
		return true
	default:
		return false
	}
}

// Rank returns the uniqueness rank (higher = more restrictive)
func (cus ComponentUniquenessStrategy) Rank() int {
	switch cus {
	case ComponentUniquePerOrganization:
		return 4
	case ComponentUniquePerProduct:
		return 3
	case ComponentUniquePerPolicy:
		return 2
	case ComponentUniquePerLicense:
		return 1
	case ComponentUniquePerMachine:
		return 0
	default:
		return -1
	}
}

// ===== MATCHING STRATEGIES =====
// Matching fingerprint khi validate license

type MatchingStrategy string

const (
	MatchAny  MatchingStrategy = "MATCH_ANY"
	MatchTwo  MatchingStrategy = "MATCH_TWO"
	MatchMost MatchingStrategy = "MATCH_MOST"
	MatchAll  MatchingStrategy = "MATCH_ALL"
)

// IsValid validates the matching strategy
func (ms MatchingStrategy) IsValid() bool {
	switch ms {
	case MatchAny, MatchTwo, MatchMost, MatchAll:
		return true
	default:
		return false
	}
}

// RequiredMatches calculates required matches based on total components
func (ms MatchingStrategy) RequiredMatches(totalComponents int) int {
	switch ms {
	case MatchAny:
		return 1
	case MatchTwo:
		if totalComponents >= 2 {
			return 2
		}
		return totalComponents
	case MatchMost:
		return (totalComponents + 1) / 2 // Ceiling division
	case MatchAll:
		return totalComponents
	default:
		return totalComponents
	}
}

// ===== EXPIRATION STRATEGIES =====
// Xử lý khi license hết hạn

type ExpirationStrategy string

const (
	ExpirationRestrictAccess ExpirationStrategy = "RESTRICT_ACCESS"
	ExpirationRevokeAccess   ExpirationStrategy = "REVOKE_ACCESS"
	ExpirationMaintainAccess ExpirationStrategy = "MAINTAIN_ACCESS"
	ExpirationAllowAccess    ExpirationStrategy = "ALLOW_ACCESS"
)

// IsValid validates the expiration strategy
func (es ExpirationStrategy) IsValid() bool {
	switch es {
	case ExpirationRestrictAccess, ExpirationRevokeAccess, ExpirationMaintainAccess, ExpirationAllowAccess:
		return true
	default:
		return false
	}
}

// ===== EXPIRATION BASIS =====
// Cơ sở tính toán thời điểm bắt đầu đếm ngược thời hạn

type ExpirationBasis string

const (
	ExpirationFromCreation        ExpirationBasis = "FROM_CREATION"
	ExpirationFromFirstValidation ExpirationBasis = "FROM_FIRST_VALIDATION"
	ExpirationFromFirstActivation ExpirationBasis = "FROM_FIRST_ACTIVATION"
	ExpirationFromFirstDownload   ExpirationBasis = "FROM_FIRST_DOWNLOAD"
	ExpirationFromFirstUse        ExpirationBasis = "FROM_FIRST_USE"
)

// IsValid validates the expiration basis
func (eb ExpirationBasis) IsValid() bool {
	switch eb {
	case ExpirationFromCreation, ExpirationFromFirstValidation, ExpirationFromFirstActivation, ExpirationFromFirstDownload, ExpirationFromFirstUse:
		return true
	default:
		return false
	}
}

// ===== RENEWAL BASIS =====
// Cơ sở tính toán thời điểm bắt đầu cho chu kỳ license mới khi renew

type RenewalBasis string

const (
	RenewalFromExpiry       RenewalBasis = "FROM_EXPIRY"
	RenewalFromNow          RenewalBasis = "FROM_NOW"
	RenewalFromNowIfExpired RenewalBasis = "FROM_NOW_IF_EXPIRED"
)

// IsValid validates the renewal basis
func (rb RenewalBasis) IsValid() bool {
	switch rb {
	case RenewalFromExpiry, RenewalFromNow, RenewalFromNowIfExpired:
		return true
	default:
		return false
	}
}

// ===== TRANSFER STRATEGIES =====
// Xử lý expiry date khi transfer license

type TransferStrategy string

const (
	TransferResetExpiry TransferStrategy = "RESET_EXPIRY"
	TransferKeepExpiry  TransferStrategy = "KEEP_EXPIRY"
)

// IsValid validates the transfer strategy
func (ts TransferStrategy) IsValid() bool {
	switch ts {
	case TransferResetExpiry, TransferKeepExpiry:
		return true
	default:
		return false
	}
}

// ===== AUTHENTICATION STRATEGIES =====
// Xác thực khi validate license

type AuthenticationStrategy string

const (
	AuthToken   AuthenticationStrategy = "TOKEN"
	AuthLicense AuthenticationStrategy = "LICENSE"
	AuthSession AuthenticationStrategy = "SESSION"
	AuthMixed   AuthenticationStrategy = "MIXED"
	AuthNone    AuthenticationStrategy = "NONE"
)

// IsValid validates the authentication strategy
func (as AuthenticationStrategy) IsValid() bool {
	switch as {
	case AuthToken, AuthLicense, AuthSession, AuthMixed, AuthNone:
		return true
	default:
		return false
	}
}

// SupportsTokenAuth checks if strategy supports token authentication
func (as AuthenticationStrategy) SupportsTokenAuth() bool {
	return as == AuthToken || as == AuthMixed
}

// SupportsLicenseAuth checks if strategy supports license authentication
func (as AuthenticationStrategy) SupportsLicenseAuth() bool {
	return as == AuthLicense || as == AuthMixed
}

// SupportsSessionAuth checks if strategy supports session authentication
func (as AuthenticationStrategy) SupportsSessionAuth() bool {
	return as == AuthSession || as == AuthMixed
}

// SupportsAuth checks if any authentication is required
func (as AuthenticationStrategy) SupportsAuth() bool {
	return as != AuthNone
}

// ===== OVERAGE STRATEGIES =====
// Xử lý khi vượt quá giới hạn tài nguyên

type OverageStrategy string

const (
	OverageAlwaysAllow OverageStrategy = "ALWAYS_ALLOW_OVERAGE"
	OverageAllow125x   OverageStrategy = "ALLOW_1_25X_OVERAGE"
	OverageAllow15x    OverageStrategy = "ALLOW_1_5X_OVERAGE"
	OverageAllow2x     OverageStrategy = "ALLOW_2X_OVERAGE"
	OverageNoOverage   OverageStrategy = "NO_OVERAGE"
)

// IsValid validates the overage strategy
func (os OverageStrategy) IsValid() bool {
	switch os {
	case OverageAlwaysAllow, OverageAllow125x, OverageAllow15x, OverageAllow2x, OverageNoOverage:
		return true
	default:
		return false
	}
}

// MaxOverageMultiplier returns the maximum overage multiplier
func (os OverageStrategy) MaxOverageMultiplier() float64 {
	switch os {
	case OverageAlwaysAllow:
		return -1 // Unlimited
	case OverageAllow125x:
		return 1.25
	case OverageAllow15x:
		return 1.5
	case OverageAllow2x:
		return 2.0
	case OverageNoOverage:
		return 1.0
	default:
		return 1.0
	}
}

// AllowsOverage checks if strategy allows any overage
func (os OverageStrategy) AllowsOverage() bool {
	return os != OverageNoOverage
}

// ValidateWithLimit validates overage compatibility with resource limits
func (os OverageStrategy) ValidateWithLimit(limit int, isNodeLocked bool) error {
	if isNodeLocked && (os == OverageAllow125x || os == OverageAllow15x) {
		return fmt.Errorf("incompatible overage strategy (cannot use %s for node-locked policy)", os)
	}

	if os == OverageAllow125x && limit%4 != 0 {
		return fmt.Errorf("incompatible overage strategy (cannot use ALLOW_1_25X_OVERAGE with limit %d not divisible by 4)", limit)
	}

	if os == OverageAllow15x && limit%2 != 0 {
		return fmt.Errorf("incompatible overage strategy (cannot use ALLOW_1_5X_OVERAGE with limit %d not divisible by 2)", limit)
	}

	return nil
}

// ===== HEARTBEAT CULL STRATEGIES =====
// Xử lý machine/process khi "chết"

type HeartbeatCullStrategy string

const (
	HeartbeatDeactivateDead HeartbeatCullStrategy = "DEACTIVATE_DEAD"
	HeartbeatKeepDead       HeartbeatCullStrategy = "KEEP_DEAD"
)

// IsValid validates the heartbeat cull strategy
func (hcs HeartbeatCullStrategy) IsValid() bool {
	switch hcs {
	case HeartbeatDeactivateDead, HeartbeatKeepDead:
		return true
	default:
		return false
	}
}

// ===== HEARTBEAT RESURRECTION STRATEGIES =====
// "Hồi sinh" machine đã bị coi là chết

type HeartbeatResurrectionStrategy string

const (
	HeartbeatAlwaysRevive   HeartbeatResurrectionStrategy = "ALWAYS_REVIVE"
	Heartbeat15MinuteRevive HeartbeatResurrectionStrategy = "15_MINUTE_REVIVE"
	Heartbeat10MinuteRevive HeartbeatResurrectionStrategy = "10_MINUTE_REVIVE"
	Heartbeat5MinuteRevive  HeartbeatResurrectionStrategy = "5_MINUTE_REVIVE"
	Heartbeat2MinuteRevive  HeartbeatResurrectionStrategy = "2_MINUTE_REVIVE"
	Heartbeat1MinuteRevive  HeartbeatResurrectionStrategy = "1_MINUTE_REVIVE"
	HeartbeatNoRevive       HeartbeatResurrectionStrategy = "NO_REVIVE"
)

// IsValid validates the heartbeat resurrection strategy
func (hrs HeartbeatResurrectionStrategy) IsValid() bool {
	switch hrs {
	case HeartbeatAlwaysRevive, Heartbeat15MinuteRevive, Heartbeat10MinuteRevive, Heartbeat5MinuteRevive, Heartbeat2MinuteRevive, Heartbeat1MinuteRevive, HeartbeatNoRevive:
		return true
	default:
		return false
	}
}

// GetTTLSeconds returns the TTL in seconds for resurrection
func (hrs HeartbeatResurrectionStrategy) GetTTLSeconds() int {
	switch hrs {
	case Heartbeat15MinuteRevive:
		return int(15 * time.Minute.Seconds())
	case Heartbeat10MinuteRevive:
		return int(10 * time.Minute.Seconds())
	case Heartbeat5MinuteRevive:
		return int(5 * time.Minute.Seconds())
	case Heartbeat2MinuteRevive:
		return int(2 * time.Minute.Seconds())
	case Heartbeat1MinuteRevive:
		return int(1 * time.Minute.Seconds())
	default:
		return 0
	}
}

// AllowsResurrection checks if strategy allows resurrection
func (hrs HeartbeatResurrectionStrategy) AllowsResurrection() bool {
	return hrs != HeartbeatNoRevive
}

// ===== HEARTBEAT BASIS =====
// Cơ sở tính toán thời điểm bắt đầu yêu cầu heartbeat

type HeartbeatBasis string

const (
	HeartbeatFromCreation  HeartbeatBasis = "FROM_CREATION"
	HeartbeatFromFirstPing HeartbeatBasis = "FROM_FIRST_PING"
)

// IsValid validates the heartbeat basis
func (hb HeartbeatBasis) IsValid() bool {
	switch hb {
	case HeartbeatFromCreation, HeartbeatFromFirstPing:
		return true
	default:
		return false
	}
}

// ===== LEASING STRATEGIES =====
// Phân bổ lease cho machines/processes

type LeasingStrategy string

const (
	LeasingPerLicense LeasingStrategy = "PER_LICENSE"
	LeasingPerMachine LeasingStrategy = "PER_MACHINE"
	LeasingPerUser    LeasingStrategy = "PER_USER"
)

// IsValid validates the leasing strategy
func (ls LeasingStrategy) IsValid() bool {
	switch ls {
	case LeasingPerLicense, LeasingPerMachine, LeasingPerUser:
		return true
	default:
		return false
	}
}
