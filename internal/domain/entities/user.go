package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type User struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Email     string    `json:"email" gorm:"size:255;uniqueIndex;not null"`
	Password  string    `json:"-" gorm:"size:255;not null"`
	FirstName *string   `json:"first_name,omitempty" gorm:"column:first_name;size:255"`
	LastName  *string   `json:"last_name,omitempty" gorm:"column:last_name;size:255"`

	// Simple status
	Status string `json:"status" gorm:"size:50;default:'active';check:status IN ('active', 'inactive', 'suspended')"`

	// 2FA support
	TOTPSecret  *string `json:"-" gorm:"column:totp_secret;type:text"`
	TOTPEnabled bool    `json:"totp_enabled" gorm:"column:totp_enabled;default:false"`

	// Password reset
	PasswordResetToken     *string    `json:"-" gorm:"column:password_reset_token;size:255"`
	PasswordResetExpiresAt *time.Time `json:"-" gorm:"column:password_reset_expires_at"`

	// User management
	BannedAt  *time.Time `json:"banned_at,omitempty" gorm:"column:banned_at"`
	LastLogin *time.Time `json:"last_login,omitempty" gorm:"column:last_login"`

	Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"column:updated_at;not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"column:deleted_at;index"`

	// Relations
	Organizations []UsersOrganization `json:"organizations,omitempty" gorm:"foreignKey:UserID"`
	Sessions      []Session           `json:"sessions,omitempty" gorm:"foreignKey:UserID"`
	APITokens     []APIToken          `json:"api_tokens,omitempty" gorm:"foreignKey:UserID"`
	Machines      []Machine           `json:"machines,omitempty" gorm:"foreignKey:OwnerID"`
}

// User statuses
const (
	UserStatusActive    = "active"
	UserStatusInactive  = "inactive"
	UserStatusSuspended = "suspended"
)

// IsActive checks if the user is active
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive && u.BannedAt == nil
}

// IsBanned checks if the user is banned
func (u *User) IsBanned() bool {
	return u.BannedAt != nil
}

// Has2FAEnabled checks if the user has 2FA enabled
func (u *User) Has2FAEnabled() bool {
	return u.TOTPEnabled && u.TOTPSecret != nil
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	var name string
	if u.FirstName != nil {
		name = *u.FirstName
	}
	if u.LastName != nil {
		if name != "" {
			name += " "
		}
		name += *u.LastName
	}
	return name
}
