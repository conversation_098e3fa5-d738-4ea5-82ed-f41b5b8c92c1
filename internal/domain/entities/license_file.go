package entities

import (
	"fmt"
	"regexp"
	"time"

	"github.com/google/uuid"
)

// LicenseFile represents a generated license certificate file (Ruby: LicenseFile model)
// This is a non-persisted entity that represents the output of the checkout service
// Maps directly from Ruby LicenseFile class with Go improvements and type safety
type LicenseFile struct {
	// === CORE FIELDS (Ruby: primary attributes) ===
	ID             uuid.UUID  `json:"id"`                                    // Generated UUID for this file
	OrganizationID uuid.UUID  `json:"organization_id"`                      // Ruby: account_id
	LicenseID      uuid.UUID  `json:"license_id"`                           // License this file was generated for
	Certificate    string     `json:"certificate"`                          // The actual license file content
	IssuedAt       time.Time  `json:"issued_at"`                           // When this file was issued
	ExpiresAt      *time.Time `json:"expires_at,omitempty"`                // When this file expires (if TTL set)
	TTL            *int       `json:"ttl,omitempty"`                       // Time-to-live in seconds
	Includes       []string   `json:"includes"`                            // Included relationships in the file
	Algorithm      string     `json:"algorithm"`                           // Cryptographic algorithm used

	// === RELATIONSHIPS (Ruby: associations) ===
	// These are loaded lazily and not persisted
	Organization *Organization `json:"organization,omitempty"` // Organization that owns this file
	License      *License      `json:"license,omitempty"`      // License this file represents
	Product      *Product      `json:"product,omitempty"`      // Product from license
	Owner        interface{}   `json:"owner,omitempty"`        // License owner (polymorphic)
}

// Validation constants (Ruby: validation patterns)
var (
	LicenseFileHeaderPattern = regexp.MustCompile(`\A-----BEGIN LICENSE FILE-----\n`)
	LicenseFileFooterPattern = regexp.MustCompile(`-----END LICENSE FILE-----\n*\z`)
)

// Supported algorithms (Ruby: ALGORITHMS constant)
var LicenseFileAlgorithms = []string{
	"base64+ed25519",
	"base64+rsa-pss-sha256",
	"base64+rsa-sha256",
	"aes-256-gcm+ed25519",
	"aes-256-gcm+rsa-pss-sha256",
	"aes-256-gcm+rsa-sha256",
}

// NewLicenseFile creates a new license file instance (Ruby: initialize)
func NewLicenseFile(organizationID, licenseID uuid.UUID) *LicenseFile {
	return &LicenseFile{
		ID:             uuid.New(),
		OrganizationID: organizationID,
		LicenseID:      licenseID,
		IssuedAt:       time.Now(),
		Includes:       make([]string, 0),
	}
}

// ===== VALIDATION METHODS (Ruby: validation methods) =====

// IsValid validates the license file structure and content (Ruby: valid? method)
func (lf *LicenseFile) IsValid() error {
	// Validate required fields (Ruby: validates presence)
	if lf.OrganizationID == uuid.Nil {
		return fmt.Errorf("organization_id must be present")
	}
	if lf.LicenseID == uuid.Nil {
		return fmt.Errorf("license_id must be present")
	}
	if lf.Certificate == "" {
		return fmt.Errorf("certificate must be present")
	}
	if lf.IssuedAt.IsZero() {
		return fmt.Errorf("issued_at must be present")
	}

	// Validate certificate format (Ruby: validates_format_of)
	if !LicenseFileHeaderPattern.MatchString(lf.Certificate) {
		return fmt.Errorf("invalid certificate prefix")
	}
	if !LicenseFileFooterPattern.MatchString(lf.Certificate) {
		return fmt.Errorf("invalid certificate suffix")
	}

	// Validate TTL (Ruby: validates_numericality_of)
	if lf.TTL != nil && *lf.TTL < 3600 {
		return fmt.Errorf("TTL must be greater than or equal to 3600 seconds (1 hour)")
	}

	// Validate algorithm (Ruby: validates_inclusion_of)
	if !lf.IsValidAlgorithm() {
		return fmt.Errorf("invalid algorithm: %s", lf.Algorithm)
	}

	return nil
}

// IsValidAlgorithm checks if the algorithm is supported (Ruby: algorithm validation)
func (lf *LicenseFile) IsValidAlgorithm() bool {
	for _, alg := range LicenseFileAlgorithms {
		if lf.Algorithm == alg {
			return true
		}
	}
	return false
}

// ===== LIFECYCLE METHODS (Ruby: lifecycle methods) =====

// IsPersisted returns false as license files are not persisted (Ruby: persisted? method)
func (lf *LicenseFile) IsPersisted() bool {
	return false
}

// HasExpiry checks if the license file has an expiration time (Ruby: expires? method)
func (lf *LicenseFile) HasExpiry() bool {
	return lf.ExpiresAt != nil
}

// IsExpired checks if the license file has expired (Ruby: expired? method)
func (lf *LicenseFile) IsExpired() bool {
	if !lf.HasExpiry() {
		return false
	}
	return lf.ExpiresAt.Before(time.Now())
}

// IsExpiring checks if the license file is expiring soon (Ruby: expiring? method)
func (lf *LicenseFile) IsExpiring() bool {
	if !lf.HasExpiry() {
		return false
	}
	now := time.Now()
	return lf.ExpiresAt.After(now) && lf.ExpiresAt.Before(now.Add(30*24*time.Hour)) // 30 days
}

// IsDesynchronized checks if the issued time is in the future (Ruby: desync? method)
func (lf *LicenseFile) IsDesynchronized() bool {
	return lf.IssuedAt.After(time.Now())
}

// ===== RELATIONSHIP METHODS (Ruby: association methods) =====

// GetProduct returns the product from the license (Ruby: product method)
func (lf *LicenseFile) GetProduct() *Product {
	if lf.Product != nil {
		return lf.Product
	}
	if lf.License != nil {
		return &lf.License.Product
	}
	return nil
}

// GetOwner returns the license owner (Ruby: owner method)
func (lf *LicenseFile) GetOwner() interface{} {
	if lf.Owner != nil {
		return lf.Owner
	}
	if lf.License != nil {
		// Return the license owner based on owner type
		// This would need to be resolved from the appropriate repository
		return nil // TODO: Implement owner resolution
	}
	return nil
}

// ===== UTILITY METHODS =====

// GetTTLDuration returns TTL as time.Duration (Ruby: ttl conversion)
func (lf *LicenseFile) GetTTLDuration() *time.Duration {
	if lf.TTL == nil {
		return nil
	}
	duration := time.Duration(*lf.TTL) * time.Second
	return &duration
}

// SetTTLDuration sets TTL from time.Duration
func (lf *LicenseFile) SetTTLDuration(duration *time.Duration) {
	if duration == nil {
		lf.TTL = nil
		return
	}
	ttl := int(duration.Seconds())
	lf.TTL = &ttl
}

// HasInclude checks if a specific include is present (Ruby: includes check)
func (lf *LicenseFile) HasInclude(include string) bool {
	for _, inc := range lf.Includes {
		if inc == include {
			return true
		}
	}
	return false
}

// AddInclude adds an include to the list if not already present
func (lf *LicenseFile) AddInclude(include string) {
	if !lf.HasInclude(include) {
		lf.Includes = append(lf.Includes, include)
	}
}

// RemoveInclude removes an include from the list
func (lf *LicenseFile) RemoveInclude(include string) {
	for i, inc := range lf.Includes {
		if inc == include {
			lf.Includes = append(lf.Includes[:i], lf.Includes[i+1:]...)
			break
		}
	}
}

// String returns a string representation of the license file
func (lf *LicenseFile) String() string {
	return fmt.Sprintf("LicenseFile{ID: %s, LicenseID: %s, Algorithm: %s, IssuedAt: %s}",
		lf.ID, lf.LicenseID, lf.Algorithm, lf.IssuedAt.Format(time.RFC3339))
}
