package entities

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/google/uuid"
)

// ===== MACHINE VALIDATION METHODS =====
// Maps from Ruby Machine model validation methods with Go type safety

// ValidateFingerprint validates the machine fingerprint (Ruby: validates :fingerprint, presence: true, length: { maximum: 255 })
func (m *Machine) ValidateFingerprint() error {
	if m.Fingerprint == "" {
		return fmt.Errorf("fingerprint can't be blank")
	}
	if len(m.Fingerprint) > 255 {
		return fmt.Errorf("fingerprint is too long (maximum is 255 characters)")
	}
	return nil
}

// ValidateName validates the machine name (Ruby: validates :name, length: { maximum: 255 })
func (m *Machine) ValidateName() error {
	if m.Name != nil && len(*m.Name) > 255 {
		return fmt.Errorf("name is too long (maximum is 255 characters)")
	}
	return nil
}

// ValidateHostname validates the machine hostname (Ruby: validates :hostname, length: { maximum: 255 })
func (m *Machine) ValidateHostname() error {
	if m.Hostname != nil && len(*m.Hostname) > 255 {
		return fmt.Errorf("hostname is too long (maximum is 255 characters)")
	}
	return nil
}

// ValidatePlatform validates the machine platform (Ruby: validates :platform, length: { maximum: 255 })
func (m *Machine) ValidatePlatform() error {
	if m.Platform != nil && len(*m.Platform) > 255 {
		return fmt.Errorf("platform is too long (maximum is 255 characters)")
	}
	return nil
}

// ValidateIP validates the machine IP address (Ruby: validates :ip, length: { maximum: 45 })
func (m *Machine) ValidateIP() error {
	if m.IP != nil && len(*m.IP) > 45 {
		return fmt.Errorf("ip is too long (maximum is 45 characters)")
	}
	// Basic IP format validation (IPv4/IPv6)
	if m.IP != nil && *m.IP != "" {
		ipPattern := `^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$`
		matched, _ := regexp.MatchString(ipPattern, *m.IP)
		if !matched {
			return fmt.Errorf("ip is not a valid IPv4 or IPv6 address")
		}
	}
	return nil
}

// ValidateStatus validates the machine status (Ruby: validates :status, inclusion: { in: %w[active inactive] })
func (m *Machine) ValidateStatus() error {
	if !m.Status.IsValid() {
		return fmt.Errorf("status is not included in the list (must be active or inactive)")
	}
	return nil
}

// ValidateCores validates the machine cores (Ruby: validates :cores, numericality: { greater_than_or_equal_to: 1 })
func (m *Machine) ValidateCores() error {
	return ValidateCores(m.Cores)
}

// ValidateMaxProcessesOverride validates max processes override (Ruby: validates :max_processes_override, numericality: { greater_than_or_equal_to: 0 })
func (m *Machine) ValidateMaxProcessesOverride() error {
	return ValidateMaxProcessesOverride(m.MaxProcessesOverride)
}

// ValidateMetadata validates metadata size (Ruby: validates :metadata, length: { maximum: 64 })
func (m *Machine) ValidateMetadata() error {
	return ValidateMetadata(m.Metadata)
}

// ValidateComponents validates machine components
func (m *Machine) ValidateComponents() error {
	for i, component := range m.Components {
		if !component.IsValid() {
			return fmt.Errorf("component %d is invalid (name and fingerprint are required)", i)
		}
		if len(component.Name) > 255 {
			return fmt.Errorf("component %d name is too long (maximum is 255 characters)", i)
		}
		if len(component.Fingerprint) > 255 {
			return fmt.Errorf("component %d fingerprint is too long (maximum is 255 characters)", i)
		}
	}
	return nil
}

// ===== BUSINESS LOGIC VALIDATION METHODS =====
// Maps from Ruby Machine model business validation methods

// ValidateOwnership validates machine ownership (Ruby: validates :owner, presence: true, if: :requires_owner?)
func (m *Machine) ValidateOwnership() error {
	if m.RequiresOwner() && !m.HasOwner() {
		return fmt.Errorf("owner can't be blank when required by policy")
	}
	return nil
}

// RequiresOwner checks if machine requires an owner (Ruby: requires_owner? method)
func (m *Machine) RequiresOwner() bool {
	// TODO: Implement based on policy requirements
	// This would check if the policy requires machine ownership
	return false
}

// ValidateUniqueness validates machine uniqueness constraints (Ruby: validates :fingerprint, uniqueness: { scope: [...] })
func (m *Machine) ValidateUniqueness() error {
	// This validation would typically be handled at the repository/service layer
	// where we have access to the database to check for duplicates
	// For now, we'll just validate the fingerprint format
	return m.ValidateFingerprint()
}

// ValidateLimits validates machine limits (Ruby: validates machine count limits)
func (m *Machine) ValidateLimits() error {
	// This validation would typically be handled at the service layer
	// where we have access to count existing machines
	// For now, we'll just validate basic constraints
	return nil
}

// ValidateHeartbeatConfiguration validates heartbeat configuration
func (m *Machine) ValidateHeartbeatConfiguration() error {
	// Validate heartbeat JID format if present
	if m.HeartbeatJID != nil && len(*m.HeartbeatJID) > 255 {
		return fmt.Errorf("heartbeat_jid is too long (maximum is 255 characters)")
	}
	return nil
}

// ===== COMPREHENSIVE VALIDATION METHOD =====

// Validate performs comprehensive validation of the machine (Ruby: validates method calls)
func (m *Machine) Validate() error {
	// Core field validations
	if err := m.ValidateFingerprint(); err != nil {
		return err
	}
	if err := m.ValidateName(); err != nil {
		return err
	}
	if err := m.ValidateHostname(); err != nil {
		return err
	}
	if err := m.ValidatePlatform(); err != nil {
		return err
	}
	if err := m.ValidateIP(); err != nil {
		return err
	}
	if err := m.ValidateStatus(); err != nil {
		return err
	}
	if err := m.ValidateCores(); err != nil {
		return err
	}
	if err := m.ValidateMaxProcessesOverride(); err != nil {
		return err
	}
	if err := m.ValidateMetadata(); err != nil {
		return err
	}
	if err := m.ValidateComponents(); err != nil {
		return err
	}

	// Business logic validations
	if err := m.ValidateOwnership(); err != nil {
		return err
	}
	if err := m.ValidateUniqueness(); err != nil {
		return err
	}
	if err := m.ValidateLimits(); err != nil {
		return err
	}
	if err := m.ValidateHeartbeatConfiguration(); err != nil {
		return err
	}

	return nil
}

// ===== SEARCH AND FILTER VALIDATION =====

// ValidateSearchQuery validates search query parameters (Ruby: search scope validations)
func ValidateSearchQuery(query string, scope SearchScope) error {
	if query == "" {
		return fmt.Errorf("search query cannot be empty")
	}

	if len(query) > 1000 {
		return fmt.Errorf("search query is too long (maximum is 1000 characters)")
	}

	// Validate scope-specific constraints
	switch scope {
	case SearchScopeID:
		// UUID format validation
		uuidPattern := `^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$`
		matched, _ := regexp.MatchString(uuidPattern, strings.ToLower(query))
		if !matched {
			return fmt.Errorf("invalid UUID format for ID search")
		}
	case SearchScopeFingerprint:
		if len(query) > 255 {
			return fmt.Errorf("fingerprint search query is too long (maximum is 255 characters)")
		}
	case SearchScopeName:
		if len(query) > 255 {
			return fmt.Errorf("name search query is too long (maximum is 255 characters)")
		}
	}

	return nil
}

// ===== PROOF VALIDATION =====

// ValidateProofDataset validates proof dataset for machine (Ruby: proof validation)
func (m *Machine) ValidateProofDataset(dataset ProofDataset) error {
	if dataset.Organization.ID == uuid.Nil {
		return fmt.Errorf("proof dataset organization ID cannot be empty")
	}
	if dataset.Product.ID == uuid.Nil {
		return fmt.Errorf("proof dataset product ID cannot be empty")
	}
	if dataset.Policy.ID == uuid.Nil {
		return fmt.Errorf("proof dataset policy ID cannot be empty")
	}
	if dataset.License.ID == uuid.Nil {
		return fmt.Errorf("proof dataset license ID cannot be empty")
	}
	if dataset.License.Key == "" {
		return fmt.Errorf("proof dataset license key cannot be empty")
	}
	if dataset.Machine.ID == uuid.Nil {
		return fmt.Errorf("proof dataset machine ID cannot be empty")
	}
	if dataset.Machine.Fingerprint == "" {
		return fmt.Errorf("proof dataset machine fingerprint cannot be empty")
	}
	if dataset.Timestamp.IsZero() {
		return fmt.Errorf("proof dataset timestamp cannot be zero")
	}
	return nil
}

// ===== ACTIVATION/DEACTIVATION VALIDATION =====

// ValidateActivation validates machine activation (Ruby: activation validation)
func (m *Machine) ValidateActivation() error {
	if m.IsActive() {
		return fmt.Errorf("machine is already active")
	}
	return nil
}

// ValidateDeactivation validates machine deactivation (Ruby: deactivation validation)
func (m *Machine) ValidateDeactivation() error {
	if m.IsInactive() {
		return fmt.Errorf("machine is already inactive")
	}
	return nil
}
