package entities

import (
	"fmt"
	"time"

	"github.com/google/uuid"
)

// ===== MACHINE STATUS TYPES =====
// Maps from Ruby Machine model status constants with Go type safety

// MachineStatus represents the basic status of a machine (Ruby: status field)
type MachineStatus string

const (
	MachineStatusActive   MachineStatus = "active"   // Machine is active and can use license
	MachineStatusInactive MachineStatus = "inactive" // Machine is deactivated and cannot use license
)

// IsValid validates the machine status
func (ms MachineStatus) IsValid() bool {
	switch ms {
	case MachineStatusActive, MachineStatusInactive:
		return true
	default:
		return false
	}
}

// String returns the string representation
func (ms MachineStatus) String() string {
	return string(ms)
}

// ===== HEARTBEAT STATUS TYPES =====
// Maps from Ruby Machine model heartbeat status logic with Go type safety

// HeartbeatStatus represents the heartbeat status of a machine (Ruby: heartbeat_status method)
type HeartbeatStatus string

const (
	HeartbeatStatusNotStarted  HeartbeatStatus = "NOT_STARTED" // Machine hasn't started heartbeat yet
	HeartbeatStatusAlive       HeartbeatStatus = "ALIVE"       // Machine is alive and sending heartbeats
	HeartbeatStatusDead        HeartbeatStatus = "DEAD"        // Machine is dead (missed heartbeat)
	HeartbeatStatusResurrected HeartbeatStatus = "RESURRECTED" // Machine was resurrected after being dead
)

// IsValid validates the heartbeat status
func (hs HeartbeatStatus) IsValid() bool {
	switch hs {
	case HeartbeatStatusNotStarted, HeartbeatStatusAlive, HeartbeatStatusDead, HeartbeatStatusResurrected:
		return true
	default:
		return false
	}
}

// String returns the string representation
func (hs HeartbeatStatus) String() string {
	return string(hs)
}

// IsAlive checks if the heartbeat status indicates the machine is alive
func (hs HeartbeatStatus) IsAlive() bool {
	return hs == HeartbeatStatusAlive || hs == HeartbeatStatusResurrected
}

// IsDead checks if the heartbeat status indicates the machine is dead
func (hs HeartbeatStatus) IsDead() bool {
	return hs == HeartbeatStatusDead
}

// IsOk checks if the heartbeat status is acceptable (not dead)
func (hs HeartbeatStatus) IsOk() bool {
	return hs == HeartbeatStatusNotStarted || hs.IsAlive()
}

// ===== MACHINE CONSTANTS =====
// Maps from Ruby Machine model constants with Go type safety

// HeartbeatDrift represents the allowed drift for heartbeat timing (Ruby: HEARTBEAT_DRIFT)
const HeartbeatDrift = 30 * time.Second

// DefaultHeartbeatTTL represents the default heartbeat TTL (Ruby: HEARTBEAT_TTL)
const DefaultHeartbeatTTL = 10 * time.Minute

// ===== MACHINE ERRORS =====
// Maps from Ruby Machine model custom errors with Go type safety

// MachineError represents machine-specific errors
type MachineError struct {
	Type    string
	Message string
}

func (e MachineError) Error() string {
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Machine error types (Ruby: custom error classes)
var (
	ErrResurrectionUnsupported = MachineError{
		Type:    "ResurrectionUnsupportedError",
		Message: "resurrection is not supported",
	}
	ErrResurrectionExpired = MachineError{
		Type:    "ResurrectionExpiredError",
		Message: "resurrection period has expired",
	}
	ErrMachineLimitExceeded = MachineError{
		Type:    "MachineLimitExceededError",
		Message: "machine count has exceeded maximum allowed",
	}
	ErrCoreLimitExceeded = MachineError{
		Type:    "CoreLimitExceededError",
		Message: "machine core count has exceeded maximum allowed",
	}
	ErrFingerprintConflict = MachineError{
		Type:    "FingerprintConflictError",
		Message: "fingerprint conflicts with another machine",
	}
	ErrInvalidOwner = MachineError{
		Type:    "InvalidOwnerError",
		Message: "owner must be a valid license user",
	}
)

// ===== MACHINE COMPONENT TYPES =====
// Maps from Ruby MachineComponent model with Go type safety

// MachineComponent represents a component of a machine (Ruby: MachineComponent model)
type MachineComponent struct {
	Name        string `json:"name"`
	Fingerprint string `json:"fingerprint"`
}

// IsValid validates the machine component
func (mc MachineComponent) IsValid() bool {
	return mc.Name != "" && mc.Fingerprint != ""
}

// ===== MACHINE UNIQUENESS VALIDATION =====
// Maps from Ruby Machine model uniqueness validation logic

// UniquenessScope represents the scope for machine uniqueness validation
type UniquenessScope string

const (
	UniquenessPerOrganization UniquenessScope = "organization" // Ruby: unique_per_account?
	UniquenessPerProduct      UniquenessScope = "product"      // Ruby: unique_per_product?
	UniquenessPerPolicy       UniquenessScope = "policy"       // Ruby: unique_per_policy?
	UniquenessPerLicense      UniquenessScope = "license"      // Ruby: unique_per_license?
)

// ===== MACHINE LEASING VALIDATION =====
// Maps from Ruby Machine model leasing validation logic
// Note: LeasingStrategy is defined in policy_strategies.go

// ===== MACHINE PROOF GENERATION =====
// Maps from Ruby Machine model proof generation logic

// ProofDataset represents the dataset used for machine proof generation (Ruby: default_proof_dataset)
type ProofDataset struct {
	Organization ProofOrganization `json:"account"` // Ruby: account (renamed to organization)
	Product      ProofProduct      `json:"product"`
	Policy       ProofPolicy       `json:"policy"`
	License      ProofLicense      `json:"license"`
	Machine      ProofMachine      `json:"machine"`
	Timestamp    time.Time         `json:"ts"`
}

type ProofOrganization struct {
	ID uuid.UUID `json:"id"`
}

type ProofProduct struct {
	ID uuid.UUID `json:"id"`
}

type ProofPolicy struct {
	ID       uuid.UUID `json:"id"`
	Duration *int64    `json:"duration,omitempty"`
}

type ProofLicense struct {
	ID     uuid.UUID  `json:"id"`
	Key    string     `json:"key"`
	Expiry *time.Time `json:"expiry,omitempty"`
}

type ProofMachine struct {
	ID          uuid.UUID `json:"id"`
	Fingerprint string    `json:"fingerprint"`
	Created     time.Time `json:"created"`
}

// ===== MACHINE VALIDATION HELPERS =====
// Maps from Ruby Machine model validation helper methods

// ValidateMaxProcessesOverride validates max processes override value
func ValidateMaxProcessesOverride(value *int) error {
	if value == nil {
		return nil
	}
	if *value < 0 {
		return fmt.Errorf("max_processes must be greater than or equal to 0")
	}
	if *value > ********** {
		return fmt.Errorf("max_processes must be less than or equal to **********")
	}
	return nil
}

// ValidateMaxMachinesOverride validates max machines override value
func ValidateMaxMachinesOverride(value *int) error {
	if value == nil {
		return nil
	}
	if *value < 0 || *value > ********** {
		return fmt.Errorf("max_machines_override must be between 0 and **********")
	}
	return nil
}

// ValidateMaxCoresOverride validates max cores override value
func ValidateMaxCoresOverride(value *int) error {
	if value == nil {
		return nil
	}
	if *value < 1 || *value > ********** {
		return fmt.Errorf("max_cores_override must be between 1 and **********")
	}
	return nil
}

// ValidateMaxUsesOverride validates max uses override value
func ValidateMaxUsesOverride(value *int) error {
	if value == nil {
		return nil
	}
	if *value < 0 || *value > ********** {
		return fmt.Errorf("max_uses_override must be between 0 and **********")
	}
	return nil
}

// ValidateMaxUsersOverride validates max users override value
func ValidateMaxUsersOverride(value *int) error {
	if value == nil {
		return nil
	}
	if *value < 0 || *value > ********** {
		return fmt.Errorf("max_users_override must be between 0 and **********")
	}
	return nil
}

// ValidateCores validates cores value
func ValidateCores(value *int) error {
	if value == nil {
		return nil
	}
	if *value < 1 {
		return fmt.Errorf("cores must be greater than or equal to 1")
	}
	if *value > ********** {
		return fmt.Errorf("cores must be less than or equal to **********")
	}
	return nil
}

// ValidateMetadata validates metadata size
func ValidateMetadata(metadata map[string]any) error {
	if len(metadata) > 64 {
		return fmt.Errorf("metadata has too many keys (exceeded limit of 64 keys)")
	}
	return nil
}

// ===== MACHINE SEARCH HELPERS =====
// Maps from Ruby Machine model search scope logic

// SearchScope represents different search scopes for machines
type SearchScope string

const (
	SearchScopeID          SearchScope = "id"
	SearchScopeFingerprint SearchScope = "fingerprint"
	SearchScopeName        SearchScope = "name"
	SearchScopeMetadata    SearchScope = "metadata"
	SearchScopeLicense     SearchScope = "license"
	SearchScopeOwner       SearchScope = "owner"
	SearchScopeUser        SearchScope = "user"
	SearchScopeProduct     SearchScope = "product"
	SearchScopePolicy      SearchScope = "policy"
)
