package entities

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMachine_TableName(t *testing.T) {
	machine := Machine{}
	assert.Equal(t, "machines", machine.TableName())
}

func TestMachine_IsActive(t *testing.T) {
	tests := []struct {
		name     string
		status   MachineStatus
		expected bool
	}{
		{"active machine", MachineStatusActive, true},
		{"inactive machine", MachineStatusInactive, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			machine := &Machine{Status: tt.status}
			assert.Equal(t, tt.expected, machine.IsActive())
		})
	}
}

func TestMachine_IsInactive(t *testing.T) {
	tests := []struct {
		name     string
		status   MachineStatus
		expected bool
	}{
		{"active machine", MachineStatusActive, false},
		{"inactive machine", MachineStatusInactive, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			machine := &Machine{Status: tt.status}
			assert.Equal(t, tt.expected, machine.IsInactive())
		})
	}
}

func TestMachine_HasOwner(t *testing.T) {
	ownerID := uuid.New()

	tests := []struct {
		name     string
		ownerID  *uuid.UUID
		expected bool
	}{
		{"with owner", &ownerID, true},
		{"without owner", nil, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			machine := &Machine{OwnerID: tt.ownerID}
			assert.Equal(t, tt.expected, machine.HasOwner())
		})
	}
}

func TestMachine_GetCores(t *testing.T) {
	cores := 8

	tests := []struct {
		name     string
		cores    *int
		expected int
	}{
		{"with cores", &cores, 8},
		{"without cores", nil, 0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			machine := &Machine{Cores: tt.cores}
			assert.Equal(t, tt.expected, machine.GetCores())
		})
	}
}

func TestMachine_HeartbeatDuration(t *testing.T) {
	duration := 300 // 5 minutes

	tests := []struct {
		name     string
		policy   Policy
		expected time.Duration
	}{
		{
			"with policy duration",
			Policy{HeartbeatDuration: &duration},
			5 * time.Minute,
		},
		{
			"without policy duration",
			Policy{},
			DefaultHeartbeatTTL,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			machine := &Machine{Policy: tt.policy}
			assert.Equal(t, tt.expected, machine.HeartbeatDuration())
		})
	}
}

func TestMachine_RequiresHeartbeat(t *testing.T) {
	now := time.Now()

	tests := []struct {
		name            string
		policy          Policy
		lastHeartbeatAt *time.Time
		expected        bool
	}{
		{
			"policy requires heartbeat",
			Policy{RequireHeartbeat: true},
			nil,
			true,
		},
		{
			"policy doesn't require but has heartbeat",
			Policy{RequireHeartbeat: false},
			&now,
			true,
		},
		{
			"policy doesn't require and no heartbeat",
			Policy{RequireHeartbeat: false},
			nil,
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			machine := &Machine{
				Policy:          tt.policy,
				LastHeartbeatAt: tt.lastHeartbeatAt,
			}
			assert.Equal(t, tt.expected, machine.RequiresHeartbeat())
		})
	}
}

func TestMachine_GetNextHeartbeatAt(t *testing.T) {
	now := time.Now()
	duration := 300 // 5 minutes

	tests := []struct {
		name            string
		lastHeartbeatAt *time.Time
		policy          Policy
		expectNil       bool
	}{
		{
			"with last heartbeat",
			&now,
			Policy{HeartbeatDuration: &duration},
			false,
		},
		{
			"without last heartbeat",
			nil,
			Policy{HeartbeatDuration: &duration},
			true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			machine := &Machine{
				LastHeartbeatAt: tt.lastHeartbeatAt,
				Policy:          tt.policy,
			}
			result := machine.GetNextHeartbeatAt()
			if tt.expectNil {
				assert.Nil(t, result)
			} else {
				require.NotNil(t, result)
				expected := now.Add(5 * time.Minute)
				assert.WithinDuration(t, expected, *result, time.Second)
			}
		})
	}
}

func TestMachine_GetHeartbeatStatus(t *testing.T) {
	now := time.Now()
	past := now.Add(-15 * time.Minute)
	recent := now.Add(-2 * time.Minute)
	duration := 600 // 10 minutes

	tests := []struct {
		name     string
		machine  Machine
		expected HeartbeatStatus
	}{
		{
			"not started - no heartbeat, doesn't require",
			Machine{
				Policy:          Policy{RequireHeartbeat: false},
				LastHeartbeatAt: nil,
				CreatedAt:       now,
			},
			HeartbeatStatusNotStarted,
		},
		{
			"alive - recent heartbeat",
			Machine{
				Policy:          Policy{RequireHeartbeat: true, HeartbeatDuration: &duration},
				LastHeartbeatAt: &recent,
				CreatedAt:       past,
			},
			HeartbeatStatusAlive,
		},
		{
			"dead - old heartbeat",
			Machine{
				Policy:          Policy{RequireHeartbeat: true, HeartbeatDuration: &duration},
				LastHeartbeatAt: &past,
				CreatedAt:       past,
			},
			HeartbeatStatusDead,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.machine.GetHeartbeatStatus())
		})
	}
}

func TestMachine_Ping(t *testing.T) {
	machine := &Machine{}

	err := machine.Ping()
	assert.NoError(t, err)
	assert.NotNil(t, machine.LastHeartbeatAt)
	assert.Nil(t, machine.HeartbeatJID)
	assert.WithinDuration(t, time.Now(), *machine.LastHeartbeatAt, time.Second)
}

func TestMachine_Reset(t *testing.T) {
	now := time.Now()
	jid := "job-123"

	machine := &Machine{
		LastHeartbeatAt: &now,
		HeartbeatJID:    &jid,
	}

	err := machine.Reset()
	assert.NoError(t, err)
	assert.Nil(t, machine.LastHeartbeatAt)
	assert.Nil(t, machine.HeartbeatJID)
}

func TestMachine_Components(t *testing.T) {
	machine := &Machine{
		Components: []MachineComponent{
			{Name: "cpu_id", Fingerprint: "Intel-i9"},
			{Name: "gpu_id", Fingerprint: "RTX-4090"},
		},
	}

	// Test HasComponents
	assert.True(t, machine.HasComponents())

	// Test GetComponent
	assert.Equal(t, "Intel-i9", machine.GetComponent("cpu_id"))
	assert.Equal(t, "RTX-4090", machine.GetComponent("gpu_id"))
	assert.Equal(t, "", machine.GetComponent("nonexistent"))

	// Test HasComponent
	assert.True(t, machine.HasComponent("cpu_id"))
	assert.True(t, machine.HasComponent("gpu_id"))
	assert.False(t, machine.HasComponent("nonexistent"))

	// Test AddComponent
	machine.AddComponent("ram_id", "32GB-DDR5")
	assert.True(t, machine.HasComponent("ram_id"))
	assert.Equal(t, "32GB-DDR5", machine.GetComponent("ram_id"))

	// Test RemoveComponent
	machine.RemoveComponent("gpu_id")
	assert.False(t, machine.HasComponent("gpu_id"))
	assert.Equal(t, "", machine.GetComponent("gpu_id"))

	// Test GetComponentsFingerprint
	fingerprint := machine.GetComponentsFingerprint()
	assert.NotEmpty(t, fingerprint)
	assert.Contains(t, fingerprint, "Intel-i9")
	assert.Contains(t, fingerprint, "32GB-DDR5")
}

func TestMachine_SetHeartbeatStatusOverride(t *testing.T) {
	machine := &Machine{}

	machine.SetHeartbeatStatusOverride(HeartbeatStatusResurrected)
	assert.Equal(t, HeartbeatStatusResurrected, machine.heartbeatStatusOverride)

	machine.ClearHeartbeatStatusOverride()
	assert.Equal(t, HeartbeatStatus(""), machine.heartbeatStatusOverride)
}

func TestMachine_Validation(t *testing.T) {
	validMachine := &Machine{
		Fingerprint: "valid-fingerprint",
		Status:      MachineStatusActive,
	}

	assert.NoError(t, validMachine.Validate())

	// Test invalid fingerprint
	invalidMachine := &Machine{
		Fingerprint: "", // Empty fingerprint
		Status:      MachineStatusActive,
	}

	assert.Error(t, invalidMachine.Validate())
}
