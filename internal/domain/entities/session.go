package entities

import (
	"time"

	"github.com/google/uuid"
)

// Session represents user authentication sessions
type Session struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID    uuid.UUID `json:"user_id" gorm:"column:user_id;type:uuid;not null"`
	TokenHash string    `json:"-" gorm:"column:token_hash;size:255;uniqueIndex;not null"`

	// Session details
	IP         *string    `json:"ip,omitempty" gorm:"size:45"`
	UserAgent  *string    `json:"user_agent,omitempty" gorm:"column:user_agent;type:text"`
	LastUsedAt *time.Time `json:"last_used_at,omitempty" gorm:"column:last_used_at"`
	ExpiresAt  time.Time  `json:"expires_at" gorm:"column:expires_at;not null"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"column:created_at;not null"`

	// Relations
	User User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName overrides the table name used by GORM
func (Session) TableName() string {
	return "sessions"
}

// IsExpired checks if the session has expired
func (s *Session) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// IsActive checks if the session is active (not expired)
func (s *Session) IsActive() bool {
	return !s.IsExpired()
}

// UpdateLastUsed updates the last used timestamp
func (s *Session) UpdateLastUsed() {
	now := time.Now()
	s.LastUsedAt = &now
}
