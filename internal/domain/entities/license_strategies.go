package entities

import (
	"fmt"
	"time"
)

// ===== LICENSE STATUS TYPES =====
// Maps from Ruby License model status logic with Go type safety

// LicenseStatus represents the status of a license (Ruby: status method)
type LicenseStatus string

const (
	LicenseStatusActive    LicenseStatus = "active"    // Ruby: :ACTIVE
	LicenseStatusExpired   LicenseStatus = "expired"   // Ruby: :EXPIRED
	LicenseStatusSuspended LicenseStatus = "suspended" // Ruby: :SUSPENDED
	LicenseStatusBanned    LicenseStatus = "banned"    // Ruby: :BANNED
	LicenseStatusExpiring  LicenseStatus = "expiring"  // Ruby: :EXPIRING
	LicenseStatusInactive  LicenseStatus = "inactive"  // Ruby: :INACTIVE
)

// IsValid validates the license status
func (ls LicenseStatus) IsValid() bool {
	switch ls {
	case LicenseStatusActive, LicenseStatusExpired, LicenseStatusSuspended,
		LicenseStatusBanned, LicenseStatusExpiring, LicenseStatusInactive:
		return true
	default:
		return false
	}
}

// String returns the string representation of the license status
func (ls LicenseStatus) String() string {
	return string(ls)
}

// ===== LICENSE OWNER TYPES =====
// Maps from Ruby License model owner polymorphic association

// LicenseOwnerType represents the type of license owner (Ruby: owner_type)
type LicenseOwnerType string

const (
	LicenseOwnerTypeUser         LicenseOwnerType = "user"         // Ruby: 'user'
	LicenseOwnerTypeOrganization LicenseOwnerType = "organization" // Ruby: 'organization'
)

// IsValid validates the license owner type
func (lot LicenseOwnerType) IsValid() bool {
	switch lot {
	case LicenseOwnerTypeUser, LicenseOwnerTypeOrganization:
		return true
	default:
		return false
	}
}

// String returns the string representation of the license owner type
func (lot LicenseOwnerType) String() string {
	return string(lot)
}

// ===== LICENSE ACTIVITY TRACKING =====
// Maps from Ruby License model activity tracking logic

// ActivityPeriod represents different activity tracking periods (Ruby: activity scope)
type ActivityPeriod time.Duration

const (
	DefaultActivityPeriod ActivityPeriod = ActivityPeriod(90 * 24 * time.Hour) // 90 days (Ruby: 90.days.ago)
)

// ===== LICENSE EXPIRY TRACKING =====
// Maps from Ruby License model expiry logic

// ExpiryWindow represents the expiry warning window (Ruby: expiring? method)
const ExpiryWarningWindow = 3 * 24 * time.Hour // 3 days (Ruby: 3.days.from_now)

// ===== LICENSE SEARCH SCOPES =====
// Maps from Ruby License model search scopes

// LicenseSearchScope represents different search scopes for licenses
type LicenseSearchScope string

const (
	LicenseSearchScopeID       LicenseSearchScope = "id"       // Ruby: search_id scope
	LicenseSearchScopeKey      LicenseSearchScope = "key"      // Ruby: search_key scope
	LicenseSearchScopeName     LicenseSearchScope = "name"     // Ruby: search_name scope
	LicenseSearchScopeMetadata LicenseSearchScope = "metadata" // Ruby: search_metadata scope
	LicenseSearchScopeOwner    LicenseSearchScope = "owner"    // Ruby: search_owner scope
	LicenseSearchScopeUser     LicenseSearchScope = "user"     // Ruby: search_user scope
	LicenseSearchScopeProduct  LicenseSearchScope = "product"  // Ruby: search_product scope
	LicenseSearchScopePolicy   LicenseSearchScope = "policy"   // Ruby: search_policy scope
)

// IsValid validates the license search scope
func (lss LicenseSearchScope) IsValid() bool {
	switch lss {
	case LicenseSearchScopeID, LicenseSearchScopeKey, LicenseSearchScopeName,
		LicenseSearchScopeMetadata, LicenseSearchScopeOwner, LicenseSearchScopeUser,
		LicenseSearchScopeProduct, LicenseSearchScopePolicy:
		return true
	default:
		return false
	}
}

// ===== LICENSE VALIDATION HELPERS =====
// Maps from Ruby License model validation helpers
// Note: Max*Override validation functions are defined in machine_strategies.go and shared

// ValidateUses validates license uses (Ruby: validates :uses)
func ValidateUses(uses int) error {
	if uses < 0 {
		return fmt.Errorf("uses must be greater than or equal to 0")
	}
	return nil
}

// ValidateLicenseKey validates license key (Ruby: validates :key)
func ValidateLicenseKey(key string) error {
	if key == "" {
		return fmt.Errorf("key can't be blank")
	}
	if len(key) < 1 {
		return fmt.Errorf("key is too short (minimum is 1 character)")
	}
	if len(key) > 100*1024 { // 100KB (Ruby: 100.kilobytes)
		return fmt.Errorf("key is too long (maximum is 100KB)")
	}
	return nil
}

// ValidateLicenseName validates license name (Ruby: name field validation)
func ValidateLicenseName(name *string) error {
	if name != nil && len(*name) > 255 {
		return fmt.Errorf("name is too long (maximum is 255 characters)")
	}
	return nil
}

// ===== LICENSE BUSINESS LOGIC HELPERS =====
// Maps from Ruby License model business logic helpers

// CalculateUsersCount calculates total users count (Ruby: users_count method)
func CalculateUsersCount(licenseUsersCount int, hasOwner bool) int {
	if hasOwner {
		return licenseUsersCount + 1
	}
	return licenseUsersCount
}

// IsExpired checks if license is expired (Ruby: expired? method)
func IsExpired(expiry *time.Time) bool {
	if expiry == nil {
		return false
	}
	return expiry.Before(time.Now())
}

// IsExpiring checks if license is expiring soon (Ruby: expiring? method)
func IsExpiring(expiry *time.Time) bool {
	if expiry == nil {
		return false
	}
	now := time.Now()
	return expiry.After(now) && expiry.Before(now.Add(ExpiryWarningWindow))
}

// IsActive checks if license is active (Ruby: active? method)
func IsActive(createdAt time.Time, lastValidatedAt, lastCheckOutAt, lastCheckInAt *time.Time, period time.Duration) bool {
	threshold := time.Now().Add(-period)

	if createdAt.After(threshold) {
		return true
	}

	if lastValidatedAt != nil && lastValidatedAt.After(threshold) {
		return true
	}

	if lastCheckOutAt != nil && lastCheckOutAt.After(threshold) {
		return true
	}

	if lastCheckInAt != nil && lastCheckInAt.After(threshold) {
		return true
	}

	return false
}

// ===== LICENSE PROOF GENERATION =====
// Maps from Ruby License model proof generation logic

// LicenseProofDataset represents the dataset for license proof generation
type LicenseProofDataset struct {
	Organization struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	} `json:"organization"`
	Product struct {
		ID   string `json:"id"`
		Name string `json:"name"`
		Code string `json:"code"`
	} `json:"product"`
	Policy struct {
		ID       string `json:"id"`
		Name     string `json:"name"`
		Duration *int64 `json:"duration,omitempty"`
	} `json:"policy"`
	License struct {
		ID      string     `json:"id"`
		Key     string     `json:"key"`
		Name    *string    `json:"name,omitempty"`
		Created time.Time  `json:"created"`
		Expiry  *time.Time `json:"expiry,omitempty"`
	} `json:"license"`
	Owner *struct {
		ID    string `json:"id"`
		Email string `json:"email"`
	} `json:"owner,omitempty"`
	Timestamp time.Time `json:"timestamp"`
}

// ===== LICENSE RENEWAL HELPERS =====
// Maps from Ruby License model renewal logic

// CalculateRenewalExpiry calculates new expiry date for renewal (Ruby: renew! method)
func CalculateRenewalExpiry(currentExpiry *time.Time, duration *int64, renewFromExpiry, renewFromNow, renewFromNowIfExpired bool) *time.Time {
	if duration == nil {
		return nil
	}

	durationTime := time.Duration(*duration) * time.Second
	now := time.Now()

	switch {
	case renewFromExpiry && currentExpiry != nil:
		newExpiry := currentExpiry.Add(durationTime)
		return &newExpiry
	case renewFromNow:
		newExpiry := now.Add(durationTime)
		return &newExpiry
	case renewFromNowIfExpired:
		var baseTime time.Time
		if currentExpiry != nil && currentExpiry.After(now) {
			baseTime = *currentExpiry
		} else {
			baseTime = now
		}
		newExpiry := baseTime.Add(durationTime)
		return &newExpiry
	default:
		return currentExpiry
	}
}

// ===== LICENSE CHECK-IN HELPERS =====
// Maps from Ruby License model check-in logic

// IsCheckInOverdue checks if license check-in is overdue (Ruby: check_in_overdue? method)
func IsCheckInOverdue(lastCheckInAt *time.Time, checkInInterval time.Duration, requiresCheckIn bool) bool {
	if !requiresCheckIn || lastCheckInAt == nil {
		return false
	}

	return lastCheckInAt.Add(checkInInterval).Before(time.Now())
}

// CalculateNextCheckInAt calculates next check-in time (Ruby: next_check_in_at method)
func CalculateNextCheckInAt(lastCheckInAt *time.Time, checkInInterval time.Duration, requiresCheckIn bool) *time.Time {
	if !requiresCheckIn || lastCheckInAt == nil {
		return nil
	}

	nextCheckIn := lastCheckInAt.Add(checkInInterval)
	return &nextCheckIn
}
