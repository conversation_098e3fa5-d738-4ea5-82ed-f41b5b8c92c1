package entities

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// JWTTokenType represents the type of JWT token
type JWTTokenType string

const (
	JWTTokenTypeAccess  JWTTokenType = "access"  // Short-lived access tokens
	JWTTokenTypeRefresh JWTTokenType = "refresh" // Long-lived refresh tokens
)

// JWTClaims represents the claims structure for JWT tokens
// Chỉ chứa thông tin tối thiểu, tất cả data khác sẽ được load từ DB
type JWTClaims struct {
	// Standard JWT claims
	Issuer    string    `json:"iss"`           // Issuer
	Subject   string    `json:"sub"`           // Subject (user ID)
	Audience  []string  `json:"aud,omitempty"` // Audience
	ExpiresAt time.Time `json:"exp"`           // Expiration time
	NotBefore time.Time `json:"nbf"`           // Not before
	IssuedAt  time.Time `json:"iat"`           // Issued at
	JWTID     string    `json:"jti"`           // JWT ID

	// Custom claims for our application - chỉ thông tin tối thiểu
	UserID         string       `json:"user_id"`
	SessionID      string       `json:"session_id,omitempty"`
	TokenType      JWTTokenType `json:"token_type"`
	OrganizationID *string      `json:"organization_id,omitempty"` // Optional, single org ID

	// Security metadata
	IPAddress string `json:"ip_address,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
}

// JWTTokenBuilder helps build JWT tokens with proper claims
type JWTTokenBuilder struct {
	user          *User
	session       *Session
	tokenType     JWTTokenType
	expiresIn     time.Duration
	organizations []*UsersOrganization
}

// NewJWTTokenBuilder creates a new JWT token builder
func NewJWTTokenBuilder(user *User) *JWTTokenBuilder {
	return &JWTTokenBuilder{
		user:      user,
		tokenType: JWTTokenTypeAccess,
		expiresIn: 15 * time.Minute, // Default 15 minutes for access tokens
	}
}

// WithSession adds session information to the token
func (b *JWTTokenBuilder) WithSession(session *Session) *JWTTokenBuilder {
	b.session = session
	return b
}

// WithTokenType sets the token type
func (b *JWTTokenBuilder) WithTokenType(tokenType JWTTokenType) *JWTTokenBuilder {
	b.tokenType = tokenType
	if tokenType == JWTTokenTypeRefresh {
		b.expiresIn = 7 * 24 * time.Hour // 7 days for refresh tokens
	}
	return b
}

// WithExpiresIn sets the expiration duration
func (b *JWTTokenBuilder) WithExpiresIn(duration time.Duration) *JWTTokenBuilder {
	b.expiresIn = duration
	return b
}

// WithOrganizations adds organization memberships to the token
func (b *JWTTokenBuilder) WithOrganizations(organizations []*UsersOrganization) *JWTTokenBuilder {
	b.organizations = organizations
	return b
}

// Build creates the JWT claims structure
func (b *JWTTokenBuilder) Build() *JWTClaims {
	now := time.Now()

	claims := &JWTClaims{
		Issuer:    "gokeys",
		Subject:   b.user.ID.String(),
		ExpiresAt: now.Add(b.expiresIn),
		NotBefore: now,
		IssuedAt:  now,
		JWTID:     uuid.New().String(),

		UserID:    b.user.ID.String(),
		TokenType: b.tokenType,
	}

	// Add session ID if available
	if b.session != nil {
		claims.SessionID = b.session.ID.String()
	}

	// Set organization ID (chỉ cho access token và chỉ nếu user thuộc 1 org)
	if b.tokenType == JWTTokenTypeAccess && len(b.organizations) > 0 {
		// Chỉ lưu organization ID đầu tiên (single org per token)
		orgID := b.organizations[0].OrganizationID.String()
		claims.OrganizationID = &orgID
	}

	return claims
}

// CanAccessOrganization checks if JWT can access a specific organization
// Chỉ check basic membership, permissions sẽ được check từ database
func (c *JWTClaims) CanAccessOrganization(orgID string) bool {
	// Check if user's organization matches the requested organization
	if c.OrganizationID != nil && *c.OrganizationID == orgID {
		return true
	}
	return false
}

// GetOrganizationID returns the organization ID if present
func (c *JWTClaims) GetOrganizationID() *string {
	return c.OrganizationID
}

// IsExpired checks if the JWT token has expired
func (c *JWTClaims) IsExpired() bool {
	return time.Now().After(c.ExpiresAt)
}

// IsValid checks if the JWT token is currently valid
func (c *JWTClaims) IsValid() bool {
	now := time.Now()
	return now.After(c.NotBefore) && now.Before(c.ExpiresAt)
}

// ToJSON converts claims to JSON string
func (c *JWTClaims) ToJSON() (string, error) {
	data, err := json.Marshal(c)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// FromJSON creates claims from JSON string
func FromJSON(jsonStr string) (*JWTClaims, error) {
	var claims JWTClaims
	err := json.Unmarshal([]byte(jsonStr), &claims)
	if err != nil {
		return nil, err
	}
	return &claims, nil
}
