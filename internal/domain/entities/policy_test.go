package entities

import (
	"testing"
	"time"

	"github.com/google/uuid"
)

// Helper function to create test UUIDs
func newTestUUID() uuid.UUID {
	return uuid.New()
}

func TestPolicy_SetDefaultStrategies(t *testing.T) {
	policy := &Policy{
		Name:           "Test Policy",
		OrganizationID: uuid.New(),
		ProductID:      uuid.New(),
	}

	// Set default strategies
	policy.SetDefaultStrategies()

	// Verify default strategies are set
	if policy.GetMachineUniquenessStrategy() != MachineUniquePerLicense {
		t.<PERSON><PERSON><PERSON>("Expected MachineUniquePerLicense, got %v", policy.GetMachineUniquenessStrategy())
	}

	if policy.GetMachineMatchingStrategy() != MatchAny {
		t.<PERSON>("Expected MatchAny, got %v", policy.GetMachineMatchingStrategy())
	}

	if policy.GetComponentUniquenessStrategy() != ComponentUniquePerMachine {
		t.<PERSON>("Expected ComponentUniquePerMachine, got %v", policy.GetComponentUniquenessStrategy())
	}

	if policy.GetExpirationStrategy() != ExpirationRestrictAccess {
		t.<PERSON>("Expected ExpirationRestrictAccess, got %v", policy.GetExpirationStrategy())
	}

	if policy.GetOverageStrategy() != OverageNoOverage {
		t.Errorf("Expected OverageNoOverage, got %v", policy.GetOverageStrategy())
	}
}

func TestPolicy_BasicProperties(t *testing.T) {
	policy := &Policy{
		Name:           "Test Policy",
		OrganizationID: uuid.New(),
		ProductID:      uuid.New(),
		Floating:       true,
		Strict:         false,
		Protected:      true,
		Encrypted:      true,
		UsePool:        false,
	}

	// Test basic property methods
	if !policy.IsFloating() {
		t.Error("Expected policy to be floating")
	}

	if policy.IsNodeLocked() {
		t.Error("Expected policy to not be node-locked")
	}

	if policy.IsStrict() {
		t.Error("Expected policy to not be strict")
	}

	if !policy.IsProtected() {
		t.Error("Expected policy to be protected")
	}

	if !policy.IsEncrypted() {
		t.Error("Expected policy to be encrypted")
	}

	if policy.IsPool() {
		t.Error("Expected policy to not use pool")
	}
}

func TestPolicy_HeartbeatMethods(t *testing.T) {
	policy := &Policy{
		RequireHeartbeat: true,
		HeartbeatDuration: func() *int {
			duration := 300 // 5 minutes
			return &duration
		}(),
	}

	policy.SetDefaultStrategies()

	if !policy.RequiresHeartbeat() {
		t.Error("Expected policy to require heartbeat")
	}

	if !policy.HasHeartbeatDuration() {
		t.Error("Expected policy to have heartbeat duration")
	}

	expectedDuration := 5 * time.Minute
	if policy.GetHeartbeatDuration() != expectedDuration {
		t.Errorf("Expected heartbeat duration %v, got %v", expectedDuration, policy.GetHeartbeatDuration())
	}

	// Test heartbeat strategy methods
	if !policy.DeactivatesDead() {
		t.Error("Expected policy to deactivate dead machines (default strategy)")
	}

	if policy.AlwaysResurrectsDead() {
		t.Error("Expected policy to not always resurrect dead machines (default strategy)")
	}
}

func TestPolicy_StrategyMethods(t *testing.T) {
	policy := &Policy{}
	policy.SetDefaultStrategies()

	// Test machine uniqueness methods
	if !policy.MachineUniquePerLicense() {
		t.Error("Expected machine unique per license (default)")
	}

	if policy.MachineUniquePerProduct() {
		t.Error("Expected machine not unique per product (default)")
	}

	// Test matching strategy methods
	if !policy.MachineMatchAny() {
		t.Error("Expected machine match any (default)")
	}

	if policy.MachineMatchAll() {
		t.Error("Expected machine not match all (default)")
	}

	// Test expiration strategy methods
	if !policy.RestrictsAccess() {
		t.Error("Expected policy to restrict access on expiry (default)")
	}

	if policy.RevokesAccess() {
		t.Error("Expected policy to not revoke access on expiry (default)")
	}

	// Test authentication strategy methods
	if !policy.SupportsTokenAuth() {
		t.Error("Expected policy to support token auth (default)")
	}

	// Test overage strategy methods
	if !policy.NoOverage() {
		t.Error("Expected policy to have no overage (modern default)")
	}

	if policy.AllowsOverage() {
		t.Error("Expected policy to not allow overage (modern default)")
	}
}

func TestPolicy_ValidationBasicFields(t *testing.T) {
	tests := []struct {
		name    string
		policy  *Policy
		wantErr bool
	}{
		{
			name: "valid policy",
			policy: &Policy{
				Name:           "Valid Policy",
				OrganizationID: newTestUUID(),
				ProductID:      newTestUUID(),
			},
			wantErr: false,
		},
		{
			name: "empty name",
			policy: &Policy{
				Name:           "",
				OrganizationID: newTestUUID(),
				ProductID:      newTestUUID(),
			},
			wantErr: true,
		},
		{
			name: "invalid duration",
			policy: &Policy{
				Name:           "Test Policy",
				OrganizationID: newTestUUID(),
				ProductID:      newTestUUID(),
				Duration: func() *int64 {
					duration := int64(-1)
					return &duration
				}(),
			},
			wantErr: true,
		},
		{
			name: "valid duration",
			policy: &Policy{
				Name:           "Test Policy",
				OrganizationID: newTestUUID(),
				ProductID:      newTestUUID(),
				Duration: func() *int64 {
					duration := int64(86400) // 1 day
					return &duration
				}(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.policy.ValidateBasicFields()
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateBasicFields() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPolicy_OverageCompatibility(t *testing.T) {
	// Test node-locked policy with incompatible overage strategy
	policy := &Policy{
		Name:           "Node-locked Policy",
		OrganizationID: newTestUUID(),
		ProductID:      newTestUUID(),
		Floating:       false, // Node-locked
		MaxMachines: func() *int {
			max := 1
			return &max
		}(),
		OverageStrategy: func() *OverageStrategy {
			strategy := OverageAllow125x
			return &strategy
		}(),
	}

	err := policy.ValidateOverageCompatibility()
	if err == nil {
		t.Error("Expected validation error for node-locked policy with 1.25x overage strategy")
	}

	// Test valid overage strategy for node-locked policy
	policy.OverageStrategy = func() *OverageStrategy {
		strategy := OverageNoOverage
		return &strategy
	}()

	err = policy.ValidateOverageCompatibility()
	if err != nil {
		t.Errorf("Expected no validation error, got: %v", err)
	}
}

func TestPolicy_CryptoSchemeDefaults(t *testing.T) {
	policy := &Policy{
		Name:           "Encrypted Policy",
		OrganizationID: newTestUUID(),
		ProductID:      newTestUUID(),
		Encrypted:      true,
	}

	policy.SetDefaultStrategies()

	// Verify modern crypto scheme is set as default
	if policy.Scheme == nil {
		t.Error("Expected crypto scheme to be set for encrypted policy")
	} else if *policy.Scheme != CryptoSchemeED25519 {
		t.Errorf("Expected ED25519 scheme (modern default), got %v", *policy.Scheme)
	}
}

func TestPolicy_NodeLockedDefaults(t *testing.T) {
	policy := &Policy{
		Name:           "Node-locked Policy",
		OrganizationID: newTestUUID(),
		ProductID:      newTestUUID(),
		Floating:       false, // Node-locked
	}

	policy.SetDefaultStrategies()

	// Verify max_machines is set to 1 for node-locked policies
	if policy.MaxMachines == nil {
		t.Error("Expected max_machines to be set for node-locked policy")
	} else if *policy.MaxMachines != 1 {
		t.Errorf("Expected max_machines = 1 for node-locked policy, got %d", *policy.MaxMachines)
	}
}
