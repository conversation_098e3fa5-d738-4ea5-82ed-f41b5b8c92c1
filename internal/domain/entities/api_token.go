package entities

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// APIToken represents API access tokens with permission-based authorization
type APIToken struct {
	ID             uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID         *uuid.UUID `json:"user_id,omitempty" gorm:"column:user_id;type:uuid"`
	OrganizationID *uuid.UUID `json:"organization_id,omitempty" gorm:"column:organization_id;type:uuid"`
	Name           string     `json:"name" gorm:"size:255;not null"`
	Description    string     `json:"description,omitempty" gorm:"size:500"`

	// Token authentication
	TokenHash   string `json:"-" gorm:"column:token_hash;size:255;uniqueIndex;not null"`
	TokenPrefix string `json:"token_prefix" gorm:"size:8;not null"` // Prefix for identification

	// Permission names that this token has
	Permissions []string `json:"permissions" gorm:"type:text[]"`

	// Token lifecycle
	Active     bool       `json:"active" gorm:"default:true"`
	ExpiresAt  *time.Time `json:"expires_at,omitempty" gorm:"column:expires_at"`
	LastUsedAt *time.Time `json:"last_used_at,omitempty" gorm:"column:last_used_at"`
	LastUsedIP string     `json:"last_used_ip,omitempty" gorm:"size:45"` // IPv4/IPv6

	// Security metadata
	CreatedByIP string `json:"created_by_ip,omitempty" gorm:"size:45"`
	UserAgent   string `json:"user_agent,omitempty" gorm:"size:500"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"column:updated_at;not null"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deleted_at;index"`

	// Relations
	User         *User         `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Organization *Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"`
}

// TableName overrides the table name used by GORM
func (APIToken) TableName() string {
	return "api_tokens"
}

// IsExpired checks if the token has expired
func (t *APIToken) IsExpired() bool {
	if t.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*t.ExpiresAt)
}

// IsActive checks if the token is currently active and not expired
func (t *APIToken) IsActive() bool {
	return t.Active && !t.IsExpired()
}

// HasPermission checks if the token has a specific permission
func (t *APIToken) HasPermission(permissionName string) bool {
	for _, p := range t.Permissions {
		if p == permissionName {
			return true
		}
	}
	return false
}

// UpdateLastUsed updates the last used timestamp and IP
func (t *APIToken) UpdateLastUsed(ip string) {
	now := time.Now()
	t.LastUsedAt = &now
	t.LastUsedIP = ip
}

// GenerateToken generates a new API token string
func (t *APIToken) GenerateToken() (string, error) {
	// Generate random bytes for token
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", fmt.Errorf("failed to generate random token: %w", err)
	}

	// Create token string with simplified prefix
	// Permissions trong scopes đã chứa toàn bộ thông tin cần thiết
	prefix := "ldr_"

	tokenString := prefix + hex.EncodeToString(tokenBytes)

	// Store hash and prefix for identification
	hash := sha256.Sum256([]byte(tokenString))
	t.TokenHash = hex.EncodeToString(hash[:])
	t.TokenPrefix = tokenString[:8] // Store first 8 chars for identification

	return tokenString, nil
}
