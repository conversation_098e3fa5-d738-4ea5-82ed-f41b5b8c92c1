package cache

import "time"

// <PERSON>ache defines the interface for caching operations
type Cache interface {
	// Get retrieves a value from the cache
	Get(key string) (interface{}, bool)
	
	// Set stores a value in the cache with TTL
	Set(key string, value interface{}, ttl time.Duration) error
	
	// Delete removes a key from the cache
	Delete(key string) error
	
	// Exists checks if a key exists in the cache
	Exists(key string) bool
	
	// Clear removes all items from the cache
	Clear() error
	
	// Keys returns all keys matching a pattern
	Keys(pattern string) ([]string, error)
	
	// TTL returns the time-to-live for a key
	TTL(key string) (time.Duration, error)
	
	// Expire sets the TTL for an existing key
	Expire(key string, ttl time.Duration) error
}

// Stats represents cache statistics
type Stats struct {
	Hits        int64 `json:"hits"`
	Misses      int64 `json:"misses"`
	Keys        int64 `json:"keys"`
	Memory      int64 `json:"memory_bytes"`
	Connections int64 `json:"connections"`
}

// CacheWithStats extends Cache with statistics
type CacheWithStats interface {
	Cache
	Stats() (*Stats, error)
}