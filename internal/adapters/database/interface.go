package database

import "gorm.io/gorm"

// Database interface defines database operations
type Database interface {
	// Connection management
	GetDB() *gorm.DB
	Close() error
	Health() error

	// Migration management
	Initialize() error
	GetMigrationStatus() (uint, bool, error)
	RollbackMigrations(steps int) error
}

// Config holds database configuration
type Config struct {
	Host            string
	Port            int
	User            string
	Password        string
	DBName          string
	SSLMode         string
	MaxIdleConns    int
	MaxOpenConns    int
	ConnMaxLifetime int // in seconds
	ConnMaxIdleTime int // in seconds
}