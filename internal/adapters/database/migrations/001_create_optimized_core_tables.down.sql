-- Rollback optimized core database tables for gokeys license management system
-- Created: 2025-07-23

-- Drop tables in reverse order to respect foreign key constraints
DROP TABLE IF EXISTS api_tokens;
DROP TABLE IF EXISTS sessions;
DROP TABLE IF EXISTS machines;
DROP TABLE IF EXISTS licenses;
DROP TABLE IF EXISTS policies;
DROP TABLE IF EXISTS products;
DROP TABLE IF EXISTS user_organizations;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS organizations;

-- Drop extensions (only if no other database objects depend on them)
-- DROP EXTENSION IF EXISTS "uuid-ossp";
