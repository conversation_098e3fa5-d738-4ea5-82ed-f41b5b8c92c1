package repositories

import (
	"context"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"gorm.io/gorm"
)

// OrganizationRepositoryImpl implements OrganizationRepository interface
type OrganizationRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Organization]
}

// NewOrganizationRepository creates a new organization repository
func NewOrganizationRepository(db *gorm.DB) repositories.OrganizationRepository {
	return &OrganizationRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Organization](db),
	}
}

// GetBySlug retrieves an organization by its slug
func (r *OrganizationRepositoryImpl) GetBySlug(ctx context.Context, slug string) (*entities.Organization, error) {
	var organization entities.Organization
	err := r.GetDB().WithContext(ctx).Where("slug = ?", slug).First(&organization).Error
	if err != nil {
		return nil, err
	}
	return &organization, nil
}



