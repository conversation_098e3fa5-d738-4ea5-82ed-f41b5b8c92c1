package repositories

import (
	"context"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
)

// APITokenRepositoryImpl implements APITokenRepository interface
type APITokenRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.APIToken]
}

// NewAPITokenRepository creates a new API token repository
func NewAPITokenRepository(db *gorm.DB) repositories.APITokenRepository {
	return &APITokenRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.APIToken](db),
	}
}

// GetByTokenHash retrieves an API token by token hash
func (r *APITokenRepositoryImpl) GetByTokenHash(ctx context.Context, tokenHash string) (*entities.APIToken, error) {
	var token entities.APIToken
	err := r.GetDB().WithContext(ctx).
		Where("token_hash = ? AND active = ?", tokenHash, true).
		First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetByUserID retrieves API tokens by user ID
func (r *APITokenRepositoryImpl) GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.APIToken, error) {
	var tokens []*entities.APIToken
	err := r.GetDB().WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&tokens).Error
	return tokens, err
}

// GetByOrganizationID retrieves API tokens by organization ID
func (r *APITokenRepositoryImpl) GetByOrganizationID(ctx context.Context, organizationID uuid.UUID) ([]*entities.APIToken, error) {
	var tokens []*entities.APIToken
	err := r.GetDB().WithContext(ctx).
		Where("organization_id = ?", organizationID).
		Order("created_at DESC").
		Find(&tokens).Error
	return tokens, err
}

// UpdateLastUsed updates the token's last used timestamp
func (r *APITokenRepositoryImpl) UpdateLastUsed(ctx context.Context, tokenID uuid.UUID) error {
	now := time.Now()
	return r.GetDB().WithContext(ctx).Model(&entities.APIToken{}).
		Where("id = ?", tokenID).
		Updates(map[string]interface{}{
			"last_used_at": &now,
			"updated_at":   now,
		}).Error
}