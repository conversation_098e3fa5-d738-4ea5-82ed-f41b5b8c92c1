package repositories

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// LicenseRepositoryImpl implements LicenseRepository interface
type LicenseRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.License]
}

// NewLicenseRepository creates a new license repository
func NewLicenseRepository(db *gorm.DB) repositories.LicenseRepository {
	return &LicenseRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.License](db),
	}
}

// GetByKey retrieves a license by its key
func (r *LicenseRepositoryImpl) GetByKey(ctx context.Context, key string) (*entities.License, error) {
	var license entities.License
	err := r.GetDB().WithContext(ctx).
		Preload("Policy").
		Preload("Product").
		Preload("User").
		Preload("Group").
		Where("key = ?", key).
		First(&license).Error
	if err != nil {
		return nil, err
	}
	return &license, nil
}

// GetByPolicy retrieves licenses by policy ID
func (r *LicenseRepositoryImpl) GetByPolicy(ctx context.Context, policyID uuid.UUID) ([]*entities.License, error) {
	var licenses []*entities.License
	err := r.GetDB().WithContext(ctx).
		Preload("Policy").
		Preload("Product").
		Where("policy_id = ?", policyID).
		Find(&licenses).Error
	return licenses, err
}

// GetExpiring retrieves licenses expiring before a specific date
func (r *LicenseRepositoryImpl) GetExpiring(ctx context.Context, organizationID uuid.UUID, beforeDate time.Time) ([]*entities.License, error) {
	var licenses []*entities.License
	err := r.GetDB().WithContext(ctx).
		Preload("Policy").
		Preload("Product").
		Where("organization_id = ? AND expires_at IS NOT NULL AND expires_at <= ?", organizationID, beforeDate).
		Find(&licenses).Error
	return licenses, err
}

// UpdateLastValidated updates the last validated timestamp
func (r *LicenseRepositoryImpl) UpdateLastValidated(ctx context.Context, licenseID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.License{}).
		Where("id = ?", licenseID).
		Updates(map[string]interface{}{
			"last_validated_at": time.Now(),
			"updated_at":        time.Now(),
		}).Error
}

// IncrementValidationCount increments the validation count
func (r *LicenseRepositoryImpl) IncrementValidationCount(ctx context.Context, licenseID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.License{}).
		Where("id = ?", licenseID).
		Updates(map[string]interface{}{
			"uses":              gorm.Expr("uses + 1"),
			"last_validated_at": time.Now(),
			"updated_at":        time.Now(),
		}).Error
}
