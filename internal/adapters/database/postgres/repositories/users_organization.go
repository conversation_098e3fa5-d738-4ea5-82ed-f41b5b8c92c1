package repositories

import (
	"context"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"gorm.io/gorm"
)

// PostgresUsersOrganizationRepository implements the users-organizations repository interface
type PostgresUsersOrganizationRepository struct {
	db *gorm.DB
}

// NewPostgresUsersOrganizationRepository creates a new users-organizations repository
func NewPostgresUsersOrganizationRepository(db *gorm.DB) repositories.UsersOrganizationRepository {
	return &PostgresUsersOrganizationRepository{db: db}
}

// Create adds a user to an organization
func (r *PostgresUsersOrganizationRepository) Create(ctx context.Context, userOrg *entities.UsersOrganization) error {
	return r.db.WithContext(ctx).Create(userOrg).Error
}

// Delete removes a user from an organization
func (r *PostgresUsersOrganizationRepository) Delete(ctx context.Context, userID, orgID string) error {
	return r.db.WithContext(ctx).
		Where("user_id = ? AND organization_id = ?", userID, orgID).
		Delete(&entities.UsersOrganization{}).Error
}

// GetUserOrganizations gets all organizations for a user
func (r *PostgresUsersOrganizationRepository) GetUserOrganizations(ctx context.Context, userID string) ([]*entities.UsersOrganization, error) {
	var userOrgs []*entities.UsersOrganization
	
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Preload("Organization").
		Preload("Inviter").
		Find(&userOrgs).Error
	
	return userOrgs, err
}

// GetOrganizationUsers gets all users in an organization
func (r *PostgresUsersOrganizationRepository) GetOrganizationUsers(ctx context.Context, orgID string) ([]*entities.UsersOrganization, error) {
	var userOrgs []*entities.UsersOrganization
	
	err := r.db.WithContext(ctx).
		Where("organization_id = ?", orgID).
		Preload("User").
		Preload("Inviter").
		Find(&userOrgs).Error
	
	return userOrgs, err
}

// IsUserInOrganization checks if a user belongs to an organization
func (r *PostgresUsersOrganizationRepository) IsUserInOrganization(ctx context.Context, userID, orgID string) (bool, error) {
	var count int64
	
	err := r.db.WithContext(ctx).
		Model(&entities.UsersOrganization{}).
		Where("user_id = ? AND organization_id = ?", userID, orgID).
		Count(&count).Error
	
	return count > 0, err
}

// GetByUserAndOrganization gets a specific user-organization relationship
func (r *PostgresUsersOrganizationRepository) GetByUserAndOrganization(ctx context.Context, userID, orgID string) (*entities.UsersOrganization, error) {
	var userOrg entities.UsersOrganization
	
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND organization_id = ?", userID, orgID).
		Preload("User").
		Preload("Organization").
		Preload("Inviter").
		First(&userOrg).Error
	
	if err != nil {
		return nil, err
	}
	
	return &userOrg, nil
}