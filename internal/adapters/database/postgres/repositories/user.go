package repositories

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// UserRepositoryImpl implements UserRepository interface
type UserRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.User]
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) repositories.UserRepository {
	return &UserRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.User](db),
	}
}

// GetByEmail retrieves a user by email
func (r *UserRepositoryImpl) GetByEmail(ctx context.Context, email string) (*entities.User, error) {
	var user entities.User
	err := r.GetDB().WithContext(ctx).
		Where("email = ?", email).
		First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}


// UpdateLastLogin updates the user's last login timestamp
func (r *UserRepositoryImpl) UpdateLastLogin(ctx context.Context, userID uuid.UUID) error {
	return r.GetDB().WithContext(ctx).Model(&entities.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"last_login": time.Now(),
			"updated_at": time.Now(),
		}).Error
}
