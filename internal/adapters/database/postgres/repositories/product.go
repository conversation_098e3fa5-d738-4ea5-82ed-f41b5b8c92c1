package repositories

import (
	"context"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ProductRepositoryImpl implements ProductRepository interface
type ProductRepositoryImpl struct {
	*repositories.BaseRepositoryImpl[entities.Product]
}

// NewProductRepository creates a new product repository
func NewProductRepository(db *gorm.DB) repositories.ProductRepository {
	return &ProductRepositoryImpl{
		BaseRepositoryImpl: repositories.NewBaseRepository[entities.Product](db),
	}
}

// GetByCode retrieves a product by its code and organization ID
func (r *ProductRepositoryImpl) GetByCode(ctx context.Context, code string, organizationID uuid.UUID) (*entities.Product, error) {
	var product entities.Product
	err := r.GetDB().WithContext(ctx).
		Where("code = ? AND organization_id = ?", code, organizationID).
		First(&product).Error
	if err != nil {
		return nil, err
	}
	return &product, nil
}

// GetByOrganizationID retrieves all products for an organization
func (r *ProductRepositoryImpl) GetByOrganizationID(ctx context.Context, organizationID uuid.UUID) ([]*entities.Product, error) {
	var products []*entities.Product
	err := r.GetDB().WithContext(ctx).
		Where("organization_id = ?", organizationID).
		Find(&products).Error
	return products, err
}
