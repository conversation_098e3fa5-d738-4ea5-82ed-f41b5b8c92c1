package postgres

import (
	"fmt"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"gorm.io/gorm"
)

type Migrator struct {
	db           *gorm.DB
	migrationsDir string
}

// NewMigrator creates a new database migrator
func NewMigrator(db *gorm.DB, migrationsDir string) *Migrator {
	return &Migrator{
		db:           db,
		migrationsDir: migrationsDir,
	}
}

// RunMigrations executes all pending migrations
func (m *Migrator) RunMigrations() error {
	sqlDB, err := m.db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	driver, err := postgres.WithInstance(sqlDB, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create postgres driver: %w", err)
	}

	migrator, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", m.migrationsDir),
		"postgres",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}
	// DON'T close migrator here as it would close the shared database connection
	// The database connection will be managed by the main application
	// defer migrator.Close()

	// Run migrations
	if err := migrator.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	fmt.Println("Migrations completed successfully")
	return nil
}

// RollbackMigrations rolls back the last migration
func (m *Migrator) RollbackMigrations(steps int) error {
	sqlDB, err := m.db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	driver, err := postgres.WithInstance(sqlDB, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create postgres driver: %w", err)
	}

	migrator, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", m.migrationsDir),
		"postgres",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}
	// DON'T close migrator here as it would close the shared database connection
	// The database connection will be managed by the main application
	// defer migrator.Close()

	// Get current version
	version, dirty, err := migrator.Version()
	if err != nil && err != migrate.ErrNilVersion {
		return fmt.Errorf("failed to get current version: %w", err)
	}

	if dirty {
		return fmt.Errorf("database is in dirty state")
	}

	// Calculate target version
	targetVersion := int(version) - steps
	if targetVersion < 0 {
		targetVersion = 0
	}

	// Migrate to target version
	if err := migrator.Migrate(uint(targetVersion)); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("failed to rollback migrations: %w", err)
	}

	fmt.Printf("Rolled back %d migration(s) successfully\n", steps)
	return nil
}

// GetMigrationStatus returns the current migration status
func (m *Migrator) GetMigrationStatus() (uint, bool, error) {
	sqlDB, err := m.db.DB()
	if err != nil {
		return 0, false, fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	driver, err := postgres.WithInstance(sqlDB, &postgres.Config{})
	if err != nil {
		return 0, false, fmt.Errorf("failed to create postgres driver: %w", err)
	}

	migrator, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", m.migrationsDir),
		"postgres",
		driver,
	)
	if err != nil {
		return 0, false, fmt.Errorf("failed to create migrator: %w", err)
	}
	// DON'T close migrator here as it would close the shared database connection
	// The database connection will be managed by the main application
	// defer migrator.Close()

	return migrator.Version()
}

// CreateExtensions ensures required PostgreSQL extensions are enabled
func (m *Migrator) CreateExtensions() error {
	extensions := []string{
		"CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",
		"CREATE EXTENSION IF NOT EXISTS \"pgcrypto\";",
		"CREATE EXTENSION IF NOT EXISTS \"btree_gin\";",
		"CREATE EXTENSION IF NOT EXISTS \"pg_stat_statements\";",
	}

	for _, ext := range extensions {
		if err := m.db.Exec(ext).Error; err != nil {
			return fmt.Errorf("failed to create extension: %w", err)
		}
	}

	fmt.Println("Required PostgreSQL extensions created successfully")
	return nil
}

// ForceVersion forces the migration version (useful for fixing dirty state)
func (m *Migrator) ForceVersion(version int) error {
	sqlDB, err := m.db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	driver, err := postgres.WithInstance(sqlDB, &postgres.Config{})
	if err != nil {
		return fmt.Errorf("failed to create postgres driver: %w", err)
	}

	migrator, err := migrate.NewWithDatabaseInstance(
		fmt.Sprintf("file://%s", m.migrationsDir),
		"postgres",
		driver,
	)
	if err != nil {
		return fmt.Errorf("failed to create migrator: %w", err)
	}

	if err := migrator.Force(version); err != nil {
		return fmt.Errorf("failed to force version: %w", err)
	}

	fmt.Printf("Forced migration version to %d\n", version)
	return nil
}