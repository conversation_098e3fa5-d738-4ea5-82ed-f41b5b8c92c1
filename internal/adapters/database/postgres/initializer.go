package postgres

import (
	"fmt"
	"path/filepath"

	"gorm.io/gorm"
)

// PostgreSQLAdapter handles PostgreSQL database operations
type PostgreSQLAdapter struct {
	DB       *gorm.DB
	migrator *Migrator
}

// NewPostgreSQLAdapter creates a new PostgreSQL database adapter
func NewPostgreSQLAdapter(config Config) (*PostgreSQLAdapter, error) {
	// Create database connection
	db, err := NewConnection(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create database connection: %w", err)
	}

	// Get migrations directory
	migrationsDir := filepath.Join("internal", "adapters", "database", "migrations")

	// Create migrator
	migrator := NewMigrator(db, migrationsDir)

	return &PostgreSQLAdapter{
		DB:       db,
		migrator: migrator,
	}, nil
}

// GetDB returns the GORM database instance
func (p *PostgreSQLAdapter) GetDB() *gorm.DB {
	return p.DB
}

// Initialize sets up the database with extensions and runs migrations
func (p *PostgreSQLAdapter) Initialize() error {
	// Create required PostgreSQL extensions
	if err := p.migrator.CreateExtensions(); err != nil {
		return fmt.Errorf("failed to create extensions: %w", err)
	}

	// Run migrations
	if err := p.migrator.RunMigrations(); err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	return nil
}

// GetMigrationStatus returns current migration status
func (p *PostgreSQLAdapter) GetMigrationStatus() (uint, bool, error) {
	return p.migrator.GetMigrationStatus()
}

// RollbackMigrations rolls back specified number of migrations
func (p *PostgreSQLAdapter) RollbackMigrations(steps int) error {
	return p.migrator.RollbackMigrations(steps)
}

// Close closes the database connection
func (p *PostgreSQLAdapter) Close() error {
	return Close(p.DB)
}

// ForceVersion forces the migration version (useful for fixing dirty state)
func (p *PostgreSQLAdapter) ForceVersion(version int) error {
	return p.migrator.ForceVersion(version)
}

// Health checks database connectivity
func (p *PostgreSQLAdapter) Health() error {
	sqlDB, err := p.DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("database ping failed: %w", err)
	}

	return nil
}