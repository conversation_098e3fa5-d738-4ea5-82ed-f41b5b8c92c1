package database

import (
	"fmt"

	"github.com/gokeys/gokeys/internal/adapters/database/postgres"
)

// DatabaseType represents the type of database
type DatabaseType string

const (
	PostgreSQL DatabaseType = "postgresql"
	// MySQL     DatabaseType = "mysql"     // Future support
	// SQLite    DatabaseType = "sqlite"    // Future support
)

// NewDatabase creates a new database adapter based on the specified type
func NewDatabase(dbType DatabaseType, config Config) (Database, error) {
	switch dbType {
	case PostgreSQL:
		// Convert to postgres-specific config
		pgConfig := postgres.Config{
			Host:            config.Host,
			Port:            config.Port,
			User:            config.User,
			Password:        config.Password,
			DBName:          config.DBName,
			SSLMode:         config.SSLMode,
			MaxIdleConns:    config.MaxIdleConns,
			MaxOpenConns:    config.MaxOpenConns,
			ConnMaxLifetime: config.ConnMaxLifetime,
			ConnMaxIdleTime: config.ConnMaxIdleTime,
		}
		return postgres.NewPostgreSQLAdapter(pgConfig)
	default:
		return nil, fmt.Errorf("unsupported database type: %s", dbType)
	}
}

// NewPostgreSQL creates a new PostgreSQL database adapter (convenience function)
func NewPostgreSQL(config Config) (Database, error) {
	// Convert to postgres-specific config
	pgConfig := postgres.Config{
		Host:            config.Host,
		Port:            config.Port,
		User:            config.User,
		Password:        config.Password,
		DBName:          config.DBName,
		SSLMode:         config.SSLMode,
		MaxIdleConns:    config.MaxIdleConns,
		MaxOpenConns:    config.MaxOpenConns,
		ConnMaxLifetime: config.ConnMaxLifetime,
		ConnMaxIdleTime: config.ConnMaxIdleTime,
	}
	return postgres.NewPostgreSQLAdapter(pgConfig)
}