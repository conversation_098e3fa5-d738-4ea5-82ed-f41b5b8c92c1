package cache

import (
	"context"
	"fmt"
	"sync"
	"time"
)

// CacheItem represents a cached item with expiration
type CacheItem struct {
	Value      interface{}
	Expiration time.Time
}

// IsExpired checks if the cache item has expired
func (item *CacheItem) IsExpired() bool {
	return time.Now().After(item.Expiration)
}

// MemoryCache implements an in-memory cache with TTL support
type MemoryCache struct {
	items map[string]*CacheItem
	mutex sync.RWMutex
}

// NewMemoryCache creates a new in-memory cache
func NewMemoryCache() *MemoryCache {
	cache := &MemoryCache{
		items: make(map[string]*CacheItem),
	}
	
	// Start cleanup goroutine
	go cache.cleanup()
	
	return cache
}

// Set stores a value in the cache with expiration
func (mc *MemoryCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	exp := time.Now().Add(expiration)
	if expiration <= 0 {
		// No expiration
		exp = time.Now().Add(24 * time.Hour * 365) // 1 year from now
	}
	
	mc.items[key] = &CacheItem{
		Value:      value,
		Expiration: exp,
	}
	
	return nil
}

// Get retrieves a value from the cache
func (mc *MemoryCache) Get(ctx context.Context, key string) (string, error) {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	
	item, exists := mc.items[key]
	if !exists {
		return "", fmt.Errorf("key not found")
	}
	
	if item.IsExpired() {
		// Item expired, remove it
		delete(mc.items, key)
		return "", fmt.Errorf("key expired")
	}
	
	// Convert value to string
	if str, ok := item.Value.(string); ok {
		return str, nil
	}
	
	return fmt.Sprintf("%v", item.Value), nil
}

// Delete removes a key from the cache
func (mc *MemoryCache) Delete(ctx context.Context, key string) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	delete(mc.items, key)
	return nil
}

// Exists checks if a key exists in the cache
func (mc *MemoryCache) Exists(ctx context.Context, key string) (bool, error) {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	
	item, exists := mc.items[key]
	if !exists {
		return false, nil
	}
	
	if item.IsExpired() {
		delete(mc.items, key)
		return false, nil
	}
	
	return true, nil
}

// Clear removes all items from the cache
func (mc *MemoryCache) Clear() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	mc.items = make(map[string]*CacheItem)
}

// Size returns the number of items in the cache
func (mc *MemoryCache) Size() int {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	
	return len(mc.items)
}

// Keys returns all keys in the cache
func (mc *MemoryCache) Keys() []string {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	
	keys := make([]string, 0, len(mc.items))
	for key := range mc.items {
		keys = append(keys, key)
	}
	
	return keys
}

// cleanup removes expired items from the cache periodically
func (mc *MemoryCache) cleanup() {
	ticker := time.NewTicker(5 * time.Minute) // Cleanup every 5 minutes
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			mc.removeExpired()
		}
	}
}

// removeExpired removes all expired items from the cache
func (mc *MemoryCache) removeExpired() {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()
	
	now := time.Now()
	for key, item := range mc.items {
		if now.After(item.Expiration) {
			delete(mc.items, key)
		}
	}
}

// GetStats returns cache statistics
func (mc *MemoryCache) GetStats() map[string]interface{} {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()
	
	totalItems := len(mc.items)
	expiredItems := 0
	
	now := time.Now()
	for _, item := range mc.items {
		if now.After(item.Expiration) {
			expiredItems++
		}
	}
	
	return map[string]interface{}{
		"total_items":   totalItems,
		"active_items":  totalItems - expiredItems,
		"expired_items": expiredItems,
	}
}

// TTLCache provides additional TTL-specific operations
type TTLCache struct {
	*MemoryCache
}

// NewTTLCache creates a new TTL cache
func NewTTLCache() *TTLCache {
	return &TTLCache{
		MemoryCache: NewMemoryCache(),
	}
}

// SetWithTTL stores a value with a specific TTL
func (tc *TTLCache) SetWithTTL(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	return tc.Set(ctx, key, value, ttl)
}

// GetTTL returns the remaining TTL for a key
func (tc *TTLCache) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	tc.mutex.RLock()
	defer tc.mutex.RUnlock()
	
	item, exists := tc.items[key]
	if !exists {
		return 0, fmt.Errorf("key not found")
	}
	
	if item.IsExpired() {
		delete(tc.items, key)
		return 0, fmt.Errorf("key expired")
	}
	
	remaining := time.Until(item.Expiration)
	if remaining < 0 {
		return 0, nil
	}
	
	return remaining, nil
}

// Extend extends the TTL of an existing key
func (tc *TTLCache) Extend(ctx context.Context, key string, additionalTTL time.Duration) error {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	
	item, exists := tc.items[key]
	if !exists {
		return fmt.Errorf("key not found")
	}
	
	if item.IsExpired() {
		delete(tc.items, key)
		return fmt.Errorf("key expired")
	}
	
	item.Expiration = item.Expiration.Add(additionalTTL)
	return nil
}

// Refresh resets the TTL of a key to the original value
func (tc *TTLCache) Refresh(ctx context.Context, key string, newTTL time.Duration) error {
	tc.mutex.Lock()
	defer tc.mutex.Unlock()
	
	item, exists := tc.items[key]
	if !exists {
		return fmt.Errorf("key not found")
	}
	
	if item.IsExpired() {
		delete(tc.items, key)
		return fmt.Errorf("key expired")
	}
	
	item.Expiration = time.Now().Add(newTTL)
	return nil
}