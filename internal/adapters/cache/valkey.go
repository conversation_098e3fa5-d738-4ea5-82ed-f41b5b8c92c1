package cache

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

// ValkeyCache implements the CacheInterface using Valkey (Redis-compatible)
type ValkeyCache struct {
	client    *redis.Client
	keyPrefix string
}

// ValkeyConfig holds Valkey connection configuration
type ValkeyConfig struct {
	Host         string
	Port         int
	Password     string
	DB           int
	KeyPrefix    string
	PoolSize     int
	MinIdleConns int
	MaxRetries   int
	DialTimeout  time.Duration
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

// NewValkeyCache creates a new Valkey cache instance
func NewValkeyCache(config ValkeyConfig) (*ValkeyCache, error) {
	// Set defaults
	if config.KeyPrefix == "" {
		config.KeyPrefix = "gokeys:"
	}
	if config.PoolSize == 0 {
		config.PoolSize = 10
	}
	if config.MinIdleConns == 0 {
		config.MinIdleConns = 2
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.DialTimeout == 0 {
		config.DialTimeout = 5 * time.Second
	}
	if config.ReadTimeout == 0 {
		config.ReadTimeout = 3 * time.Second
	}
	if config.WriteTimeout == 0 {
		config.WriteTimeout = 3 * time.Second
	}

	// Create Valkey client (using Redis-compatible protocol)
	client := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", config.Host, config.Port),
		Password:     config.Password,
		DB:           config.DB,
		PoolSize:     config.PoolSize,
		MinIdleConns: config.MinIdleConns,
		MaxRetries:   config.MaxRetries,
		DialTimeout:  config.DialTimeout,
		ReadTimeout:  config.ReadTimeout,
		WriteTimeout: config.WriteTimeout,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Valkey: %w", err)
	}

	return &ValkeyCache{
		client:    client,
		keyPrefix: config.KeyPrefix,
	}, nil
}

// Set stores a value in Valkey with expiration
func (vc *ValkeyCache) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	fullKey := vc.keyPrefix + key
	
	// Convert value to string if it's not already
	var stringValue string
	switch v := value.(type) {
	case string:
		stringValue = v
	case []byte:
		stringValue = string(v)
	default:
		stringValue = fmt.Sprintf("%v", v)
	}

	err := vc.client.Set(ctx, fullKey, stringValue, expiration).Err()
	if err != nil {
		return fmt.Errorf("failed to set key %s in Valkey: %w", key, err)
	}

	return nil
}

// Get retrieves a value from Valkey
func (vc *ValkeyCache) Get(ctx context.Context, key string) (string, error) {
	fullKey := vc.keyPrefix + key
	
	result, err := vc.client.Get(ctx, fullKey).Result()
	if err != nil {
		if err == redis.Nil {
			return "", fmt.Errorf("key not found")
		}
		return "", fmt.Errorf("failed to get key %s from Valkey: %w", key, err)
	}

	return result, nil
}

// Delete removes a key from Valkey
func (vc *ValkeyCache) Delete(ctx context.Context, key string) error {
	fullKey := vc.keyPrefix + key
	
	err := vc.client.Del(ctx, fullKey).Err()
	if err != nil {
		return fmt.Errorf("failed to delete key %s from Valkey: %w", key, err)
	}

	return nil
}

// Exists checks if a key exists in Valkey
func (vc *ValkeyCache) Exists(ctx context.Context, key string) (bool, error) {
	fullKey := vc.keyPrefix + key
	
	result, err := vc.client.Exists(ctx, fullKey).Result()
	if err != nil {
		return false, fmt.Errorf("failed to check existence of key %s in Valkey: %w", key, err)
	}

	return result > 0, nil
}

// SetNX sets a key only if it doesn't exist (atomic operation)
func (vc *ValkeyCache) SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	fullKey := vc.keyPrefix + key
	
	var stringValue string
	switch v := value.(type) {
	case string:
		stringValue = v
	case []byte:
		stringValue = string(v)
	default:
		stringValue = fmt.Sprintf("%v", v)
	}

	result, err := vc.client.SetNX(ctx, fullKey, stringValue, expiration).Result()
	if err != nil {
		return false, fmt.Errorf("failed to setnx key %s in Valkey: %w", key, err)
	}

	return result, nil
}

// GetTTL returns the remaining TTL for a key
func (vc *ValkeyCache) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	fullKey := vc.keyPrefix + key
	
	ttl, err := vc.client.TTL(ctx, fullKey).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get TTL for key %s: %w", key, err)
	}

	return ttl, nil
}

// Expire sets expiration for an existing key
func (vc *ValkeyCache) Expire(ctx context.Context, key string, expiration time.Duration) error {
	fullKey := vc.keyPrefix + key
	
	result, err := vc.client.Expire(ctx, fullKey, expiration).Result()
	if err != nil {
		return fmt.Errorf("failed to set expiration for key %s: %w", key, err)
	}

	if !result {
		return fmt.Errorf("key %s does not exist", key)
	}

	return nil
}

// Increment atomically increments a numeric value
func (vc *ValkeyCache) Increment(ctx context.Context, key string) (int64, error) {
	fullKey := vc.keyPrefix + key
	
	result, err := vc.client.Incr(ctx, fullKey).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to increment key %s: %w", key, err)
	}

	return result, nil
}

// IncrementBy atomically increments a numeric value by the specified amount
func (vc *ValkeyCache) IncrementBy(ctx context.Context, key string, value int64) (int64, error) {
	fullKey := vc.keyPrefix + key
	
	result, err := vc.client.IncrBy(ctx, fullKey, value).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to increment key %s by %d: %w", key, value, err)
	}

	return result, nil
}

// MGet gets multiple keys at once
func (vc *ValkeyCache) MGet(ctx context.Context, keys ...string) ([]interface{}, error) {
	fullKeys := make([]string, len(keys))
	for i, key := range keys {
		fullKeys[i] = vc.keyPrefix + key
	}
	
	result, err := vc.client.MGet(ctx, fullKeys...).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get multiple keys from Valkey: %w", err)
	}

	return result, nil
}

// MSet sets multiple key-value pairs at once
func (vc *ValkeyCache) MSet(ctx context.Context, pairs map[string]interface{}) error {
	fullPairs := make(map[string]interface{})
	for key, value := range pairs {
		fullKey := vc.keyPrefix + key
		
		// Convert value to string
		switch v := value.(type) {
		case string:
			fullPairs[fullKey] = v
		case []byte:
			fullPairs[fullKey] = string(v)
		default:
			fullPairs[fullKey] = fmt.Sprintf("%v", v)
		}
	}

	err := vc.client.MSet(ctx, fullPairs).Err()
	if err != nil {
		return fmt.Errorf("failed to set multiple keys in Valkey: %w", err)
	}

	return nil
}

// DeletePattern deletes all keys matching a pattern
func (vc *ValkeyCache) DeletePattern(ctx context.Context, pattern string) (int64, error) {
	fullPattern := vc.keyPrefix + pattern
	
	// Use SCAN to find keys matching the pattern
	var cursor uint64
	var deletedCount int64
	
	for {
		keys, nextCursor, err := vc.client.Scan(ctx, cursor, fullPattern, 100).Result()
		if err != nil {
			return deletedCount, fmt.Errorf("failed to scan keys with pattern %s: %w", pattern, err)
		}

		if len(keys) > 0 {
			deleted, err := vc.client.Del(ctx, keys...).Result()
			if err != nil {
				return deletedCount, fmt.Errorf("failed to delete keys: %w", err)
			}
			deletedCount += deleted
		}

		cursor = nextCursor
		if cursor == 0 {
			break
		}
	}

	return deletedCount, nil
}

// FlushDB clears all keys in the current database
func (vc *ValkeyCache) FlushDB(ctx context.Context) error {
	err := vc.client.FlushDB(ctx).Err()
	if err != nil {
		return fmt.Errorf("failed to flush Valkey database: %w", err)
	}

	return nil
}

// Ping tests the connection to Valkey
func (vc *ValkeyCache) Ping(ctx context.Context) error {
	err := vc.client.Ping(ctx).Err()
	if err != nil {
		return fmt.Errorf("Valkey ping failed: %w", err)
	}

	return nil
}

// Close closes the Valkey connection
func (vc *ValkeyCache) Close() error {
	return vc.client.Close()
}

// GetStats returns Valkey connection and performance statistics
func (vc *ValkeyCache) GetStats() map[string]interface{} {
	stats := vc.client.PoolStats()
	
	return map[string]interface{}{
		"hits":           stats.Hits,
		"misses":         stats.Misses,
		"timeouts":       stats.Timeouts,
		"total_conns":    stats.TotalConns,
		"idle_conns":     stats.IdleConns,
		"stale_conns":    stats.StaleConns,
	}
}

// HSet sets a field in a hash
func (vc *ValkeyCache) HSet(ctx context.Context, key, field string, value interface{}) error {
	fullKey := vc.keyPrefix + key
	
	var stringValue string
	switch v := value.(type) {
	case string:
		stringValue = v
	case []byte:
		stringValue = string(v)
	default:
		stringValue = fmt.Sprintf("%v", v)
	}

	err := vc.client.HSet(ctx, fullKey, field, stringValue).Err()
	if err != nil {
		return fmt.Errorf("failed to set hash field %s.%s: %w", key, field, err)
	}

	return nil
}

// HGet gets a field from a hash
func (vc *ValkeyCache) HGet(ctx context.Context, key, field string) (string, error) {
	fullKey := vc.keyPrefix + key
	
	result, err := vc.client.HGet(ctx, fullKey, field).Result()
	if err != nil {
		if err == redis.Nil {
			return "", fmt.Errorf("hash field not found")
		}
		return "", fmt.Errorf("failed to get hash field %s.%s: %w", key, field, err)
	}

	return result, nil
}

// HGetAll gets all fields from a hash
func (vc *ValkeyCache) HGetAll(ctx context.Context, key string) (map[string]string, error) {
	fullKey := vc.keyPrefix + key
	
	result, err := vc.client.HGetAll(ctx, fullKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get all hash fields for %s: %w", key, err)
	}

	return result, nil
}

// HDel deletes fields from a hash
func (vc *ValkeyCache) HDel(ctx context.Context, key string, fields ...string) (int64, error) {
	fullKey := vc.keyPrefix + key
	
	result, err := vc.client.HDel(ctx, fullKey, fields...).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to delete hash fields from %s: %w", key, err)
	}

	return result, nil
}

// WithPrefix creates a new ValkeyCache instance with an additional key prefix
func (vc *ValkeyCache) WithPrefix(additionalPrefix string) *ValkeyCache {
	return &ValkeyCache{
		client:    vc.client,
		keyPrefix: vc.keyPrefix + additionalPrefix + ":",
	}
}

// Lock implements distributed locking using Valkey
func (vc *ValkeyCache) Lock(ctx context.Context, key string, expiration time.Duration) (bool, error) {
	lockKey := vc.keyPrefix + "lock:" + key
	lockValue := fmt.Sprintf("%d", time.Now().UnixNano())
	
	acquired, err := vc.client.SetNX(ctx, lockKey, lockValue, expiration).Result()
	if err != nil {
		return false, fmt.Errorf("failed to acquire lock %s: %w", key, err)
	}

	return acquired, nil
}

// Unlock releases a distributed lock
func (vc *ValkeyCache) Unlock(ctx context.Context, key string) error {
	lockKey := vc.keyPrefix + "lock:" + key
	
	err := vc.client.Del(ctx, lockKey).Err()
	if err != nil {
		return fmt.Errorf("failed to release lock %s: %w", key, err)
	}

	return nil
}

// NewValkeyFromEnvironment creates a Valkey cache from environment variables
func NewValkeyFromEnvironment() (*ValkeyCache, error) {
	config := ValkeyConfig{
		Host:     getEnv("VALKEY_HOST", "localhost"),
		Port:     getEnvInt("VALKEY_PORT", 6379),
		Password: getEnv("VALKEY_PASSWORD", ""),
		DB:       getEnvInt("VALKEY_DB", 0),
	}

	return NewValkeyCache(config)
}

// Helper functions for environment variables
func getEnv(key, defaultValue string) string {
	// This would use os.Getenv in a real implementation
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	// This would parse os.Getenv in a real implementation
	return defaultValue
}