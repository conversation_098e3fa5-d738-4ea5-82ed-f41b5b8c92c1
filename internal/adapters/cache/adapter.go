package cache

import (
	"time"

	"github.com/gokeys/gokeys/internal/ports/cache"
)

// MemoryCacheAdapter adapts MemoryCache to match cache.Cache interface
type MemoryCacheAdapter struct {
	memoryCache *MemoryCache
}

// NewMemoryCacheAdapter creates a new memory cache adapter
func NewMemoryCacheAdapter() cache.Cache {
	return &MemoryCacheAdapter{
		memoryCache: NewMemoryCache(),
	}
}

// Get retrieves a value from the cache
func (a *MemoryCacheAdapter) Get(key string) (interface{}, bool) {
	a.memoryCache.mutex.RLock()
	defer a.memoryCache.mutex.RUnlock()
	
	item, exists := a.memoryCache.items[key]
	if !exists {
		return nil, false
	}
	
	if item.IsExpired() {
		delete(a.memoryCache.items, key)
		return nil, false
	}
	
	return item.Value, true
}

// Set stores a value in the cache with TTL
func (a *MemoryCacheAdapter) Set(key string, value interface{}, ttl time.Duration) error {
	a.memoryCache.mutex.Lock()
	defer a.memoryCache.mutex.Unlock()
	
	exp := time.Now().Add(ttl)
	if ttl <= 0 {
		exp = time.Now().Add(24 * time.Hour * 365) // 1 year from now
	}
	
	a.memoryCache.items[key] = &CacheItem{
		Value:      value,
		Expiration: exp,
	}
	
	return nil
}

// Delete removes a key from the cache
func (a *MemoryCacheAdapter) Delete(key string) error {
	a.memoryCache.mutex.Lock()
	defer a.memoryCache.mutex.Unlock()
	
	delete(a.memoryCache.items, key)
	return nil
}

// Exists checks if a key exists in the cache
func (a *MemoryCacheAdapter) Exists(key string) bool {
	a.memoryCache.mutex.RLock()
	defer a.memoryCache.mutex.RUnlock()
	
	item, exists := a.memoryCache.items[key]
	if !exists {
		return false
	}
	
	if item.IsExpired() {
		delete(a.memoryCache.items, key)
		return false
	}
	
	return true
}

// Clear removes all items from the cache
func (a *MemoryCacheAdapter) Clear() error {
	a.memoryCache.Clear()
	return nil
}

// Keys returns all keys matching a pattern (simplified implementation)
func (a *MemoryCacheAdapter) Keys(pattern string) ([]string, error) {
	keys := a.memoryCache.Keys()
	// For simplicity, return all keys if pattern is "*" or empty
	if pattern == "*" || pattern == "" {
		return keys, nil
	}
	
	// For more complex patterns, would need pattern matching
	// For now, return all keys
	return keys, nil
}

// TTL returns the time-to-live for a key
func (a *MemoryCacheAdapter) TTL(key string) (time.Duration, error) {
	a.memoryCache.mutex.RLock()
	defer a.memoryCache.mutex.RUnlock()
	
	item, exists := a.memoryCache.items[key]
	if !exists {
		return 0, nil
	}
	
	if item.IsExpired() {
		delete(a.memoryCache.items, key)
		return 0, nil
	}
	
	remaining := time.Until(item.Expiration)
	if remaining < 0 {
		return 0, nil
	}
	
	return remaining, nil
}

// Expire sets the TTL for an existing key
func (a *MemoryCacheAdapter) Expire(key string, ttl time.Duration) error {
	a.memoryCache.mutex.Lock()
	defer a.memoryCache.mutex.Unlock()
	
	item, exists := a.memoryCache.items[key]
	if !exists {
		return nil // Key doesn't exist, which is fine
	}
	
	if item.IsExpired() {
		delete(a.memoryCache.items, key)
		return nil
	}
	
	item.Expiration = time.Now().Add(ttl)
	return nil
}

// ValkeyCacheAdapter adapts ValkeyCache to match cache.Cache interface
type ValkeyCacheAdapter struct {
	valkeyCache *ValkeyCache
}

// NewValkeyCacheAdapter creates a new Valkey cache adapter
func NewValkeyCacheAdapter(config ValkeyConfig) (cache.Cache, error) {
	valkeyCache, err := NewValkeyCache(config)
	if err != nil {
		return nil, err
	}
	
	return &ValkeyCacheAdapter{
		valkeyCache: valkeyCache,
	}, nil
}

// Get retrieves a value from the cache
func (a *ValkeyCacheAdapter) Get(key string) (interface{}, bool) {
	value, err := a.valkeyCache.Get(nil, key)
	if err != nil {
		return nil, false
	}
	return value, true
}

// Set stores a value in the cache with TTL
func (a *ValkeyCacheAdapter) Set(key string, value interface{}, ttl time.Duration) error {
	return a.valkeyCache.Set(nil, key, value, ttl)
}

// Delete removes a key from the cache
func (a *ValkeyCacheAdapter) Delete(key string) error {
	return a.valkeyCache.Delete(nil, key)
}

// Exists checks if a key exists in the cache
func (a *ValkeyCacheAdapter) Exists(key string) bool {
	exists, err := a.valkeyCache.Exists(nil, key)
	if err != nil {
		return false
	}
	return exists
}

// Clear removes all items from the cache
func (a *ValkeyCacheAdapter) Clear() error {
	return a.valkeyCache.FlushDB(nil)
}

// Keys returns all keys matching a pattern
func (a *ValkeyCacheAdapter) Keys(pattern string) ([]string, error) {
	// This would need to be implemented with SCAN
	// For now, return empty slice
	return []string{}, nil
}

// TTL returns the time-to-live for a key
func (a *ValkeyCacheAdapter) TTL(key string) (time.Duration, error) {
	return a.valkeyCache.GetTTL(nil, key)
}

// Expire sets the TTL for an existing key
func (a *ValkeyCacheAdapter) Expire(key string, ttl time.Duration) error {
	return a.valkeyCache.Expire(nil, key, ttl)
}