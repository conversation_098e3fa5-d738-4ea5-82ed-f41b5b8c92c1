package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	"github.com/rs/zerolog"
)

// CallerHook adds file and line information to log entries
type CallerHook struct{}

// Run implements zerolog.Hook
func (h CallerHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	if level == zerolog.NoLevel {
		return
	}

	// Get caller information
	_, file, line, ok := runtime.Caller(4) // Adjust depth as needed
	if ok {
		// Get just the filename without full path
		filename := filepath.Base(file)
		e.Str("caller", fmt.Sprintf("%s:%d", filename, line))
	}
}

// CorrelationIDHook adds correlation ID from context
type CorrelationIDHook struct{}

// Run implements zerolog.Hook
func (h CorrelationIDHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	// This would need to be set up with a context that carries correlation ID
	// For now, we'll add a placeholder
	if correlationID := os.Getenv("CORRELATION_ID"); correlationID != "" {
		e.Str("correlation_id", correlationID)
	}
}

// ServiceInfoHook adds service information to every log entry
type ServiceInfoHook struct {
	ServiceName    string
	ServiceVersion string
	Environment    string
	InstanceID     string
}

// Run implements zerolog.Hook
func (h ServiceInfoHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	if h.ServiceName != "" {
		e.Str("service", h.ServiceName)
	}
	if h.ServiceVersion != "" {
		e.Str("version", h.ServiceVersion)
	}
	if h.Environment != "" {
		e.Str("environment", h.Environment)
	}
	if h.InstanceID != "" {
		e.Str("instance_id", h.InstanceID)
	}
}

// SecurityHook adds security context to log entries
type SecurityHook struct{}

// Run implements zerolog.Hook
func (h SecurityHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	// Add security-related information
	if level >= zerolog.WarnLevel {
		// Add security flag for warning and above
		e.Bool("security_relevant", true)
	}
	
	// Check if this is a security event
	if strings.Contains(msg, "security") || 
	   strings.Contains(msg, "auth") || 
	   strings.Contains(msg, "unauthorized") ||
	   strings.Contains(msg, "forbidden") {
		e.Str("event_category", "security")
	}
}

// PerformanceHook adds performance tracking
type PerformanceHook struct{}

// Run implements zerolog.Hook
func (h PerformanceHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	// Add memory usage information for error and above
	if level >= zerolog.ErrorLevel {
		var m runtime.MemStats
		runtime.ReadMemStats(&m)
		e.Uint64("memory_alloc", m.Alloc)
		e.Uint64("memory_sys", m.Sys)
		e.Uint32("goroutines", uint32(runtime.NumGoroutine()))
	}
}

// SanitizationHook removes sensitive information from logs
type SanitizationHook struct {
	SensitiveFields []string
}

// Run implements zerolog.Hook
func (h SanitizationHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	// Note: This is a basic implementation
	// In a real system, you'd want more sophisticated sanitization
	for _, field := range h.SensitiveFields {
		if strings.Contains(strings.ToLower(msg), strings.ToLower(field)) {
			e.Str("sanitized", "message contains sensitive information")
			break
		}
	}
}

// ErrorStackHook adds stack trace for errors
type ErrorStackHook struct{}

// Run implements zerolog.Hook
func (h ErrorStackHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	if level >= zerolog.ErrorLevel {
		// Add stack trace for errors
		stack := make([]byte, 2048)
		n := runtime.Stack(stack, false)
		e.Str("stack_trace", string(stack[:n]))
	}
}

// MetricsHook sends log metrics to monitoring system
type MetricsHook struct {
	// This would contain metrics client
}

// Run implements zerolog.Hook
func (h MetricsHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	// In a real implementation, you'd increment counters/metrics here
	// For now, we'll just add a metric flag
	e.Bool("metrics_tracked", true)
}

// AuditHook ensures audit events are properly marked
type AuditHook struct{}

// Run implements zerolog.Hook
func (h AuditHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	auditKeywords := []string{"audit", "create", "update", "delete", "login", "logout", "access"}
	
	for _, keyword := range auditKeywords {
		if strings.Contains(strings.ToLower(msg), keyword) {
			e.Str("audit_category", "user_action")
			e.Bool("audit_required", true)
			break
		}
	}
}

// NewZerologWithHooks creates a zerolog logger with all hooks configured
func NewZerologWithHooks(config LoggerConfig) *StructuredLogger {
	// Create base logger
	baseLogger := NewLogger(config)
	
	// Create hooks
	callerHook := CallerHook{}
	serviceHook := ServiceInfoHook{
		ServiceName:    "gokeys",
		ServiceVersion: getEnv("APP_VERSION", "dev"),
		Environment:    getEnv("APP_ENV", "production"),
		InstanceID:     getEnv("INSTANCE_ID", ""),
	}
	securityHook := SecurityHook{}
	sanitizationHook := SanitizationHook{
		SensitiveFields: []string{"password", "token", "secret", "key", "auth"},
	}
	auditHook := AuditHook{}
	
	// Add hooks to logger
	logger := baseLogger.logger.Hook(callerHook).
		Hook(serviceHook).
		Hook(securityHook).
		Hook(sanitizationHook).
		Hook(auditHook)
	
	// Add performance hook only in development
	if getEnv("APP_ENV", "production") == "development" {
		performanceHook := PerformanceHook{}
		logger = logger.Hook(performanceHook)
	}
	
	// Add error stack hook for errors
	if config.Level == "debug" || config.Level == "trace" {
		errorStackHook := ErrorStackHook{}
		logger = logger.Hook(errorStackHook)
	}
	
	return &StructuredLogger{
		logger: logger,
	}
}

// ContextLoggerHook extracts values from context and adds them to log
type ContextLoggerHook struct{}

// Run implements zerolog.Hook
func (h ContextLoggerHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	// This would need access to the current context
	// In practice, you'd pass context through the logger
}

// HTTPRequestHook adds HTTP request information
type HTTPRequestHook struct {
	RequestID string
	UserID    string
	IP        string
	UserAgent string
	Method    string
	Path      string
}

// Run implements zerolog.Hook
func (h HTTPRequestHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	if h.RequestID != "" {
		e.Str("request_id", h.RequestID)
	}
	if h.UserID != "" {
		e.Str("user_id", h.UserID)
	}
	if h.IP != "" {
		e.Str("client_ip", h.IP)
	}
	if h.UserAgent != "" {
		e.Str("user_agent", h.UserAgent)
	}
	if h.Method != "" {
		e.Str("http_method", h.Method)
	}
	if h.Path != "" {
		e.Str("http_path", h.Path)
	}
}

// NewHTTPLogger creates a logger with HTTP context
func NewHTTPLogger(baseLogger *StructuredLogger, requestID, userID, ip, userAgent, method, path string) *StructuredLogger {
	httpHook := HTTPRequestHook{
		RequestID: requestID,
		UserID:    userID,
		IP:        ip,
		UserAgent: userAgent,
		Method:    method,
		Path:      path,
	}
	
	return &StructuredLogger{
		logger: baseLogger.logger.Hook(httpHook),
	}
}