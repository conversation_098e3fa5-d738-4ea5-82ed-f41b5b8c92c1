package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/pkgerrors"
)

// Logger interface for dependency injection
type Logger interface {
	Debug(args ...interface{})
	Info(args ...interface{})
	Warn(args ...interface{})
	Error(args ...interface{})
	Fatal(args ...interface{})

	Debugf(format string, args ...interface{})
	Infof(format string, args ...interface{})
	Warnf(format string, args ...interface{})
	Errorf(format string, args ...interface{})
	Fatalf(format string, args ...interface{})

	WithField(key string, value interface{}) Logger
	WithFields(fields map[string]interface{}) Logger
	WithContext(ctx context.Context) Logger
	WithError(err error) Logger
}

// StructuredLogger implements Logger interface using zerolog
type StructuredLogger struct {
	logger zerolog.Logger
}

// LoggerConfig holds logger configuration
type LoggerConfig struct {
	Level      string                 // debug, info, warn, error
	Format     string                 // json, text
	Output     io.Writer              // output destination
	Fields     map[string]interface{} // default fields
	Filename   string                 // log file path
	MaxSize    int                    // max file size in MB
	MaxBackups int                    // max backup files
	MaxAge     int                    // max age in days
	Compress   bool                   // compress old files
}

// NewLogger creates a new structured logger
func NewLogger(config LoggerConfig) *StructuredLogger {
	// Enable stack trace capture
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack

	// Set global log level
	level, err := zerolog.ParseLevel(config.Level)
	if err != nil {
		level = zerolog.InfoLevel
	}
	zerolog.SetGlobalLevel(level)

	// Configure output writer
	var output io.Writer
	if config.Output != nil {
		output = config.Output
	} else if config.Filename != "" {
		// File rotation can be implemented with lumberjack if needed
		file, err := os.OpenFile(config.Filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			output = os.Stdout
		} else {
			output = file
		}
	} else {
		output = os.Stdout
	}

	// Configure logger based on format
	var logger zerolog.Logger
	switch strings.ToLower(config.Format) {
	case "console", "text":
		// Pretty console output for development
		consoleWriter := zerolog.ConsoleWriter{
			Out:        output,
			TimeFormat: time.RFC3339,
			NoColor:    false,
		}
		logger = zerolog.New(consoleWriter).With().Timestamp().Logger()
	case "json":
		// JSON output for production
		logger = zerolog.New(output).With().Timestamp().Logger()
	default:
		// Default to JSON
		logger = zerolog.New(output).With().Timestamp().Logger()
	}

	// Add default fields
	if config.Fields != nil {
		for key, value := range config.Fields {
			logger = logger.With().Interface(key, value).Logger()
		}
	}

	return &StructuredLogger{
		logger: logger,
	}
}

// NewFromEnvironment creates logger from environment variables
func NewFromEnvironment() *StructuredLogger {
	config := LoggerConfig{
		Level:  getEnv("LOG_LEVEL", "info"),
		Format: getEnv("LOG_FORMAT", "json"),
		Fields: map[string]interface{}{
			"service": "gokeys",
			"version": getEnv("APP_VERSION", "dev"),
		},
	}

	// Set log file if specified
	if filename := getEnv("LOG_FILE", ""); filename != "" {
		config.Filename = filename
	}

	return NewLogger(config)
}

// Debug logs a debug message
func (l *StructuredLogger) Debug(args ...interface{}) {
	l.logger.Debug().Msg(fmt.Sprint(args...))
}

// Info logs an info message
func (l *StructuredLogger) Info(args ...interface{}) {
	l.logger.Info().Msg(fmt.Sprint(args...))
}

// Warn logs a warning message
func (l *StructuredLogger) Warn(args ...interface{}) {
	l.logger.Warn().Msg(fmt.Sprint(args...))
}

// Error logs an error message
func (l *StructuredLogger) Error(args ...interface{}) {
	l.logger.Error().Msg(fmt.Sprint(args...))
}

// Fatal logs a fatal message and exits
func (l *StructuredLogger) Fatal(args ...interface{}) {
	l.logger.Fatal().Msg(fmt.Sprint(args...))
}

// Debugf logs a debug message with formatting
func (l *StructuredLogger) Debugf(format string, args ...interface{}) {
	l.logger.Debug().Msgf(format, args...)
}

// Infof logs an info message with formatting
func (l *StructuredLogger) Infof(format string, args ...interface{}) {
	l.logger.Info().Msgf(format, args...)
}

// Warnf logs a warning message with formatting
func (l *StructuredLogger) Warnf(format string, args ...interface{}) {
	l.logger.Warn().Msgf(format, args...)
}

// Errorf logs an error message with formatting
func (l *StructuredLogger) Errorf(format string, args ...interface{}) {
	l.logger.Error().Msgf(format, args...)
}

// Fatalf logs a fatal message with formatting and exits
func (l *StructuredLogger) Fatalf(format string, args ...interface{}) {
	l.logger.Fatal().Msgf(format, args...)
}

// WithField adds a field to the logger
func (l *StructuredLogger) WithField(key string, value interface{}) Logger {
	return &StructuredLogger{
		logger: l.logger.With().Interface(key, value).Logger(),
	}
}

// WithFields adds multiple fields to the logger
func (l *StructuredLogger) WithFields(fields map[string]interface{}) Logger {
	logger := l.logger
	for key, value := range fields {
		logger = logger.With().Interface(key, value).Logger()
	}
	return &StructuredLogger{
		logger: logger,
	}
}

// WithContext adds context to the logger
func (l *StructuredLogger) WithContext(ctx context.Context) Logger {
	logger := l.logger

	// Extract common context values
	if requestID := ctx.Value("request_id"); requestID != nil {
		logger = logger.With().Interface("request_id", requestID).Logger()
	}

	if userID := ctx.Value("user_id"); userID != nil {
		logger = logger.With().Interface("user_id", userID).Logger()
	}

	if organizationID := ctx.Value("organization_id"); organizationID != nil {
		logger = logger.With().Interface("organization_id", organizationID).Logger()
	}

	return &StructuredLogger{
		logger: logger,
	}
}

// WithError adds error to the logger
func (l *StructuredLogger) WithError(err error) Logger {
	return &StructuredLogger{
		logger: l.logger.With().Err(err).Logger(),
	}
}

// RequestLogger creates a logger with request context
func (l *StructuredLogger) RequestLogger(c *gin.Context) Logger {
	fields := map[string]interface{}{
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"ip":         c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	}

	// Add request ID if available
	if requestID := c.GetString("request_id"); requestID != "" {
		fields["request_id"] = requestID
	}

	// Add user context if available
	if userID := c.GetString("user_id"); userID != "" {
		fields["user_id"] = userID
	}

	if organizationID := c.GetString("organization_id"); organizationID != "" {
		fields["organization_id"] = organizationID
	}

	return l.WithFields(fields)
}

// SecurityLogger creates a logger for security events
func (l *StructuredLogger) SecurityLogger() Logger {
	return l.WithFields(map[string]interface{}{
		"category": "security",
		"priority": "high",
	})
}

// AuditLogger creates a logger for audit events
func (l *StructuredLogger) AuditLogger() Logger {
	return l.WithFields(map[string]interface{}{
		"category": "audit",
		"priority": "high",
	})
}

// LicenseLogger creates a logger for license events
func (l *StructuredLogger) LicenseLogger() Logger {
	return l.WithFields(map[string]interface{}{
		"category": "license",
		"priority": "medium",
	})
}

// DatabaseLogger creates a logger for database events
func (l *StructuredLogger) DatabaseLogger() Logger {
	return l.WithFields(map[string]interface{}{
		"category": "database",
		"priority": "low",
	})
}

// CacheLogger creates a logger for cache events
func (l *StructuredLogger) CacheLogger() Logger {
	return l.WithFields(map[string]interface{}{
		"category": "cache",
		"priority": "low",
	})
}

// MetricsLogger creates a logger for metrics events
func (l *StructuredLogger) MetricsLogger() Logger {
	return l.WithFields(map[string]interface{}{
		"category": "metrics",
		"priority": "low",
	})
}

// LogLevel represents log levels
type LogLevel int

const (
	DebugLevel LogLevel = iota
	InfoLevel
	WarnLevel
	ErrorLevel
	FatalLevel
)

// String returns string representation of log level
func (l LogLevel) String() string {
	switch l {
	case DebugLevel:
		return "debug"
	case InfoLevel:
		return "info"
	case WarnLevel:
		return "warn"
	case ErrorLevel:
		return "error"
	case FatalLevel:
		return "fatal"
	default:
		return "unknown"
	}
}

// Helper functions for structured logging

// LogRequestStart logs the start of a request
func LogRequestStart(logger Logger, c *gin.Context) {
	logger.WithFields(map[string]interface{}{
		"event":      "request_start",
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"query":      c.Request.URL.RawQuery,
		"ip":         c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	}).Info("Request started")
}

// LogRequestEnd logs the end of a request
func LogRequestEnd(logger Logger, c *gin.Context, duration time.Duration) {
	logger.WithFields(map[string]interface{}{
		"event":         "request_end",
		"status":        c.Writer.Status(),
		"duration_ms":   duration.Milliseconds(),
		"response_size": c.Writer.Size(),
	}).Info("Request completed")
}

// LogLicenseValidation logs license validation events
func LogLicenseValidation(logger Logger, licenseKey string, valid bool, reason string) {
	logger.WithFields(map[string]interface{}{
		"event":            "license_validation",
		"license_key_hash": hashString(licenseKey), // Hash for privacy
		"valid":            valid,
		"reason":           reason,
	}).Info("License validation performed")
}

// LogSecurityEvent logs security-related events
func LogSecurityEvent(logger Logger, event string, severity string, details map[string]interface{}) {
	logger.WithFields(map[string]interface{}{
		"event":          "security_event",
		"security_event": event,
		"severity":       severity,
		"details":        details,
	}).Warn("Security event detected")
}

// LogDatabaseOperation logs database operations
func LogDatabaseOperation(logger Logger, operation string, table string, duration time.Duration, err error) {
	fields := map[string]interface{}{
		"event":       "database_operation",
		"operation":   operation,
		"table":       table,
		"duration_ms": duration.Milliseconds(),
	}

	if err != nil {
		fields["error"] = err.Error()
		logger.WithFields(fields).Error("Database operation failed")
	} else {
		logger.WithFields(fields).Debug("Database operation completed")
	}
}

// LogCacheOperation logs cache operations
func LogCacheOperation(logger Logger, operation string, key string, hit bool, duration time.Duration) {
	logger.WithFields(map[string]interface{}{
		"event":       "cache_operation",
		"operation":   operation,
		"key":         key,
		"hit":         hit,
		"duration_ms": duration.Milliseconds(),
	}).Debug("Cache operation performed")
}

// Helper function to get environment variables
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Helper function to hash strings for privacy
func hashString(input string) string {
	if len(input) < 8 {
		return "***"
	}
	// Simple hash for logging - don't use for security
	return fmt.Sprintf("%x", []byte(input[:4])) + "***"
}
