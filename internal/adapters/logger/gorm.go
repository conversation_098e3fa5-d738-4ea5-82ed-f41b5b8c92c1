package logger

import (
	"context"
	"errors"
	"time"

	gormlogger "gorm.io/gorm/logger"
)

// GormLogger implements GORM's logger interface using our structured logger
type GormLogger struct {
	logger               Logger
	logLevel             gormlogger.LogLevel
	slowThreshold        time.Duration
	ignoreRecordNotFound bool
}

// NewGormLogger creates a new GORM logger
func NewGormLogger(logger Logger) *GormLogger {
	return &GormLogger{
		logger:               logger,
		logLevel:             gormlogger.Info,
		slowThreshold:        200 * time.Millisecond,
		ignoreRecordNotFound: true,
	}
}

// WithConfig sets configuration for the GORM logger
func (gl *GormLogger) WithConfig(config GormLoggerConfig) *GormLogger {
	gl.logLevel = config.LogLevel
	gl.slowThreshold = config.SlowThreshold
	gl.ignoreRecordNotFound = config.IgnoreRecordNotFound
	return gl
}

// GormLoggerConfig holds configuration for GORM logger
type GormLoggerConfig struct {
	LogLevel             gormlogger.LogLevel
	SlowThreshold        time.Duration
	IgnoreRecordNotFound bool
}

// LogMode sets the log level
func (gl *GormLogger) LogMode(level gormlogger.LogLevel) gormlogger.Interface {
	newLogger := *gl
	newLogger.logLevel = level
	return &newLogger
}

// Info logs info messages
func (gl *GormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	if gl.logLevel >= gormlogger.Info {
		gl.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"event": "gorm_info",
			"data":  data,
		}).Infof(msg, data...)
	}
}

// Warn logs warning messages
func (gl *GormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	if gl.logLevel >= gormlogger.Warn {
		gl.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"event": "gorm_warn",
			"data":  data,
		}).Warnf(msg, data...)
	}
}

// Error logs error messages
func (gl *GormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	if gl.logLevel >= gormlogger.Error {
		gl.logger.WithContext(ctx).WithFields(map[string]interface{}{
			"event": "gorm_error",
			"data":  data,
		}).Errorf(msg, data...)
	}
}

// Trace logs SQL queries
func (gl *GormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	if gl.logLevel <= gormlogger.Silent {
		return
	}

	elapsed := time.Since(begin)
	sql, rows := fc()

	fields := map[string]interface{}{
		"event":         "database_query",
		"sql":           sql,
		"duration_ms":   elapsed.Milliseconds(),
		"duration":      elapsed.String(),
		"rows_affected": rows,
	}

	// Add context fields if available
	if requestID := ctx.Value("request_id"); requestID != nil {
		fields["request_id"] = requestID
	}

	logger := gl.logger.WithContext(ctx).WithFields(fields)

	switch {
	case err != nil && gl.logLevel >= gormlogger.Error && (!errors.Is(err, gormlogger.ErrRecordNotFound) || !gl.ignoreRecordNotFound):
		logger.WithError(err).Error("Database query failed")
	case elapsed > gl.slowThreshold && gl.slowThreshold != 0 && gl.logLevel >= gormlogger.Warn:
		logger.Warn("Slow database query detected")
	case gl.logLevel == gormlogger.Info:
		logger.Debug("Database query executed")
	}
}

// CreateGormLoggerFromEnvironment creates a GORM logger with environment configuration
func CreateGormLoggerFromEnvironment(logger Logger) *GormLogger {
	config := GormLoggerConfig{
		LogLevel:             gormlogger.Warn, // Default to warn level
		SlowThreshold:        200 * time.Millisecond,
		IgnoreRecordNotFound: true,
	}

	// Configure based on environment
	env := getEnv("APP_ENV", "production")
	if env == "development" {
		config.LogLevel = gormlogger.Info
		config.SlowThreshold = 100 * time.Millisecond
	}

	// Override with specific GORM log level if set
	if gormLogLevel := getEnv("GORM_LOG_LEVEL", ""); gormLogLevel != "" {
		switch gormLogLevel {
		case "silent":
			config.LogLevel = gormlogger.Silent
		case "error":
			config.LogLevel = gormlogger.Error
		case "warn":
			config.LogLevel = gormlogger.Warn
		case "info":
			config.LogLevel = gormlogger.Info
		}
	}

	return NewGormLogger(logger).WithConfig(config)
}