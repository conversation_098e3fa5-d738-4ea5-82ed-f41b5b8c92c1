package logger

import (
	"io"
	"time"

	"github.com/gin-gonic/gin"
)

// GinLogger provides Gin-compatible logging using our structured logger
type GinLogger struct {
	logger Logger
}

// NewGinLogger creates a new Gin logger
func NewGinLogger(logger Logger) *GinLogger {
	return &GinLogger{
		logger: logger,
	}
}

// Write implements io.Writer for Gin's default logger
func (gl *GinLogger) Write(p []byte) (n int, err error) {
	gl.logger.Info(string(p))
	return len(p), nil
}

// GinLoggerMiddleware returns a Gin middleware for structured logging
func (gl *GinLogger) GinLoggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithConfig(gin.LoggerConfig{
		Formatter: gl.customFormatter,
		Output:    gl,
	})
}

// customFormatter formats log entries in a structured way
func (gl *GinLogger) customFormatter(param gin.LogFormatterParams) string {
	// Use our structured logger instead of formatting
	fields := map[string]interface{}{
		"timestamp":     param.TimeStamp.Format(time.RFC3339),
		"status":        param.StatusCode,
		"latency":       param.Latency.String(),
		"latency_ms":    param.Latency.Milliseconds(),
		"client_ip":     param.ClientIP,
		"method":        param.Method,
		"path":          param.Path,
		"user_agent":    param.Request.UserAgent(),
		"response_size": param.BodySize,
	}

	// Add error message if present
	if param.ErrorMessage != "" {
		fields["error"] = param.ErrorMessage
	}

	// Add request ID if available
	if requestID := param.Request.Header.Get("X-Request-ID"); requestID != "" {
		fields["request_id"] = requestID
	}

	logger := gl.logger.WithFields(fields)

	// Log based on status code
	switch {
	case param.StatusCode >= 500:
		logger.Error("HTTP request completed with server error")
	case param.StatusCode >= 400:
		logger.Warn("HTTP request completed with client error")
	case param.StatusCode >= 300:
		logger.Info("HTTP request completed with redirect")
	default:
		logger.Info("HTTP request completed successfully")
	}

	return "" // Return empty string since we're using structured logging
}

// CustomGinRecovery returns a Gin middleware for panic recovery with structured logging
func (gl *GinLogger) CustomGinRecovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			gl.logger.WithFields(map[string]interface{}{
				"event":      "panic_recovery",
				"error":      err,
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"ip":         c.ClientIP(),
				"user_agent": c.Request.UserAgent(),
				"request_id": c.GetString("request_id"),
			}).Error("Panic recovered in HTTP handler")
		}
		c.AbortWithStatus(500)
	})
}

// SetGinMode sets Gin mode and configures logging accordingly
func SetGinMode(logger Logger) {
	env := getEnv("APP_ENV", "production")
	
	switch env {
	case "development":
		gin.SetMode(gin.DebugMode)
		logger.Info("Gin set to debug mode")
	case "test":
		gin.SetMode(gin.TestMode)
		gin.DefaultWriter = io.Discard // Disable Gin logging in tests
		logger.Info("Gin set to test mode")
	default:
		gin.SetMode(gin.ReleaseMode)
		gin.DefaultWriter = io.Discard // Use structured logging instead
		logger.Info("Gin set to release mode")
	}
}

// LoggerToWriter converts our Logger to an io.Writer for Gin
func LoggerToWriter(logger Logger) io.Writer {
	return &ginWriter{logger: logger}
}

type ginWriter struct {
	logger Logger
}

func (gw *ginWriter) Write(p []byte) (n int, err error) {
	gw.logger.Info(string(p))
	return len(p), nil
}

// GinDebugLogger provides debug-level logging for Gin in development
func GinDebugLogger(logger Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		if gin.Mode() == gin.DebugMode {
			start := time.Now()
			path := c.Request.URL.Path
			raw := c.Request.URL.RawQuery

			// Process request
			c.Next()

			// Log debug information
			param := gin.LogFormatterParams{
				Request:      c.Request,
				TimeStamp:    start,
				Latency:      time.Since(start),
				ClientIP:     c.ClientIP(),
				Method:       c.Request.Method,
				StatusCode:   c.Writer.Status(),
				ErrorMessage: c.Errors.ByType(gin.ErrorTypePrivate).String(),
				BodySize:     c.Writer.Size(),
			}

			if raw != "" {
				param.Path = path + "?" + raw
			} else {
				param.Path = path
			}

			logger.WithFields(map[string]interface{}{
				"method":        param.Method,
				"path":          param.Path,
				"status":        param.StatusCode,
				"latency":       param.Latency.String(),
				"client_ip":     param.ClientIP,
				"response_size": param.BodySize,
				"errors":        param.ErrorMessage,
			}).Debug("Gin debug log")
		}
	}
}

// HTTPErrorLogger logs HTTP errors with additional context
func HTTPErrorLogger(logger Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// Log errors that occurred during request processing
		for _, ginErr := range c.Errors {
			fields := map[string]interface{}{
				"event":      "http_error",
				"error":      ginErr.Error(),
				"error_type": ginErr.Type,
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"status":     c.Writer.Status(),
				"ip":         c.ClientIP(),
				"user_agent": c.Request.UserAgent(),
			}

			// Add request ID if available
			if requestID := c.GetString("request_id"); requestID != "" {
				fields["request_id"] = requestID
			}

			// Add user context if available
			if userID := c.GetString("user_id"); userID != "" {
				fields["user_id"] = userID
			}

			// Add additional error context if it's a custom error
			if ginErr.Meta != nil {
				fields["error_meta"] = ginErr.Meta
			}

			// Log based on error type
			switch ginErr.Type {
			case gin.ErrorTypeBind:
				logger.WithFields(fields).Warn("Request binding error")
			case gin.ErrorTypePublic:
				logger.WithFields(fields).Info("Public error occurred")
			case gin.ErrorTypePrivate:
				logger.WithFields(fields).Error("Private error occurred")
			default:
				logger.WithFields(fields).Error("Unknown error occurred")
			}
		}
	}
}

// AccessLogger logs access patterns for analysis
func AccessLogger(logger Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		c.Next()
		duration := time.Since(start)

		// Log access information
		logger.WithFields(map[string]interface{}{
			"event":         "access_log",
			"method":        c.Request.Method,
			"path":          c.Request.URL.Path,
			"query":         c.Request.URL.RawQuery,
			"status":        c.Writer.Status(),
			"duration_ms":   duration.Milliseconds(),
			"ip":            c.ClientIP(),
			"user_agent":    c.Request.UserAgent(),
			"referer":       c.Request.Referer(),
			"content_length": c.Request.ContentLength,
			"response_size": c.Writer.Size(),
			"request_id":    c.GetString("request_id"),
			"user_id":       c.GetString("user_id"),
		}).Info("Access logged")
	}
}