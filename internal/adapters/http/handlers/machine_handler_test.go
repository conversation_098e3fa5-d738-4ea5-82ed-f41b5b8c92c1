package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// Mock repositories are now defined in mocks_test.go

// Test setup helper
func setupMachineTestHandler() (*MachineHandler, *MockMachineRepository, *MockLicenseRepository, *MockUserRepository) {
	mockMachineRepo := &MockMachineRepository{}
	mockLicenseRepo := &MockLicenseRepository{}
	mockUserRepo := &MockUserRepository{}
	handler := NewMachineHandler(mockMachineRepo, mockLicenseRepo, mockUserRepo)
	return handler, mockMachineRepo, mockLicenseRepo, mockUserRepo
}

func TestMachineHandler_CreateMachine(t *testing.T) {
	handler, mockMachineRepo, mockLicenseRepo, _ := setupMachineTestHandler()

	// Test data
	licenseID := uuid.New()
	policyID := uuid.New()

	license := &entities.License{
		ID:       licenseID,
		PolicyID: policyID,
		Key:      "test-license-key",
	}

	req := CreateMachineRequest{
		LicenseID:   licenseID.String(),
		Fingerprint: "test-fingerprint",
		Name:        "Test Machine",
		Hostname:    "test-host",
		Platform:    "linux",
		IP:          "*************",
		Cores:       func() *int { c := 8; return &c }(),
	}

	// Setup mocks
	mockLicenseRepo.On("GetByID", mock.Anything, licenseID).Return(license, nil)
	mockMachineRepo.On("Create", mock.Anything, mock.AnythingOfType("*entities.Machine")).Return(nil)

	// Setup Gin
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)

	// Create request
	reqBody, _ := json.Marshal(req)
	c.Request = httptest.NewRequest("POST", "/machines", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	// Execute
	handler.CreateMachine(c)

	// Assert
	assert.Equal(t, http.StatusCreated, w.Code)

	var response MachineResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, licenseID.String(), response.LicenseID)
	assert.Equal(t, policyID.String(), response.PolicyID)
	assert.Equal(t, "test-fingerprint", response.Fingerprint)
	assert.Equal(t, "Test Machine", *response.Name)

	mockLicenseRepo.AssertExpectations(t)
	mockMachineRepo.AssertExpectations(t)
}

func TestMachineHandler_GetMachine(t *testing.T) {
	handler, mockMachineRepo, _, _ := setupMachineTestHandler()

	// Test data
	machineID := uuid.New()
	licenseID := uuid.New()
	policyID := uuid.New()

	machine := &entities.Machine{
		ID:          machineID,
		LicenseID:   licenseID,
		PolicyID:    policyID,
		Fingerprint: "test-fingerprint",
		Name:        func() *string { s := "Test Machine"; return &s }(),
		Status:      entities.MachineStatusActive,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Setup mocks
	mockMachineRepo.On("GetByID", mock.Anything, machineID).Return(machine, nil)

	// Setup Gin
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = gin.Params{{Key: "id", Value: machineID.String()}}
	c.Request = httptest.NewRequest("GET", "/machines/"+machineID.String(), nil)

	// Execute
	handler.GetMachine(c)

	// Assert
	assert.Equal(t, http.StatusOK, w.Code)

	var response MachineResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, machineID.String(), response.ID)
	assert.Equal(t, "test-fingerprint", response.Fingerprint)
	assert.Equal(t, "Test Machine", *response.Name)

	mockMachineRepo.AssertExpectations(t)
}

func TestMachineHandler_GetMachine_NotFound(t *testing.T) {
	handler, mockMachineRepo, _, _ := setupMachineTestHandler()

	// Test data
	machineID := uuid.New()

	// Setup mocks
	mockMachineRepo.On("GetByID", mock.Anything, machineID).Return(nil, gorm.ErrRecordNotFound)

	// Setup Gin
	gin.SetMode(gin.TestMode)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = gin.Params{{Key: "id", Value: machineID.String()}}
	c.Request = httptest.NewRequest("GET", "/machines/"+machineID.String(), nil)

	// Execute
	handler.GetMachine(c)

	// Assert
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "MACHINE_NOT_FOUND", response.Error.Code)

	mockMachineRepo.AssertExpectations(t)
}
