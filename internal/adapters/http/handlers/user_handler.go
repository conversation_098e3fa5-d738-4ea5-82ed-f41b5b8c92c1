package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// UserHandler handles HTTP requests for user operations
type UserHandler struct {
	userRepo repositories.UserRepository
}

// NewUserHandler creates a new user handler
func NewUserHandler(
	userRepo repositories.UserRepository,
) *UserHandler {
	return &UserHandler{
		userRepo: userRepo,
	}
}

// Request types - simple, flat Go structs
type CreateUserRequest struct {
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=8"`
	FirstName string `json:"firstName,omitempty"`
	LastName  string `json:"lastName,omitempty"`

	// Optional fields
	Status string `json:"status,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

type UpdateUserRequest struct {
	Email     string `json:"email,omitempty"`
	Password  string `json:"password,omitempty"`
	FirstName string `json:"firstName,omitempty"`
	LastName  string `json:"lastName,omitempty"`

	// Optional fields
	Status      string `json:"status,omitempty"`
	TOTPEnabled *bool  `json:"totpEnabled,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

// Response types - simple, flat Go structs
type UserResponse struct {
	ID        string `json:"id"`
	Email     string `json:"email"`
	FirstName string `json:"firstName,omitempty"`
	LastName  string `json:"lastName,omitempty"`
	FullName  string `json:"fullName,omitempty"`

	// Status and state
	Status      string     `json:"status"`
	TOTPEnabled bool       `json:"totpEnabled"`
	BannedAt    *time.Time `json:"bannedAt,omitempty"`
	LastLogin   *time.Time `json:"lastLogin,omitempty"`

	// Computed fields
	IsActive bool `json:"isActive"`
	IsBanned bool `json:"isBanned"`
	Has2FA   bool `json:"has2FA"`

	// Metadata
	Metadata map[string]any `json:"metadata"`

	// Timestamps
	CreatedAt string `json:"createdAt"`
	UpdatedAt string `json:"updatedAt"`
}

type UserListResponse struct {
	Users []UserResponse `json:"users"`
	Total int64          `json:"total"`
	Page  int            `json:"page"`
	Limit int            `json:"limit"`
}

// GetUsers handles GET /users - list users with pagination
func (h *UserHandler) GetUsers(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "25"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 25
	}

	filter := repositories.ListFilter{
		PageSize:  limit,
		Page:      page,
		SortBy:    c.DefaultQuery("sortBy", "created_at"),
		SortOrder: c.DefaultQuery("sortOrder", "DESC"),
		Search:    c.Query("search"),
	}

	// Fetch users from repository
	users, total, err := h.userRepo.List(c.Request.Context(), filter)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch users")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch users",
			},
		})
		return
	}

	// Convert to response format
	userResponses := make([]UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = h.entityToResponse(user)
	}

	c.JSON(http.StatusOK, UserListResponse{
		Users: userResponses,
		Total: total,
		Page:  page,
		Limit: limit,
	})
}

// GetUser handles GET /users/:id - get single user
func (h *UserHandler) GetUser(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_USER_ID",
				Message: "Invalid user ID format",
			},
		})
		return
	}

	user, err := h.userRepo.GetByID(c.Request.Context(), userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "USER_NOT_FOUND",
					Message: "User not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to fetch user")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch user",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(user))
}

// CreateUser handles POST /users - create new user
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Check if email already exists
	existingUser, err := h.userRepo.GetByEmail(c.Request.Context(), req.Email)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Error().Err(err).Str("email", req.Email).Msg("Failed to check user email")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to validate user email",
			},
		})
		return
	}
	if existingUser != nil {
		c.JSON(http.StatusConflict, ErrorResponse{
			Error: ErrorDetail{
				Code:    "USER_EMAIL_EXISTS",
				Message: "User with this email already exists",
			},
		})
		return
	}

	// Create user entity
	user, err := h.requestToEntity(&req)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create user entity")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to process user data",
			},
		})
		return
	}

	// Save to database
	if err := h.userRepo.Create(c.Request.Context(), user); err != nil {
		log.Error().Err(err).Msg("Failed to create user")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to create user",
			},
		})
		return
	}

	c.JSON(http.StatusCreated, h.entityToResponse(user))
}

// UpdateUser handles PUT /users/:id - update user
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_USER_ID",
				Message: "Invalid user ID format",
			},
		})
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Fetch existing user
	user, err := h.userRepo.GetByID(c.Request.Context(), userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "USER_NOT_FOUND",
					Message: "User not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to fetch user")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch user",
			},
		})
		return
	}

	// If email is being updated, check for conflicts
	if req.Email != "" && req.Email != user.Email {
		existingUser, err := h.userRepo.GetByEmail(c.Request.Context(), req.Email)
		if err != nil && err != gorm.ErrRecordNotFound {
			log.Error().Err(err).Str("email", req.Email).Msg("Failed to check user email")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to validate user email",
				},
			})
			return
		}
		if existingUser != nil {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error: ErrorDetail{
					Code:    "USER_EMAIL_EXISTS",
					Message: "User with this email already exists",
				},
			})
			return
		}
	}

	// Update user fields
	if err := h.updateEntityFromRequest(user, &req); err != nil {
		log.Error().Err(err).Msg("Failed to update user entity")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to process user data",
			},
		})
		return
	}

	// Save to database
	if err := h.userRepo.Update(c.Request.Context(), user); err != nil {
		log.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to update user")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to update user",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(user))
}

// DeleteUser handles DELETE /users/:id - delete user
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_USER_ID",
				Message: "Invalid user ID format",
			},
		})
		return
	}

	// Check if user exists
	_, err = h.userRepo.GetByID(c.Request.Context(), userID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "USER_NOT_FOUND",
					Message: "User not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to fetch user")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch user",
			},
		})
		return
	}

	// Soft delete the user
	if err := h.userRepo.SoftDelete(c.Request.Context(), userID); err != nil {
		log.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to delete user")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to delete user",
			},
		})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// Helper methods for entity/request/response conversions

// requestToEntity converts CreateUserRequest to User entity
func (h *UserHandler) requestToEntity(req *CreateUserRequest) (*entities.User, error) {
	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	user := &entities.User{
		Email:    req.Email,
		Password: string(hashedPassword),
	}

	// Set optional name fields
	if req.FirstName != "" {
		user.FirstName = &req.FirstName
	}
	if req.LastName != "" {
		user.LastName = &req.LastName
	}

	// Set user status with default
	if req.Status != "" {
		// Validate user status
		if req.Status == entities.UserStatusActive ||
			req.Status == entities.UserStatusInactive ||
			req.Status == entities.UserStatusSuspended {
			user.Status = req.Status
		} else {
			user.Status = entities.UserStatusActive // Default
		}
	} else {
		user.Status = entities.UserStatusActive // Default
	}

	// Set metadata
	if req.Metadata != nil {
		user.Metadata = entities.Metadata(req.Metadata)
	} else {
		user.Metadata = make(entities.Metadata)
	}

	return user, nil
}

// updateEntityFromRequest updates User entity from UpdateUserRequest
func (h *UserHandler) updateEntityFromRequest(user *entities.User, req *UpdateUserRequest) error {
	if req.Email != "" {
		user.Email = req.Email
	}

	// Update password if provided
	if req.Password != "" {
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
		if err != nil {
			return err
		}
		user.Password = string(hashedPassword)
	}

	// Update name fields
	if req.FirstName != "" {
		user.FirstName = &req.FirstName
	}
	if req.LastName != "" {
		user.LastName = &req.LastName
	}

	// Update user status with validation
	if req.Status != "" {
		if req.Status == entities.UserStatusActive ||
			req.Status == entities.UserStatusInactive ||
			req.Status == entities.UserStatusSuspended {
			user.Status = req.Status
		}
	}

	// Update TOTP enabled flag
	if req.TOTPEnabled != nil {
		user.TOTPEnabled = *req.TOTPEnabled
	}

	// Update metadata
	if req.Metadata != nil {
		user.Metadata = entities.Metadata(req.Metadata)
	}

	return nil
}

// entityToResponse converts User entity to UserResponse (excluding sensitive fields)
func (h *UserHandler) entityToResponse(user *entities.User) UserResponse {
	response := UserResponse{
		ID:          user.ID.String(),
		Email:       user.Email,
		Status:      user.Status,
		TOTPEnabled: user.TOTPEnabled,
		BannedAt:    user.BannedAt,
		LastLogin:   user.LastLogin,
		CreatedAt:   user.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   user.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Set optional name fields
	if user.FirstName != nil {
		response.FirstName = *user.FirstName
	}
	if user.LastName != nil {
		response.LastName = *user.LastName
	}

	// Set full name using entity method
	response.FullName = user.GetFullName()

	// Set computed fields using entity methods
	response.IsActive = user.IsActive()
	response.IsBanned = user.IsBanned()
	response.Has2FA = user.Has2FAEnabled()

	// Set metadata
	if user.Metadata != nil {
		response.Metadata = map[string]any(user.Metadata)
	} else {
		response.Metadata = make(map[string]any)
	}

	return response
}
