package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/middleware"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// Auth<PERSON>andler handles authentication-related HTTP requests
type AuthHandler struct {
	userRepo         repositories.UserRepository
	sessionRepo      repositories.SessionRepository
	usersOrgRepo     repositories.UsersOrganizationRepository
	organizationRepo repositories.OrganizationRepository
	permissionRepo   repositories.PermissionRepository
	authService      *auth.AuthService
}

// NewAuthHandler creates a new auth handler
func <PERSON>AuthHandler(
	userRepo repositories.UserRepository,
	sessionRepo repositories.SessionRepository,
	usersOrgRepo repositories.UsersOrganizationRepository,
	organizationRepo repositories.OrganizationRepository,
	permissionRepo repositories.PermissionRepository,
	authService *auth.AuthService,
) *AuthHandler {
	return &AuthHandler{
		userRepo:         userRepo,
		sessionRepo:      sessionRepo,
		usersOrgRepo:     usersOrgRepo,
		organizationRepo: organizationRepo,
		permissionRepo:   permissionRepo,
		authService:      authService,
	}
}

// Request/Response types
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	User         UserResponse `json:"user"`
	AccessToken  string       `json:"accessToken"`
	RefreshToken string       `json:"refreshToken,omitempty"`
	ExpiresIn    int64        `json:"expiresIn"`
	TokenType    string       `json:"tokenType"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken" binding:"required"`
}

type GenerateAPITokenRequest struct {
	Name        string   `json:"name" binding:"required"`
	Permissions []string `json:"permissions"`
	ExpiresIn   *int64   `json:"expiresIn,omitempty"` // seconds
}

type APITokenResponse struct {
	ID          string                    `json:"id"`
	Name        string                    `json:"name"`
	Token       string                    `json:"token,omitempty"` // Only returned on creation
	Scopes      []entities.APITokenScope  `json:"scopes"`
	Active      bool                      `json:"active"`
	ExpiresAt   *string                   `json:"expiresAt,omitempty"`
	CreatedAt   string                    `json:"createdAt"`
	LastUsedAt  *string                   `json:"lastUsedAt,omitempty"`
}

type APITokenListResponse struct {
	Tokens []APITokenResponse `json:"tokens"`
	Total  int64              `json:"total"`
	Page   int                `json:"page"`
	Limit  int                `json:"limit"`
}

// Login handles POST /auth/login
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Find user by email
	user, err := h.userRepo.GetByEmail(c.Request.Context(), req.Email)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INVALID_CREDENTIALS",
					Message: "Invalid email or password",
				},
			})
			return
		}
		log.Error().Err(err).Str("email", req.Email).Msg("Failed to fetch user")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Authentication failed",
			},
		})
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_CREDENTIALS",
				Message: "Invalid email or password",
			},
		})
		return
	}

	// Check if user is active
	if !user.IsActive() {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "USER_INACTIVE",
				Message: "User account is inactive",
			},
		})
		return
	}

	if user.IsBanned() {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "USER_BANNED",
				Message: "User account is banned",
			},
		})
		return
	}

	// Create session
	clientIP := c.ClientIP()
	userAgent := c.Request.UserAgent()
	session := &entities.Session{
		ID:        uuid.New(),
		UserID:    user.ID,
		TokenHash: h.generateTokenHash(),
		IP:        &clientIP,
		UserAgent: &userAgent,
		ExpiresAt: time.Now().Add(24 * time.Hour), // 24 hour session
		CreatedAt: time.Now(),
	}

	if err := h.sessionRepo.Create(c.Request.Context(), session); err != nil {
		log.Error().Err(err).Msg("Failed to create session")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to create session",
			},
		})
		return
	}

	// Get user's default organization if available
	var orgID string
	userOrgs, err := h.usersOrgRepo.GetUserOrganizations(c.Request.Context(), user.ID.String())
	if err == nil && len(userOrgs) > 0 {
		// Use first organization as default
		orgID = userOrgs[0].OrganizationID.String()
	}

	// Generate JWT access token
	accessToken, err := h.authService.GenerateJWT(user.ID.String(), session.ID.String(), orgID)
	if err != nil {
		log.Error().Err(err).Msg("Failed to generate JWT token")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to generate access token",
			},
		})
		return
	}

	// Generate refresh token
	refreshToken, err := h.authService.GenerateRefreshToken(user.ID.String(), session.ID.String())
	if err != nil {
		log.Error().Err(err).Msg("Failed to generate refresh token")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to generate refresh token",
			},
		})
		return
	}

	// Update user last login
	now := time.Now()
	user.LastLogin = &now
	h.userRepo.Update(c.Request.Context(), user)

	// Set session cookie
	c.SetCookie("session_id", session.ID.String(), 86400, "/", "", false, true)

	// Convert user to response format
	userHandler := &UserHandler{userRepo: h.userRepo}
	userResponse := userHandler.entityToResponse(user)

	c.JSON(http.StatusOK, LoginResponse{
		User:         userResponse,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    900, // 15 minutes in seconds (matching JWT expiration)
		TokenType:    "Bearer",
	})
}

// Logout handles POST /auth/logout
func (h *AuthHandler) Logout(c *gin.Context) {
	authCtx, exists := middleware.GetAuthContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "AUTHENTICATION_REQUIRED",
				Message: "Authentication is required",
			},
		})
		return
	}

	// If session exists, delete it
	if authCtx.Session != nil {
		if err := h.sessionRepo.Delete(c.Request.Context(), authCtx.Session.ID); err != nil {
			log.Error().Err(err).Str("session_id", authCtx.Session.ID.String()).Msg("Failed to delete session")
		}
	}

	// Clear session cookie
	c.SetCookie("session_id", "", -1, "/", "", false, true)

	c.JSON(http.StatusOK, gin.H{
		"message": "Successfully logged out",
	})
}

// RefreshToken handles POST /auth/refresh
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Refresh the JWT token
	newToken, err := h.authService.RefreshAccessToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REFRESH_TOKEN",
				Message: "Invalid or expired refresh token",
			},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"accessToken": newToken,
		"expiresIn":   900, // 15 minutes in seconds
		"tokenType":   "Bearer",
	})
}

// GetProfile handles GET /auth/profile
func (h *AuthHandler) GetProfile(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "AUTHENTICATION_REQUIRED",
				Message: "Authentication is required",
			},
		})
		return
	}

	// Convert user to response format
	userHandler := &UserHandler{userRepo: h.userRepo}
	userResponse := userHandler.entityToResponse(user)

	c.JSON(http.StatusOK, userResponse)
}

// generateTokenHash generates a secure random token hash
func (h *AuthHandler) generateTokenHash() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// GenerateAPIToken handles POST /auth/tokens
func (h *AuthHandler) GenerateAPIToken(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "AUTHENTICATION_REQUIRED",
				Message: "Authentication is required",
			},
		})
		return
	}

	var req GenerateAPITokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Convert permissions to APITokenScope format
	scopes := make([]entities.APITokenScope, 0, len(req.Permissions))
	for _, perm := range req.Permissions {
		// Parse permission string (e.g., "license:read" -> scope="R", resource="license", action="read")
		parts := strings.Split(perm, ":")
		if len(parts) >= 2 {
			scope := entities.APITokenScope{
				Scope:        entities.ScopeResource, // Default to resource scope
				ResourceType: parts[0],
				Actions:      []string{parts[1]},
			}
			scopes = append(scopes, scope)
		}
	}

	// Set default expiration if not provided (30 days)
	var expiresAt *time.Time
	if req.ExpiresIn != nil {
		expires := time.Now().Add(time.Duration(*req.ExpiresIn) * time.Second)
		expiresAt = &expires
	} else {
		expires := time.Now().Add(30 * 24 * time.Hour)
		expiresAt = &expires
	}

	// Get user's organization if available
	authCtx, _ := middleware.GetAuthContext(c)
	var orgID *uuid.UUID
	if authCtx != nil && authCtx.CurrentOrganizationID != nil {
		orgUUID, _ := uuid.Parse(*authCtx.CurrentOrganizationID)
		orgID = &orgUUID
	}

	// Create API token using the auth service
	createReq := &auth.CreateAPITokenRequest{
		UserID:         user.ID,
		OrganizationID: orgID,
		Name:           req.Name,
		Description:    "", // Could be added to request
		Scopes:         scopes,
		ExpiresAt:      expiresAt,
		CreatedByIP:    c.ClientIP(),
		UserAgent:      c.GetHeader("User-Agent"),
	}

	apiToken, tokenString, err := h.authService.CreateAPIToken(c.Request.Context(), createReq)
	if err != nil {
		log.Error().Err(err).Msg("Failed to create API token")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to create API token",
			},
		})
		return
	}

	// Convert to response format
	response := h.entityToResponse(apiToken)
	response.Token = tokenString // Include the actual token only on creation

	c.JSON(http.StatusCreated, response)
}

// GetAPITokens handles GET /auth/tokens
func (h *AuthHandler) GetAPITokens(c *gin.Context) {
	_, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "AUTHENTICATION_REQUIRED",
				Message: "Authentication is required",
			},
		})
		return
	}

	// For now, return empty list since we need APITokenRepository implementation
	c.JSON(http.StatusOK, APITokenListResponse{
		Tokens: []APITokenResponse{},
		Total:  0,
		Page:   1,
		Limit:  25,
	})
}

// RevokeAPIToken handles DELETE /auth/tokens/:id
func (h *AuthHandler) RevokeAPIToken(c *gin.Context) {
	_, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error: ErrorDetail{
				Code:    "AUTHENTICATION_REQUIRED",
				Message: "Authentication is required",
			},
		})
		return
	}

	tokenID := c.Param("id")
	if tokenID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "MISSING_TOKEN_ID",
				Message: "Token ID is required",
			},
		})
		return
	}

	// Validate token ID format
	if _, err := uuid.Parse(tokenID); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_TOKEN_ID",
				Message: "Invalid token ID format",
			},
		})
		return
	}

	// For now, just return success since we need APITokenRepository implementation
	c.JSON(http.StatusOK, gin.H{
		"message": "API token revoked successfully",
	})
}

// entityToResponse converts APIToken entity to response format
func (h *AuthHandler) entityToResponse(token *entities.APIToken) APITokenResponse {
	response := APITokenResponse{
		ID:          token.ID.String(),
		Name:        token.Name,
		Scopes:      token.Scopes,
		Active:      token.Active,
		CreatedAt:   token.CreatedAt.Format(time.RFC3339),
	}

	if token.ExpiresAt != nil {
		expiresAt := token.ExpiresAt.Format(time.RFC3339)
		response.ExpiresAt = &expiresAt
	}

	if token.LastUsedAt != nil {
		lastUsedAt := token.LastUsedAt.Format(time.RFC3339)
		response.LastUsedAt = &lastUsedAt
	}

	return response
}
