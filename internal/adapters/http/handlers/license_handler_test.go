package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// Mock repositories are now defined in mocks_test.go

func setupLicenseHandler() (*LicenseHandler, *MockLicenseRepository, *MockPolicyRepository, *MockOrganizationRepository, *MockUserRepository) {
	mockLicenseRepo := &MockLicenseRepository{}
	mockPolicyRepo := &MockPolicyRepository{}
	mockOrgRepo := &MockOrganizationRepository{}
	mockUserRepo := &MockUserRepository{}
	mockCryptoService := &crypto.CryptoService{}

	handler := NewLicenseHandler(mockLicenseRepo, mockPolicyRepo, mockOrgRepo, mockUserRepo, mockCryptoService)
	return handler, mockLicenseRepo, mockPolicyRepo, mockOrgRepo, mockUserRepo
}

func TestLicenseHandler_GetLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	license := &entities.License{
		ID:             licenseID,
		OrganizationID: uuid.New(),
		PolicyID:       uuid.New(),
		ProductID:      uuid.New(),
		Key:            "TEST-1234-5678-9ABC",
		Status:         entities.LicenseStatusActive,
		Uses:           5,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, licenseID).Return(license, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("GET", "/licenses/"+licenseID.String(), nil)

	handler.GetLicense(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestLicenseHandler_GetLicense_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, licenseID).Return(nil, gorm.ErrRecordNotFound)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("GET", "/licenses/"+licenseID.String(), nil)

	handler.GetLicense(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestLicenseHandler_GetLicenses(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenses := []*entities.License{
		{
			ID:             uuid.New(),
			OrganizationID: uuid.New(),
			PolicyID:       uuid.New(),
			ProductID:      uuid.New(),
			Key:            "TEST-1234-5678-9ABC",
			Status:         entities.LicenseStatusActive,
			Uses:           5,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
		{
			ID:             uuid.New(),
			OrganizationID: uuid.New(),
			PolicyID:       uuid.New(),
			ProductID:      uuid.New(),
			Key:            "TEST-ABCD-EFGH-IJKL",
			Status:         entities.LicenseStatusActive,
			Uses:           3,
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
	}

	mockRepo.On("List", mock.Anything, mock.AnythingOfType("repositories.ListFilter")).Return(licenses, int64(2), nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/licenses", nil)

	handler.GetLicenses(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)

	var response LicenseListResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Len(t, response.Licenses, 2)
	assert.Equal(t, int64(2), response.Total)
}

func TestLicenseHandler_CreateLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockLicenseRepo, mockPolicyRepo, mockOrgRepo, mockUserRepo := setupLicenseHandler()

	policyID := uuid.New()
	organizationID := uuid.New()
	userID := uuid.New()

	policy := &entities.Policy{
		ID:             policyID,
		OrganizationID: organizationID,
		ProductID:      uuid.New(),
		Name:           "Test Policy",
		Duration:       nil,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	organization := &entities.Organization{
		ID:        organizationID,
		Name:      "Test Organization",
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	firstName := "Test"
	lastName := "User"
	user := &entities.User{
		ID:        userID,
		Email:     "<EMAIL>",
		FirstName: &firstName,
		LastName:  &lastName,
		Status:    entities.UserStatusActive,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	mockPolicyRepo.On("GetByID", mock.Anything, policyID).Return(policy, nil)
	mockOrgRepo.On("GetByID", mock.Anything, organizationID).Return(organization, nil)
	mockUserRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
	mockLicenseRepo.On("Create", mock.Anything, mock.AnythingOfType("*entities.License")).Return(nil)

	reqBody := CreateLicenseRequest{
		PolicyID:       policyID.String(),
		OrganizationID: organizationID.String(),
		Name:           "Test License",
		OwnerType:      "user",
		OwnerID:        userID.String(),
	}

	body, _ := json.Marshal(reqBody)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/licenses", bytes.NewBuffer(body))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateLicense(c)

	assert.Equal(t, http.StatusCreated, w.Code)
	mockLicenseRepo.AssertExpectations(t)
	mockPolicyRepo.AssertExpectations(t)
	mockOrgRepo.AssertExpectations(t)
	mockUserRepo.AssertExpectations(t)
}

func TestLicenseHandler_UpdateLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	originalName := "Original Name"
	license := &entities.License{
		ID:             licenseID,
		OrganizationID: uuid.New(),
		PolicyID:       uuid.New(),
		ProductID:      uuid.New(),
		Key:            "TEST-1234-5678-9ABC",
		Name:           &originalName,
		Status:         entities.LicenseStatusActive,
		Uses:           5,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, licenseID).Return(license, nil)
	mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*entities.License")).Return(nil)

	reqBody := UpdateLicenseRequest{
		Name: "Updated Name",
	}

	body, _ := json.Marshal(reqBody)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("PUT", "/licenses/"+licenseID.String(), bytes.NewBuffer(body))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateLicense(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestLicenseHandler_DeleteLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	license := &entities.License{
		ID:             licenseID,
		OrganizationID: uuid.New(),
		PolicyID:       uuid.New(),
		ProductID:      uuid.New(),
		Key:            "TEST-1234-5678-9ABC",
		Status:         entities.LicenseStatusActive,
		Uses:           5,
		Protected:      &[]bool{false}[0], // Explicitly set to false to avoid nil pointer
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, licenseID).Return(license, nil)
	mockRepo.On("SoftDelete", mock.Anything, licenseID).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/licenses/"+licenseID.String(), nil)

	handler.DeleteLicense(c)

	assert.Equal(t, http.StatusNoContent, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestLicenseHandler_SuspendLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	license := &entities.License{
		ID:             licenseID,
		OrganizationID: uuid.New(),
		PolicyID:       uuid.New(),
		ProductID:      uuid.New(),
		Key:            "TEST-1234-5678-9ABC",
		Status:         entities.LicenseStatusActive,
		Suspended:      false,
		Uses:           5,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, licenseID).Return(license, nil)
	mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*entities.License")).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("POST", "/licenses/"+licenseID.String()+"/actions/suspend", nil)

	handler.SuspendLicense(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestLicenseHandler_ReinstateLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	license := &entities.License{
		ID:             licenseID,
		OrganizationID: uuid.New(),
		PolicyID:       uuid.New(),
		ProductID:      uuid.New(),
		Key:            "TEST-1234-5678-9ABC",
		Status:         entities.LicenseStatusActive,
		Suspended:      true,
		Uses:           5,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, licenseID).Return(license, nil)
	mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*entities.License")).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("POST", "/licenses/"+licenseID.String()+"/actions/reinstate", nil)

	handler.ReinstateLicense(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestLicenseHandler_TransferLicense(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockLicenseRepo, mockPolicyRepo, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	oldPolicyID := uuid.New()
	newPolicyID := uuid.New()

	license := &entities.License{
		ID:             licenseID,
		OrganizationID: uuid.New(),
		PolicyID:       oldPolicyID,
		ProductID:      uuid.New(),
		Key:            "TEST-1234-5678-9ABC",
		Status:         entities.LicenseStatusActive,
		Uses:           5,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	newPolicy := &entities.Policy{
		ID:             newPolicyID,
		OrganizationID: license.OrganizationID,
		ProductID:      license.ProductID,
		Name:           "New Policy",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	mockLicenseRepo.On("GetByID", mock.Anything, licenseID).Return(license, nil)
	mockPolicyRepo.On("GetByID", mock.Anything, newPolicyID).Return(newPolicy, nil)
	mockLicenseRepo.On("Update", mock.Anything, mock.AnythingOfType("*entities.License")).Return(nil)

	reqBody := TransferLicenseRequest{
		PolicyID: newPolicyID.String(),
	}

	body, _ := json.Marshal(reqBody)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("POST", "/licenses/"+licenseID.String()+"/actions/transfer", bytes.NewBuffer(body))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.TransferLicense(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockLicenseRepo.AssertExpectations(t)
	mockPolicyRepo.AssertExpectations(t)
}

func TestLicenseHandler_GenerateLicenseKey(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _, _, _, _ := setupLicenseHandler()

	key, err := handler.generateLicenseKey()
	assert.NoError(t, err)
	assert.NotEmpty(t, key)
	assert.Contains(t, key, "-")

	// Should be in format XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
	parts := strings.Split(key, "-")
	assert.Len(t, parts, 8)
	for _, part := range parts {
		assert.Len(t, part, 4)
	}
}

// Additional error handling tests

func TestLicenseHandler_GetLicense_InvalidID(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _, _, _, _ := setupLicenseHandler()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: "invalid-uuid"}}
	c.Request = httptest.NewRequest("GET", "/licenses/invalid-uuid", nil)

	handler.GetLicense(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestLicenseHandler_DeleteLicense_Protected(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	license := &entities.License{
		ID:             licenseID,
		OrganizationID: uuid.New(),
		PolicyID:       uuid.New(),
		ProductID:      uuid.New(),
		Key:            "TEST-1234-5678-9ABC",
		Status:         entities.LicenseStatusActive,
		Protected:      &[]bool{true}[0], // Protected license
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, licenseID).Return(license, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/licenses/"+licenseID.String(), nil)

	handler.DeleteLicense(c)

	assert.Equal(t, http.StatusUnprocessableEntity, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestLicenseHandler_CreateLicense_InvalidRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _, _, _, _ := setupLicenseHandler()

	// Test with invalid JSON
	invalidJSON := `{"policyId": "invalid-uuid"}`
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/licenses", bytes.NewBufferString(invalidJSON))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateLicense(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestLicenseHandler_UpdateLicense_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _, _, _ := setupLicenseHandler()

	licenseID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, licenseID).Return(nil, gorm.ErrRecordNotFound)

	updateReq := UpdateLicenseRequest{
		Name: "Updated License",
	}
	reqBody, _ := json.Marshal(updateReq)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: licenseID.String()}}
	c.Request = httptest.NewRequest("PUT", "/licenses/"+licenseID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateLicense(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}
