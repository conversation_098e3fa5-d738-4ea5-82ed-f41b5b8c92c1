package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ProductHandler handles HTTP requests for product operations
type ProductHandler struct {
	productRepo      repositories.ProductRepository
	organizationRepo repositories.OrganizationRepository
}

// NewProductHandler creates a new product handler
func NewProductHandler(
	productRepo repositories.ProductRepository,
	organizationRepo repositories.OrganizationRepository,
) *ProductHandler {
	return &ProductHandler{
		productRepo:      productRepo,
		organizationRepo: organizationRepo,
	}
}

// Request types - simple, flat Go structs
type CreateProductRequest struct {
	OrganizationID string `json:"organizationId" binding:"required"`
	Name           string `json:"name" binding:"required"`
	Code           string `json:"code" binding:"required"`

	// Optional fields
	Description          string   `json:"description,omitempty"`
	URL                  string   `json:"url,omitempty"`
	Platforms            []string `json:"platforms,omitempty"`
	DistributionStrategy string   `json:"distributionStrategy,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

type UpdateProductRequest struct {
	// Product identification
	Name string `json:"name,omitempty"`
	Code string `json:"code,omitempty"`

	// Optional fields
	Description          string   `json:"description,omitempty"`
	URL                  string   `json:"url,omitempty"`
	Platforms            []string `json:"platforms,omitempty"`
	DistributionStrategy string   `json:"distributionStrategy,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

// Response types - simple, flat Go structs
type ProductResponse struct {
	ID                   string         `json:"id"`
	OrganizationID       string         `json:"organizationId"`
	Name                 string         `json:"name"`
	Code                 string         `json:"code"`
	Description          string         `json:"description,omitempty"`
	URL                  string         `json:"url,omitempty"`
	Platforms            []string       `json:"platforms"`
	DistributionStrategy string         `json:"distributionStrategy,omitempty"`
	Metadata             map[string]any `json:"metadata"`
	CreatedAt            string         `json:"createdAt"`
	UpdatedAt            string         `json:"updatedAt"`
}

type ProductListResponse struct {
	Products   []ProductResponse `json:"products"`
	TotalCount int64             `json:"totalCount"`
	Page       int               `json:"page"`
	PageSize   int               `json:"pageSize"`
}

// GetProducts handles GET /products - list products with pagination
func (h *ProductHandler) GetProducts(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("pageSize", "20"))

	// Parse optional organization filter
	var organizationID *uuid.UUID
	if orgIDStr := c.Query("organizationId"); orgIDStr != "" {
		if orgID, err := uuid.Parse(orgIDStr); err == nil {
			organizationID = &orgID
		}
	}

	filter := repositories.ListFilter{
		Page:           page,
		PageSize:       pageSize,
		SortBy:         c.DefaultQuery("sortBy", "created_at"),
		SortOrder:      c.DefaultQuery("sortOrder", "DESC"),
		Search:         c.Query("search"),
		OrganizationID: organizationID,
	}

	products, totalCount, err := h.productRepo.List(c.Request.Context(), filter)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch products")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch products",
			},
		})
		return
	}

	// Convert to response format
	productResponses := make([]ProductResponse, len(products))
	for i, product := range products {
		productResponses[i] = h.entityToResponse(product)
	}

	c.JSON(http.StatusOK, ProductListResponse{
		Products:   productResponses,
		TotalCount: totalCount,
		Page:       page,
		PageSize:   pageSize,
	})
}

// GetProduct handles GET /products/:id - get single product
func (h *ProductHandler) GetProduct(c *gin.Context) {
	productID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_PRODUCT_ID",
				Message: "Invalid product ID format",
			},
		})
		return
	}

	product, err := h.productRepo.GetByID(c.Request.Context(), productID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "PRODUCT_NOT_FOUND",
					Message: "Product not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("product_id", productID.String()).Msg("Failed to fetch product")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch product",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(product))
}

// CreateProduct handles POST /products - create new product
func (h *ProductHandler) CreateProduct(c *gin.Context) {
	var req CreateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Parse organization ID
	organizationID, err := uuid.Parse(req.OrganizationID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_ORGANIZATION_ID",
				Message: "Invalid organization ID format",
			},
		})
		return
	}

	// Verify organization exists
	_, err = h.organizationRepo.GetByID(c.Request.Context(), organizationID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "ORGANIZATION_NOT_FOUND",
					Message: "Organization not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("organization_id", organizationID.String()).Msg("Failed to fetch organization")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to verify organization",
			},
		})
		return
	}

	// Check if product code already exists for this organization
	existingProduct, err := h.productRepo.GetByCode(c.Request.Context(), req.Code, organizationID)
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Error().Err(err).Str("code", req.Code).Str("organization_id", organizationID.String()).Msg("Failed to check product code")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to validate product code",
			},
		})
		return
	}
	if existingProduct != nil {
		c.JSON(http.StatusConflict, ErrorResponse{
			Error: ErrorDetail{
				Code:    "PRODUCT_CODE_EXISTS",
				Message: "Product with this code already exists in the organization",
			},
		})
		return
	}

	// Create product entity
	product := h.requestToEntity(&req)

	// Save to database
	if err := h.productRepo.Create(c.Request.Context(), product); err != nil {
		log.Error().Err(err).Msg("Failed to create product")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to create product",
			},
		})
		return
	}

	c.JSON(http.StatusCreated, h.entityToResponse(product))
}

// UpdateProduct handles PUT /products/:id - update product
func (h *ProductHandler) UpdateProduct(c *gin.Context) {
	productID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_PRODUCT_ID",
				Message: "Invalid product ID format",
			},
		})
		return
	}

	var req UpdateProductRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: "Invalid request format: " + err.Error(),
			},
		})
		return
	}

	// Fetch existing product
	product, err := h.productRepo.GetByID(c.Request.Context(), productID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "PRODUCT_NOT_FOUND",
					Message: "Product not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("product_id", productID.String()).Msg("Failed to fetch product")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch product",
			},
		})
		return
	}

	// If code is being updated, check for conflicts
	if req.Code != "" && req.Code != product.Code {
		existingProduct, err := h.productRepo.GetByCode(c.Request.Context(), req.Code, product.OrganizationID)
		if err != nil && err != gorm.ErrRecordNotFound {
			log.Error().Err(err).Str("code", req.Code).Str("organization_id", product.OrganizationID.String()).Msg("Failed to check product code")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to validate product code",
				},
			})
			return
		}
		if existingProduct != nil {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error: ErrorDetail{
					Code:    "PRODUCT_CODE_EXISTS",
					Message: "Product with this code already exists in the organization",
				},
			})
			return
		}
	}

	// Update product fields
	h.updateEntityFromRequest(product, &req)

	// Save to database
	if err := h.productRepo.Update(c.Request.Context(), product); err != nil {
		log.Error().Err(err).Str("product_id", productID.String()).Msg("Failed to update product")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to update product",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(product))
}

// DeleteProduct handles DELETE /products/:id - delete product
func (h *ProductHandler) DeleteProduct(c *gin.Context) {
	productID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_PRODUCT_ID",
				Message: "Invalid product ID format",
			},
		})
		return
	}

	// Check if product exists
	_, err = h.productRepo.GetByID(c.Request.Context(), productID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "PRODUCT_NOT_FOUND",
					Message: "Product not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("product_id", productID.String()).Msg("Failed to fetch product")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch product",
			},
		})
		return
	}

	// Soft delete the product
	if err := h.productRepo.SoftDelete(c.Request.Context(), productID); err != nil {
		log.Error().Err(err).Str("product_id", productID.String()).Msg("Failed to delete product")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to delete product",
			},
		})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// Helper methods for entity/request/response conversions

// requestToEntity converts CreateProductRequest to Product entity
func (h *ProductHandler) requestToEntity(req *CreateProductRequest) *entities.Product {
	organizationID, _ := uuid.Parse(req.OrganizationID)

	product := &entities.Product{
		OrganizationID: organizationID,
		Name:           req.Name,
		Code:           req.Code,
		Platforms:      entities.ProductPlatforms(req.Platforms),
		Metadata:       entities.Metadata(req.Metadata),
	}

	// Set optional fields
	if req.Description != "" {
		product.Description = &req.Description
	}
	if req.URL != "" {
		product.URL = &req.URL
	}
	if req.DistributionStrategy != "" {
		product.DistributionStrategy = &req.DistributionStrategy
	}

	// Set default distribution strategy if not provided
	if product.DistributionStrategy == nil {
		defaultStrategy := "LICENSED"
		product.DistributionStrategy = &defaultStrategy
	}

	return product
}

// updateEntityFromRequest updates Product entity from UpdateProductRequest
func (h *ProductHandler) updateEntityFromRequest(product *entities.Product, req *UpdateProductRequest) {
	if req.Name != "" {
		product.Name = req.Name
	}
	if req.Code != "" {
		product.Code = req.Code
	}
	if req.Description != "" {
		product.Description = &req.Description
	}
	if req.URL != "" {
		product.URL = &req.URL
	}
	if req.DistributionStrategy != "" {
		product.DistributionStrategy = &req.DistributionStrategy
	}
	if req.Platforms != nil {
		product.Platforms = entities.ProductPlatforms(req.Platforms)
	}
	if req.Metadata != nil {
		product.Metadata = entities.Metadata(req.Metadata)
	}
}

// entityToResponse converts Product entity to ProductResponse
func (h *ProductHandler) entityToResponse(product *entities.Product) ProductResponse {
	response := ProductResponse{
		ID:             product.ID.String(),
		OrganizationID: product.OrganizationID.String(),
		Name:           product.Name,
		Code:           product.Code,
		Platforms:      []string(product.Platforms),
		Metadata:       map[string]any(product.Metadata),
		CreatedAt:      product.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:      product.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Set optional fields
	if product.Description != nil {
		response.Description = *product.Description
	}
	if product.URL != nil {
		response.URL = *product.URL
	}
	if product.DistributionStrategy != nil {
		response.DistributionStrategy = *product.DistributionStrategy
	}

	// Ensure platforms is never nil
	if response.Platforms == nil {
		response.Platforms = []string{}
	}

	// Ensure metadata is never nil
	if response.Metadata == nil {
		response.Metadata = map[string]any{}
	}

	return response
}
