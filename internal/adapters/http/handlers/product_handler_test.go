package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// setupProductHandler creates a product handler with mock dependencies for testing
func setupProductHandler() (*ProductHandler, *MockProductRepository, *MockOrganizationRepository) {
	mockProductRepo := &MockProductRepository{}
	mockOrgRepo := &MockOrganizationRepository{}

	handler := NewProductHandler(mockProductRepo, mockOrgRepo)
	return handler, mockProductRepo, mockOrgRepo
}

func TestProductHandler_GetProduct(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _ := setupProductHandler()

	productID := uuid.New()
	product := &entities.Product{
		ID:                   productID,
		OrganizationID:       uuid.New(),
		Name:                 "Test Product",
		Code:                 "TEST-PRODUCT",
		Description:          &[]string{"Test Description"}[0],
		URL:                  &[]string{"https://example.com"}[0],
		Platforms:            entities.ProductPlatforms{"windows", "linux"},
		DistributionStrategy: &[]string{"LICENSED"}[0],
		Metadata:             entities.Metadata{"key": "value"},
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, productID).Return(product, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: productID.String()}}
	c.Request = httptest.NewRequest("GET", "/products/"+productID.String(), nil)

	handler.GetProduct(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestProductHandler_GetProduct_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _ := setupProductHandler()

	productID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, productID).Return(nil, gorm.ErrRecordNotFound)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: productID.String()}}
	c.Request = httptest.NewRequest("GET", "/products/"+productID.String(), nil)

	handler.GetProduct(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestProductHandler_GetProducts(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _ := setupProductHandler()

	products := []*entities.Product{
		{
			ID:             uuid.New(),
			OrganizationID: uuid.New(),
			Name:           "Product 1",
			Code:           "PROD-1",
			Platforms:      entities.ProductPlatforms{"windows"},
			Metadata:       entities.Metadata{},
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
		{
			ID:             uuid.New(),
			OrganizationID: uuid.New(),
			Name:           "Product 2",
			Code:           "PROD-2",
			Platforms:      entities.ProductPlatforms{"linux"},
			Metadata:       entities.Metadata{},
			CreatedAt:      time.Now(),
			UpdatedAt:      time.Now(),
		},
	}

	mockRepo.On("List", mock.Anything, mock.AnythingOfType("repositories.ListFilter")).Return(products, int64(2), nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/products", nil)

	handler.GetProducts(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestProductHandler_CreateProduct(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockProductRepo, mockOrgRepo := setupProductHandler()

	organizationID := uuid.New()
	organization := &entities.Organization{
		ID:   organizationID,
		Name: "Test Organization",
		Slug: "test-org",
	}

	createReq := CreateProductRequest{
		OrganizationID:       organizationID.String(),
		Name:                 "New Product",
		Code:                 "NEW-PRODUCT",
		Description:          "A new product",
		URL:                  "https://example.com",
		Platforms:            []string{"windows", "linux"},
		DistributionStrategy: "LICENSED",
		Metadata:             map[string]any{"key": "value"},
	}
	reqBody, _ := json.Marshal(createReq)

	mockOrgRepo.On("GetByID", mock.Anything, organizationID).Return(organization, nil)
	mockProductRepo.On("GetByCode", mock.Anything, "NEW-PRODUCT", organizationID).Return(nil, gorm.ErrRecordNotFound)
	mockProductRepo.On("Create", mock.Anything, mock.AnythingOfType("*entities.Product")).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/products", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateProduct(c)

	assert.Equal(t, http.StatusCreated, w.Code)
	mockProductRepo.AssertExpectations(t)
	mockOrgRepo.AssertExpectations(t)
}

func TestProductHandler_UpdateProduct(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _ := setupProductHandler()

	productID := uuid.New()
	product := &entities.Product{
		ID:             productID,
		OrganizationID: uuid.New(),
		Name:           "Original Product",
		Code:           "ORIG-PRODUCT",
		Platforms:      entities.ProductPlatforms{"windows"},
		Metadata:       entities.Metadata{},
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	updateReq := UpdateProductRequest{
		Name:        "Updated Product",
		Description: "Updated description",
		Platforms:   []string{"windows", "linux", "macos"},
	}
	reqBody, _ := json.Marshal(updateReq)

	mockRepo.On("GetByID", mock.Anything, productID).Return(product, nil)
	mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*entities.Product")).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: productID.String()}}
	c.Request = httptest.NewRequest("PUT", "/products/"+productID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateProduct(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestProductHandler_DeleteProduct(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _ := setupProductHandler()

	productID := uuid.New()
	product := &entities.Product{
		ID:             productID,
		OrganizationID: uuid.New(),
		Name:           "Product to Delete",
		Code:           "DELETE-PRODUCT",
		Platforms:      entities.ProductPlatforms{},
		Metadata:       entities.Metadata{},
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, productID).Return(product, nil)
	mockRepo.On("SoftDelete", mock.Anything, productID).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: productID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/products/"+productID.String(), nil)

	handler.DeleteProduct(c)

	assert.Equal(t, http.StatusNoContent, w.Code)
	mockRepo.AssertExpectations(t)
}

// Additional error handling tests

func TestProductHandler_GetProduct_InvalidID(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _, _ := setupProductHandler()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: "invalid-uuid"}}
	c.Request = httptest.NewRequest("GET", "/products/invalid-uuid", nil)

	handler.GetProduct(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestProductHandler_CreateProduct_InvalidRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _, _ := setupProductHandler()

	// Test with invalid JSON
	invalidJSON := `{"organizationId": "invalid-uuid"}`
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/products", bytes.NewBufferString(invalidJSON))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateProduct(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestProductHandler_CreateProduct_OrganizationNotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _, mockOrgRepo := setupProductHandler()

	organizationID := uuid.New()
	createReq := CreateProductRequest{
		OrganizationID: organizationID.String(),
		Name:           "New Product",
		Code:           "NEW-PRODUCT",
	}
	reqBody, _ := json.Marshal(createReq)

	mockOrgRepo.On("GetByID", mock.Anything, organizationID).Return(nil, gorm.ErrRecordNotFound)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/products", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateProduct(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockOrgRepo.AssertExpectations(t)
}

func TestProductHandler_CreateProduct_CodeExists(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockProductRepo, mockOrgRepo := setupProductHandler()

	organizationID := uuid.New()
	organization := &entities.Organization{
		ID:   organizationID,
		Name: "Test Organization",
		Slug: "test-org",
	}

	existingProduct := &entities.Product{
		ID:             uuid.New(),
		OrganizationID: organizationID,
		Name:           "Existing Product",
		Code:           "EXISTING-CODE",
	}

	createReq := CreateProductRequest{
		OrganizationID: organizationID.String(),
		Name:           "New Product",
		Code:           "EXISTING-CODE", // Same code as existing product
	}
	reqBody, _ := json.Marshal(createReq)

	mockOrgRepo.On("GetByID", mock.Anything, organizationID).Return(organization, nil)
	mockProductRepo.On("GetByCode", mock.Anything, "EXISTING-CODE", organizationID).Return(existingProduct, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/products", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateProduct(c)

	assert.Equal(t, http.StatusConflict, w.Code)
	mockProductRepo.AssertExpectations(t)
	mockOrgRepo.AssertExpectations(t)
}

func TestProductHandler_UpdateProduct_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _ := setupProductHandler()

	productID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, productID).Return(nil, gorm.ErrRecordNotFound)

	updateReq := UpdateProductRequest{
		Name: "Updated Product",
	}
	reqBody, _ := json.Marshal(updateReq)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: productID.String()}}
	c.Request = httptest.NewRequest("PUT", "/products/"+productID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateProduct(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestProductHandler_UpdateProduct_CodeConflict(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _ := setupProductHandler()

	productID := uuid.New()
	organizationID := uuid.New()

	product := &entities.Product{
		ID:             productID,
		OrganizationID: organizationID,
		Name:           "Original Product",
		Code:           "ORIG-CODE",
		Platforms:      entities.ProductPlatforms{},
		Metadata:       entities.Metadata{},
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	existingProduct := &entities.Product{
		ID:             uuid.New(),
		OrganizationID: organizationID,
		Name:           "Existing Product",
		Code:           "EXISTING-CODE",
	}

	updateReq := UpdateProductRequest{
		Code: "EXISTING-CODE", // Trying to update to existing code
	}
	reqBody, _ := json.Marshal(updateReq)

	mockRepo.On("GetByID", mock.Anything, productID).Return(product, nil)
	mockRepo.On("GetByCode", mock.Anything, "EXISTING-CODE", organizationID).Return(existingProduct, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: productID.String()}}
	c.Request = httptest.NewRequest("PUT", "/products/"+productID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateProduct(c)

	assert.Equal(t, http.StatusConflict, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestProductHandler_DeleteProduct_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo, _ := setupProductHandler()

	productID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, productID).Return(nil, gorm.ErrRecordNotFound)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: productID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/products/"+productID.String(), nil)

	handler.DeleteProduct(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}
