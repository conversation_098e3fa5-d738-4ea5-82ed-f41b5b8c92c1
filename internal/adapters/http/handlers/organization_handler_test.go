package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// setupOrganizationHandler creates an organization handler with mock dependencies for testing
func setupOrganizationHandler() (*OrganizationHandler, *MockOrganizationRepository) {
	mockOrgRepo := &MockOrganizationRepository{}
	handler := NewOrganizationHandler(mockOrgRepo)
	return handler, mockOrgRepo
}

func TestOrganizationHandler_GetOrganization(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizationID := uuid.New()
	organization := &entities.Organization{
		ID:        organizationID,
		Name:      "Test Organization",
		Slug:      "test-org",
		Email:     "<EMAIL>",
		Type:      entities.OrganizationTypeVendor,
		Status:    entities.OrganizationStatusActive,
		Protected: false,
		Settings:  entities.OrganizationSettings{"key": "value"},
		Metadata:  entities.Metadata{"meta": "data"},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, organizationID).Return(organization, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: organizationID.String()}}
	c.Request = httptest.NewRequest("GET", "/organizations/"+organizationID.String(), nil)

	handler.GetOrganization(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestOrganizationHandler_GetOrganization_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizationID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, organizationID).Return(nil, gorm.ErrRecordNotFound)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: organizationID.String()}}
	c.Request = httptest.NewRequest("GET", "/organizations/"+organizationID.String(), nil)

	handler.GetOrganization(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestOrganizationHandler_GetOrganizations(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizations := []*entities.Organization{
		{
			ID:        uuid.New(),
			Name:      "Organization 1",
			Slug:      "org-1",
			Email:     "<EMAIL>",
			Type:      entities.OrganizationTypeVendor,
			Status:    entities.OrganizationStatusActive,
			Protected: false,
			Settings:  entities.OrganizationSettings{},
			Metadata:  entities.Metadata{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:        uuid.New(),
			Name:      "Organization 2",
			Slug:      "org-2",
			Email:     "<EMAIL>",
			Type:      entities.OrganizationTypeCustomer,
			Status:    entities.OrganizationStatusActive,
			Protected: false,
			Settings:  entities.OrganizationSettings{},
			Metadata:  entities.Metadata{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	mockRepo.On("List", mock.Anything, mock.AnythingOfType("repositories.ListFilter")).Return(organizations, int64(2), nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/organizations", nil)

	handler.GetOrganizations(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)

	var response OrganizationListResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Len(t, response.Organizations, 2)
	assert.Equal(t, int64(2), response.Total)
}

func TestOrganizationHandler_CreateOrganization(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	createReq := CreateOrganizationRequest{
		Name:        "New Organization",
		Slug:        "new-org",
		Email:       "<EMAIL>",
		Type:        entities.OrganizationTypeVendor,
		Status:      entities.OrganizationStatusActive,
		Protected:   false,
		MaxUsers:    &[]int{100}[0],
		MaxLicenses: &[]int{50}[0],
		Settings:    map[string]any{"setting": "value"},
		Metadata:    map[string]any{"meta": "data"},
	}
	reqBody, _ := json.Marshal(createReq)

	mockRepo.On("GetBySlug", mock.Anything, "new-org").Return(nil, gorm.ErrRecordNotFound)
	mockRepo.On("Create", mock.Anything, mock.AnythingOfType("*entities.Organization")).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/organizations", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateOrganization(c)

	assert.Equal(t, http.StatusCreated, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestOrganizationHandler_UpdateOrganization(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizationID := uuid.New()
	organization := &entities.Organization{
		ID:        organizationID,
		Name:      "Original Organization",
		Slug:      "original-org",
		Email:     "<EMAIL>",
		Type:      entities.OrganizationTypeVendor,
		Status:    entities.OrganizationStatusActive,
		Protected: false,
		Settings:  entities.OrganizationSettings{},
		Metadata:  entities.Metadata{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	updateReq := UpdateOrganizationRequest{
		Name:     "Updated Organization",
		Email:    "<EMAIL>",
		Settings: map[string]any{"updated": "setting"},
	}
	reqBody, _ := json.Marshal(updateReq)

	mockRepo.On("GetByID", mock.Anything, organizationID).Return(organization, nil)
	mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*entities.Organization")).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: organizationID.String()}}
	c.Request = httptest.NewRequest("PUT", "/organizations/"+organizationID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateOrganization(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestOrganizationHandler_DeleteOrganization(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizationID := uuid.New()
	organization := &entities.Organization{
		ID:        organizationID,
		Name:      "Organization to Delete",
		Slug:      "delete-org",
		Email:     "<EMAIL>",
		Type:      entities.OrganizationTypeVendor,
		Status:    entities.OrganizationStatusActive,
		Protected: false, // Not protected, can be deleted
		Settings:  entities.OrganizationSettings{},
		Metadata:  entities.Metadata{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, organizationID).Return(organization, nil)
	mockRepo.On("SoftDelete", mock.Anything, organizationID).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: organizationID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/organizations/"+organizationID.String(), nil)

	handler.DeleteOrganization(c)

	assert.Equal(t, http.StatusNoContent, w.Code)
	mockRepo.AssertExpectations(t)
}

// Additional error handling tests

func TestOrganizationHandler_GetOrganization_InvalidID(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _ := setupOrganizationHandler()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: "invalid-uuid"}}
	c.Request = httptest.NewRequest("GET", "/organizations/invalid-uuid", nil)

	handler.GetOrganization(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestOrganizationHandler_CreateOrganization_InvalidRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _ := setupOrganizationHandler()

	// Test with invalid JSON
	invalidJSON := `{"name": "Test", "slug": "", "email": "invalid-email"}`
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/organizations", bytes.NewBufferString(invalidJSON))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateOrganization(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestOrganizationHandler_CreateOrganization_SlugExists(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	existingOrganization := &entities.Organization{
		ID:   uuid.New(),
		Name: "Existing Organization",
		Slug: "existing-slug",
	}

	createReq := CreateOrganizationRequest{
		Name:  "New Organization",
		Slug:  "existing-slug", // Same slug as existing organization
		Email: "<EMAIL>",
	}
	reqBody, _ := json.Marshal(createReq)

	mockRepo.On("GetBySlug", mock.Anything, "existing-slug").Return(existingOrganization, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/organizations", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateOrganization(c)

	assert.Equal(t, http.StatusConflict, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestOrganizationHandler_UpdateOrganization_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizationID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, organizationID).Return(nil, gorm.ErrRecordNotFound)

	updateReq := UpdateOrganizationRequest{
		Name: "Updated Organization",
	}
	reqBody, _ := json.Marshal(updateReq)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: organizationID.String()}}
	c.Request = httptest.NewRequest("PUT", "/organizations/"+organizationID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateOrganization(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestOrganizationHandler_UpdateOrganization_SlugConflict(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizationID := uuid.New()
	organization := &entities.Organization{
		ID:   organizationID,
		Name: "Original Organization",
		Slug: "original-slug",
	}

	existingOrganization := &entities.Organization{
		ID:   uuid.New(),
		Name: "Existing Organization",
		Slug: "existing-slug",
	}

	updateReq := UpdateOrganizationRequest{
		Slug: "existing-slug", // Trying to update to existing slug
	}
	reqBody, _ := json.Marshal(updateReq)

	mockRepo.On("GetByID", mock.Anything, organizationID).Return(organization, nil)
	mockRepo.On("GetBySlug", mock.Anything, "existing-slug").Return(existingOrganization, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: organizationID.String()}}
	c.Request = httptest.NewRequest("PUT", "/organizations/"+organizationID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateOrganization(c)

	assert.Equal(t, http.StatusConflict, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestOrganizationHandler_DeleteOrganization_Protected(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizationID := uuid.New()
	organization := &entities.Organization{
		ID:        organizationID,
		Name:      "Protected Organization",
		Slug:      "protected-org",
		Email:     "<EMAIL>",
		Protected: true, // Protected organization
	}

	mockRepo.On("GetByID", mock.Anything, organizationID).Return(organization, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: organizationID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/organizations/"+organizationID.String(), nil)

	handler.DeleteOrganization(c)

	assert.Equal(t, http.StatusForbidden, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestOrganizationHandler_DeleteOrganization_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupOrganizationHandler()

	organizationID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, organizationID).Return(nil, gorm.ErrRecordNotFound)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: organizationID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/organizations/"+organizationID.String(), nil)

	handler.DeleteOrganization(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}
