package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// Mock repositories are now defined in mocks_test.go

func setupTestHandler() (*PolicyHandler, *MockPolicyRepository, *MockProductRepository) {
	mockPolicyRepo := &MockPolicyRepository{}
	mockProductRepo := &MockProductRepository{}
	handler := NewPolicyHandler(mockPolicyRepo, mockProductRepo)
	return handler, mockPolicyRepo, mockProductRepo
}

func TestPolicyHandler_CreatePolicy(t *testing.T) {
	gin.SetMode(gin.TestMode)

	handler, mockPolicyRepo, mockProductRepo := setupTestHandler()

	// Test data
	productID := uuid.New()
	policyID := uuid.New()

	createReq := CreatePolicyRequest{
		Name:             "Test Policy",
		ProductID:        productID.String(),
		Scheme:           "ed25519",
		Encrypted:        true,
		UsePool:          false,
		Strict:           true,
		Floating:         false,
		Protected:        false,
		RequireHeartbeat: false,
	}

	// Mock product exists
	mockProduct := &entities.Product{
		ID:   productID,
		Name: "Test Product",
	}
	mockProductRepo.On("GetByID", mock.Anything, productID).Return(mockProduct, nil)

	// Mock policy creation
	mockPolicyRepo.On("Create", mock.Anything, mock.AnythingOfType("*entities.Policy")).Return(nil).Run(func(args mock.Arguments) {
		policy := args.Get(1).(*entities.Policy)
		policy.ID = policyID // Simulate DB setting ID
		policy.CreatedAt = time.Now()
		policy.UpdatedAt = time.Now()
	})

	// Create request
	reqBody, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/policies", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	// Create response recorder
	w := httptest.NewRecorder()

	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req

	// Call handler
	handler.CreatePolicy(c)

	// Assertions
	assert.Equal(t, http.StatusCreated, w.Code)

	var response PolicyResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Test Policy", response.Name)
	assert.Equal(t, productID.String(), response.ProductID)
	assert.True(t, response.Encrypted)
	assert.True(t, response.Strict)

	// Verify mocks were called
	mockProductRepo.AssertExpectations(t)
	mockPolicyRepo.AssertExpectations(t)
}

func TestPolicyHandler_GetPolicy(t *testing.T) {
	gin.SetMode(gin.TestMode)

	handler, mockPolicyRepo, _ := setupTestHandler()

	// Test data
	policyID := uuid.New()
	productID := uuid.New()

	mockPolicy := &entities.Policy{
		ID:               policyID,
		Name:             "Test Policy",
		ProductID:        productID,
		Encrypted:        true,
		UsePool:          false,
		Strict:           true,
		Floating:         false,
		Protected:        false,
		RequireHeartbeat: false,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Mock policy retrieval
	mockPolicyRepo.On("GetByID", mock.Anything, policyID).Return(mockPolicy, nil)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/policies/"+policyID.String(), nil)

	// Create response recorder
	w := httptest.NewRecorder()

	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Params = gin.Params{{Key: "id", Value: policyID.String()}}

	// Call handler
	handler.GetPolicy(c)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response PolicyResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, policyID.String(), response.ID)
	assert.Equal(t, "Test Policy", response.Name)
	assert.Equal(t, productID.String(), response.ProductID)

	// Verify mocks were called
	mockPolicyRepo.AssertExpectations(t)
}

func TestPolicyHandler_GetPolicy_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)

	handler, mockPolicyRepo, _ := setupTestHandler()

	// Test data
	policyID := uuid.New()

	// Mock policy not found
	mockPolicyRepo.On("GetByID", mock.Anything, policyID).Return(nil, gorm.ErrRecordNotFound)

	// Create request
	req := httptest.NewRequest(http.MethodGet, "/policies/"+policyID.String(), nil)

	// Create response recorder
	w := httptest.NewRecorder()

	// Create Gin context
	c, _ := gin.CreateTestContext(w)
	c.Request = req
	c.Params = gin.Params{{Key: "id", Value: policyID.String()}}

	// Call handler
	handler.GetPolicy(c)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response ErrorResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "POLICY_NOT_FOUND", response.Error.Code)

	// Verify mocks were called
	mockPolicyRepo.AssertExpectations(t)
}
