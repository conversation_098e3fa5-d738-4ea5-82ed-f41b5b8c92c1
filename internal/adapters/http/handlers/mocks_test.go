package handlers

import (
	"context"
	"time"

	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
)

// Shared mock repositories for all handler tests

type MockLicenseRepository struct {
	mock.Mock
}

func (m *MockLicenseRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.License, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.License, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.License), args.Get(1).(int64), args.Error(2)
}

func (m *MockLicenseRepository) Create(ctx context.Context, entity *entities.License) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockLicenseRepository) Update(ctx context.Context, entity *entities.License) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockLicenseRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLicenseRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockLicenseRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockLicenseRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockLicenseRepository) GetByKey(ctx context.Context, key string) (*entities.License, error) {
	args := m.Called(ctx, key)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) GetByPolicy(ctx context.Context, policyID uuid.UUID) ([]*entities.License, error) {
	args := m.Called(ctx, policyID)
	return args.Get(0).([]*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) GetExpiring(ctx context.Context, organizationID uuid.UUID, beforeDate time.Time) ([]*entities.License, error) {
	args := m.Called(ctx, organizationID, beforeDate)
	return args.Get(0).([]*entities.License), args.Error(1)
}

func (m *MockLicenseRepository) UpdateLastValidated(ctx context.Context, licenseID uuid.UUID) error {
	args := m.Called(ctx, licenseID)
	return args.Error(0)
}

func (m *MockLicenseRepository) IncrementValidationCount(ctx context.Context, licenseID uuid.UUID) error {
	args := m.Called(ctx, licenseID)
	return args.Error(0)
}

type MockPolicyRepository struct {
	mock.Mock
}

func (m *MockPolicyRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Policy, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.Policy), args.Error(1)
}

func (m *MockPolicyRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.Policy, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.Policy), args.Get(1).(int64), args.Error(2)
}

func (m *MockPolicyRepository) Create(ctx context.Context, entity *entities.Policy) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockPolicyRepository) Update(ctx context.Context, entity *entities.Policy) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockPolicyRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockPolicyRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockPolicyRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockPolicyRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockPolicyRepository) GetByProduct(ctx context.Context, productID uuid.UUID) ([]*entities.Policy, error) {
	args := m.Called(ctx, productID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entities.Policy), args.Error(1)
}

func (m *MockPolicyRepository) GetByScheme(ctx context.Context, scheme string, organizationID uuid.UUID) ([]*entities.Policy, error) {
	args := m.Called(ctx, scheme, organizationID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entities.Policy), args.Error(1)
}

type MockOrganizationRepository struct {
	mock.Mock
}

func (m *MockOrganizationRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.Organization), args.Error(1)
}

func (m *MockOrganizationRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.Organization, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.Organization), args.Get(1).(int64), args.Error(2)
}

func (m *MockOrganizationRepository) Create(ctx context.Context, entity *entities.Organization) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockOrganizationRepository) Update(ctx context.Context, entity *entities.Organization) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockOrganizationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockOrganizationRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockOrganizationRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockOrganizationRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockOrganizationRepository) GetBySlug(ctx context.Context, slug string) (*entities.Organization, error) {
	args := m.Called(ctx, slug)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.Organization), args.Error(1)
}

type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.User, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.User), args.Error(1)
}

func (m *MockUserRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.User, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) Create(ctx context.Context, entity *entities.User) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockUserRepository) Update(ctx context.Context, entity *entities.User) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockUserRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*entities.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.User), args.Error(1)
}

func (m *MockUserRepository) UpdateLastLogin(ctx context.Context, userID uuid.UUID) error {
	args := m.Called(ctx, userID)
	return args.Error(0)
}

type MockMachineRepository struct {
	mock.Mock
}

func (m *MockMachineRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Machine, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.Machine), args.Error(1)
}

func (m *MockMachineRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.Machine, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.Machine), args.Get(1).(int64), args.Error(2)
}

func (m *MockMachineRepository) Create(ctx context.Context, entity *entities.Machine) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockMachineRepository) Update(ctx context.Context, entity *entities.Machine) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockMachineRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockMachineRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockMachineRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockMachineRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockMachineRepository) GetByFingerprint(ctx context.Context, fingerprint string, licenseID uuid.UUID) (*entities.Machine, error) {
	args := m.Called(ctx, fingerprint, licenseID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.Machine), args.Error(1)
}

func (m *MockMachineRepository) GetByLicense(ctx context.Context, licenseID uuid.UUID) ([]*entities.Machine, error) {
	args := m.Called(ctx, licenseID)
	return args.Get(0).([]*entities.Machine), args.Error(1)
}

func (m *MockMachineRepository) UpdateHeartbeat(ctx context.Context, machineID uuid.UUID) error {
	args := m.Called(ctx, machineID)
	return args.Error(0)
}

func (m *MockMachineRepository) GetDeadMachines(ctx context.Context, organizationID uuid.UUID, beforeDate time.Time) ([]*entities.Machine, error) {
	args := m.Called(ctx, organizationID, beforeDate)
	return args.Get(0).([]*entities.Machine), args.Error(1)
}

func (m *MockMachineRepository) GetStale(ctx context.Context, olderThan time.Time) ([]*entities.Machine, error) {
	args := m.Called(ctx, olderThan)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*entities.Machine), args.Error(1)
}

type MockProductRepository struct {
	mock.Mock
}

func (m *MockProductRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Product, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.Product), args.Error(1)
}

func (m *MockProductRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.Product, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.Product), args.Get(1).(int64), args.Error(2)
}

func (m *MockProductRepository) Create(ctx context.Context, entity *entities.Product) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockProductRepository) Update(ctx context.Context, entity *entities.Product) error {
	args := m.Called(ctx, entity)
	return args.Error(0)
}

func (m *MockProductRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockProductRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockProductRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockProductRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockProductRepository) GetByCode(ctx context.Context, code string, organizationID uuid.UUID) (*entities.Product, error) {
	args := m.Called(ctx, code, organizationID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.Product), args.Error(1)
}

func (m *MockProductRepository) GetByOrganizationID(ctx context.Context, organizationID uuid.UUID) ([]*entities.Product, error) {
	args := m.Called(ctx, organizationID)
	return args.Get(0).([]*entities.Product), args.Error(1)
}
