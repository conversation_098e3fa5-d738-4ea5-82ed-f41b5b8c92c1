package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gorm.io/gorm"
)

// setupUserHandler creates a user handler with mock dependencies for testing
func setupUserHandler() (*UserHandler, *MockUserRepository) {
	mockUserRepo := &MockUserRepository{}
	handler := NewUserHandler(mockUserRepo)
	return handler, mockUserRepo
}

func TestUserHandler_GetUser(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	userID := uuid.New()
	user := &entities.User{
		ID:          userID,
		Email:       "<EMAIL>",
		FirstName:   &[]string{"<PERSON>"}[0],
		LastName:    &[]string{"Doe"}[0],
		Status:      entities.UserStatusActive,
		TOTPEnabled: false,
		Metadata:    entities.Metadata{"key": "value"},
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, userID).Return(user, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: userID.String()}}
	c.Request = httptest.NewRequest("GET", "/users/"+userID.String(), nil)

	handler.GetUser(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)

	var response UserResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, userID.String(), response.ID)
	assert.Equal(t, "<EMAIL>", response.Email)
	assert.Equal(t, "John", response.FirstName)
	assert.Equal(t, "Doe", response.LastName)
	assert.Equal(t, "John Doe", response.FullName)
	assert.Equal(t, entities.UserStatusActive, response.Status)
	assert.True(t, response.IsActive)
	assert.False(t, response.IsBanned)
	assert.False(t, response.Has2FA)
}

func TestUserHandler_GetUser_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	userID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, userID).Return(nil, gorm.ErrRecordNotFound)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: userID.String()}}
	c.Request = httptest.NewRequest("GET", "/users/"+userID.String(), nil)

	handler.GetUser(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestUserHandler_GetUsers(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	users := []*entities.User{
		{
			ID:        uuid.New(),
			Email:     "<EMAIL>",
			FirstName: &[]string{"User"}[0],
			LastName:  &[]string{"One"}[0],
			Status:    entities.UserStatusActive,
			Metadata:  entities.Metadata{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:        uuid.New(),
			Email:     "<EMAIL>",
			FirstName: &[]string{"User"}[0],
			LastName:  &[]string{"Two"}[0],
			Status:    entities.UserStatusInactive,
			Metadata:  entities.Metadata{},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	mockRepo.On("List", mock.Anything, mock.AnythingOfType("repositories.ListFilter")).Return(users, int64(2), nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/users", nil)

	handler.GetUsers(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)

	var response UserListResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Len(t, response.Users, 2)
	assert.Equal(t, int64(2), response.Total)
}

func TestUserHandler_CreateUser(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	createReq := CreateUserRequest{
		Email:     "<EMAIL>",
		Password:  "password123",
		FirstName: "New",
		LastName:  "User",
		Status:    entities.UserStatusActive,
		Metadata:  map[string]any{"role": "user"},
	}
	reqBody, _ := json.Marshal(createReq)

	mockRepo.On("GetByEmail", mock.Anything, "<EMAIL>").Return(nil, gorm.ErrRecordNotFound)
	mockRepo.On("Create", mock.Anything, mock.AnythingOfType("*entities.User")).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/users", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateUser(c)

	assert.Equal(t, http.StatusCreated, w.Code)
	mockRepo.AssertExpectations(t)

	var response UserResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "<EMAIL>", response.Email)
	assert.Equal(t, "New", response.FirstName)
	assert.Equal(t, "User", response.LastName)
	assert.Equal(t, "New User", response.FullName)
}

func TestUserHandler_UpdateUser(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	userID := uuid.New()
	user := &entities.User{
		ID:        userID,
		Email:     "<EMAIL>",
		FirstName: &[]string{"Original"}[0],
		LastName:  &[]string{"User"}[0],
		Status:    entities.UserStatusActive,
		Metadata:  entities.Metadata{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	updateReq := UpdateUserRequest{
		FirstName: "Updated",
		LastName:  "Name",
		Status:    entities.UserStatusInactive,
		Metadata:  map[string]any{"updated": "true"},
	}
	reqBody, _ := json.Marshal(updateReq)

	mockRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
	mockRepo.On("Update", mock.Anything, mock.AnythingOfType("*entities.User")).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: userID.String()}}
	c.Request = httptest.NewRequest("PUT", "/users/"+userID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateUser(c)

	assert.Equal(t, http.StatusOK, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestUserHandler_DeleteUser(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	userID := uuid.New()
	user := &entities.User{
		ID:        userID,
		Email:     "<EMAIL>",
		Status:    entities.UserStatusActive,
		Metadata:  entities.Metadata{},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	mockRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
	mockRepo.On("SoftDelete", mock.Anything, userID).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: userID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/users/"+userID.String(), nil)

	handler.DeleteUser(c)

	assert.Equal(t, http.StatusNoContent, w.Code)
	mockRepo.AssertExpectations(t)
}

// Additional error handling tests

func TestUserHandler_GetUser_InvalidID(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _ := setupUserHandler()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: "invalid-uuid"}}
	c.Request = httptest.NewRequest("GET", "/users/invalid-uuid", nil)

	handler.GetUser(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUserHandler_CreateUser_InvalidRequest(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, _ := setupUserHandler()

	// Test with invalid JSON (missing required fields)
	invalidJSON := `{"email": "invalid-email", "password": "123"}`
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/users", bytes.NewBufferString(invalidJSON))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateUser(c)

	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUserHandler_CreateUser_EmailExists(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	existingUser := &entities.User{
		ID:    uuid.New(),
		Email: "<EMAIL>",
	}

	createReq := CreateUserRequest{
		Email:    "<EMAIL>", // Same email as existing user
		Password: "password123",
	}
	reqBody, _ := json.Marshal(createReq)

	mockRepo.On("GetByEmail", mock.Anything, "<EMAIL>").Return(existingUser, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/users", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateUser(c)

	assert.Equal(t, http.StatusConflict, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestUserHandler_UpdateUser_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	userID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, userID).Return(nil, gorm.ErrRecordNotFound)

	updateReq := UpdateUserRequest{
		FirstName: "Updated",
	}
	reqBody, _ := json.Marshal(updateReq)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: userID.String()}}
	c.Request = httptest.NewRequest("PUT", "/users/"+userID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateUser(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestUserHandler_UpdateUser_EmailConflict(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	userID := uuid.New()
	user := &entities.User{
		ID:    userID,
		Email: "<EMAIL>",
	}

	existingUser := &entities.User{
		ID:    uuid.New(),
		Email: "<EMAIL>",
	}

	updateReq := UpdateUserRequest{
		Email: "<EMAIL>", // Trying to update to existing email
	}
	reqBody, _ := json.Marshal(updateReq)

	mockRepo.On("GetByID", mock.Anything, userID).Return(user, nil)
	mockRepo.On("GetByEmail", mock.Anything, "<EMAIL>").Return(existingUser, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: userID.String()}}
	c.Request = httptest.NewRequest("PUT", "/users/"+userID.String(), bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.UpdateUser(c)

	assert.Equal(t, http.StatusConflict, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestUserHandler_DeleteUser_NotFound(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	userID := uuid.New()
	mockRepo.On("GetByID", mock.Anything, userID).Return(nil, gorm.ErrRecordNotFound)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Params = []gin.Param{{Key: "id", Value: userID.String()}}
	c.Request = httptest.NewRequest("DELETE", "/users/"+userID.String(), nil)

	handler.DeleteUser(c)

	assert.Equal(t, http.StatusNotFound, w.Code)
	mockRepo.AssertExpectations(t)
}

func TestUserHandler_CreateUser_PasswordSecurity(t *testing.T) {
	gin.SetMode(gin.TestMode)
	handler, mockRepo := setupUserHandler()

	createReq := CreateUserRequest{
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}
	reqBody, _ := json.Marshal(createReq)

	mockRepo.On("GetByEmail", mock.Anything, "<EMAIL>").Return(nil, gorm.ErrRecordNotFound)
	mockRepo.On("Create", mock.Anything, mock.MatchedBy(func(user *entities.User) bool {
		// Verify password is hashed and not stored in plain text
		return user.Password != "securepassword123" && len(user.Password) > 20
	})).Return(nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/users", bytes.NewBuffer(reqBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.CreateUser(c)

	assert.Equal(t, http.StatusCreated, w.Code)
	mockRepo.AssertExpectations(t)

	// Verify password is not exposed in response
	var response UserResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	// UserResponse struct doesn't have a Password field, which is correct for security
}
