package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// ErrorResponse represents a standardized error response
type ErrorResponse struct {
	Error ErrorDetail `json:"error"`
}

// ErrorDetail contains error information
type ErrorDetail struct {
	Code      string    `json:"code" example:"VALIDATION_ERROR"`
	Message   string    `json:"message" example:"Invalid request format"`
	Details   string    `json:"details,omitempty" example:"Field 'email' is required"`
	Timestamp time.Time `json:"timestamp" example:"2024-01-15T10:30:00Z"`
	RequestID string    `json:"request_id,omitempty" example:"req_123456789"`
}

// SendError sends a standardized error response
func SendError(c *gin.Context, statusCode int, code, message string, err error) {
	var details string
	if err != nil {
		details = err.Error()
		
		// Log error for debugging (don't expose internal errors to client)
		log.Error().
			Err(err).
			Str("code", code).
			Str("message", message).
			Int("status", statusCode).
			Str("path", c.Request.URL.Path).
			Str("method", c.Request.Method).
			Msg("HTTP error response")
	}

	// Get request ID if set
	requestID, _ := c.Get("request_id")
	requestIDStr, _ := requestID.(string)

	response := ErrorResponse{
		Error: ErrorDetail{
			Code:      code,
			Message:   message,
			Details:   details,
			Timestamp: time.Now().UTC(),
			RequestID: requestIDStr,
		},
	}

	c.JSON(statusCode, response)
	c.Abort()
}

// SendValidationError sends a validation error response
func SendValidationError(c *gin.Context, message string, err error) {
	SendError(c, 400, "VALIDATION_ERROR", message, err)
}

// SendUnauthorizedError sends an unauthorized error response
func SendUnauthorizedError(c *gin.Context, message string) {
	SendError(c, 401, "AUTHENTICATION_REQUIRED", message, nil)
}

// SendForbiddenError sends a forbidden error response
func SendForbiddenError(c *gin.Context, message string) {
	SendError(c, 403, "INSUFFICIENT_PERMISSIONS", message, nil)
}

// SendNotFoundError sends a not found error response
func SendNotFoundError(c *gin.Context, message string) {
	SendError(c, 404, "RESOURCE_NOT_FOUND", message, nil)
}

// SendInternalError sends an internal server error response
func SendInternalError(c *gin.Context, message string, err error) {
	SendError(c, 500, "INTERNAL_ERROR", message, err)
}