package http

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	// "github.com/gokeys/gokeys/internal/adapters/http/handlers" // Temporarily disabled
	// "github.com/gokeys/gokeys/internal/adapters/http/middleware" // Temporarily disabled
	"github.com/gokeys/gokeys/internal/adapters/http/routes"
	"github.com/gokeys/gokeys/internal/adapters/metrics"
	"github.com/gokeys/gokeys/internal/config"
	"github.com/gokeys/gokeys/internal/domain/services"
	// "github.com/gokeys/gokeys/internal/domain/services/auth" // Temporarily disabled

	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// Server represents the HTTP server
type Server struct {
	config             *config.Config
	router             *gin.Engine
	httpServer         *http.Server
	database           DatabaseHealthChecker
	metricsExporter    MetricsExporter
	serviceCoordinator *services.ServiceCoordinator
	apiRoutes          *routes.APIRoutes
}

// DatabaseHealthChecker interface for database health checks
type DatabaseHealthChecker interface {
	Health() error
}

// MetricsExporter interface for metrics exporter status
type MetricsExporter interface {
	IsRunning() bool
}

// NewServer creates a new HTTP server
func NewServer(cfg *config.Config) *Server {
	// Set Gin mode based on environment
	if cfg.Server.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	router := gin.New()

	// CORS middleware
	corsConfig := cors.DefaultConfig()
	corsConfig.AllowOrigins = []string{
		"http://localhost:3000",    // Next.js development server
		"http://localhost:3001",    // Alternative port
		"https://admin.gokeys.com", // Production admin portal
	}
	corsConfig.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"}
	corsConfig.AllowHeaders = []string{
		"Origin",
		"Content-Type",
		"Accept",
		"Authorization",
		"X-API-Key",
		"X-License-Key",
		"X-Requested-With",
	}
	corsConfig.ExposeHeaders = []string{"Content-Length"}
	corsConfig.AllowCredentials = true
	router.Use(cors.New(corsConfig))

	// Middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	server := &Server{
		config: cfg,
		router: router,
	}

	// Setup routes
	server.setupRoutes()

	return server
}

// setupRoutes configures all routes
func (s *Server) setupRoutes() {
	// Health check routes
	health := s.router.Group("/health")
	{
		health.GET("/live", s.livenessCheck)
		health.GET("/ready", s.readinessCheck)
		health.GET("/startup", s.startupCheck)
	}

	// Swagger documentation (only in development and staging)
	if s.config.Server.Environment != "production" {
		s.router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))
		s.router.GET("/docs", func(c *gin.Context) {
			c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
		})
	}

	// API routes will be set up later when service coordinator is available

	// Root route
	s.router.GET("/", s.root)
}

// setupAPIRoutes configures only the API routes (called after service coordinator is set)
func (s *Server) setupAPIRoutes() {
	// Setup API routes if service coordinator is available
	if s.serviceCoordinator != nil && s.apiRoutes != nil {
		s.apiRoutes.RegisterRoutes(s.router)
	}
}

// Start starts the HTTP server
func (s *Server) Start() error {
	addr := fmt.Sprintf("%s:%d", s.config.Server.Host, s.config.Server.Port)

	s.httpServer = &http.Server{
		Addr:         addr,
		Handler:      s.router,
		ReadTimeout:  time.Duration(s.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(s.config.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(s.config.Server.IdleTimeout) * time.Second,
	}

	log.Printf("Starting HTTP server on %s", addr)

	if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("failed to start server: %w", err)
	}

	return nil
}

// Stop gracefully stops the HTTP server
func (s *Server) Stop(ctx context.Context) error {
	log.Println("Stopping HTTP server...")

	if err := s.httpServer.Shutdown(ctx); err != nil {
		return fmt.Errorf("failed to shutdown server: %w", err)
	}

	log.Println("HTTP server stopped")
	return nil
}

// AddMetricsMiddleware adds metrics middleware to the server
func (s *Server) AddMetricsMiddleware(collector *metrics.MetricsCollector) {
	s.router.Use(collector.MetricsMiddleware())
}

// SetDatabase sets the database health checker
func (s *Server) SetDatabase(db DatabaseHealthChecker) {
	s.database = db
}

// SetMetricsExporter sets the metrics exporter
func (s *Server) SetMetricsExporter(exporter MetricsExporter) {
	s.metricsExporter = exporter
}

// SetServiceCoordinator sets the service coordinator and initializes API routes
func (s *Server) SetServiceCoordinator(serviceCoordinator *services.ServiceCoordinator) {
	s.serviceCoordinator = serviceCoordinator

	// Create clean auth service (temporarily disabled)
	// authService := auth.NewAuthService(
	//	serviceCoordinator.Repositories.User(),
	//	serviceCoordinator.Repositories.Session(),
	//	serviceCoordinator.Repositories.APIToken(),
	//	serviceCoordinator.GetCryptoService(),
	//	s.config.Security.JWTPrivateKey,
	//	s.config.Security.JWTPublicKey,
	// )

	// Create middleware (temporarily disabled)
	// authMW := middleware.NewAuthenticationMiddleware(
	//	serviceCoordinator.Repositories.User(),
	//	serviceCoordinator.Repositories.Session(),
	//	serviceCoordinator.Repositories.APIToken(),
	//	authService,
	// )

	// Create API routes with authorization
	apiRoutes := routes.NewAPIRoutes(serviceCoordinator)
	s.apiRoutes = apiRoutes

	// Setup all API routes
	s.setupAPIRoutes()
}


// Handler functions

func (s *Server) root(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"service":     "GoKeys License Management Platform",
		"version":     "1.0.0",
		"status":      "running",
		"environment": s.config.Server.Environment,
	})
}

func (s *Server) livenessCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "alive",
		"timestamp": time.Now().UTC(),
	})
}

func (s *Server) readinessCheck(c *gin.Context) {
	checks := gin.H{}
	status := http.StatusOK

	// Database health check
	if s.database != nil {
		if err := s.database.Health(); err != nil {
			checks["database"] = gin.H{
				"status": "failed",
				"error":  err.Error(),
			}
			status = http.StatusServiceUnavailable
		} else {
			checks["database"] = "ok"
		}
	} else {
		checks["database"] = "not_configured"
	}

	// TODO: Add Redis health check when implemented
	checks["redis"] = "not_implemented"

	// Metrics exporter status
	if s.metricsExporter != nil {
		if s.metricsExporter.IsRunning() {
			checks["metrics_exporter"] = "running"
		} else {
			checks["metrics_exporter"] = "stopped"
		}
	} else {
		checks["metrics_exporter"] = "not_configured"
	}

	response := gin.H{
		"status":    "ready",
		"timestamp": time.Now().UTC(),
		"checks":    checks,
	}

	if status != http.StatusOK {
		response["status"] = "not_ready"
	}

	c.JSON(status, response)
}

func (s *Server) startupCheck(c *gin.Context) {
	// TODO: Add actual startup checks
	c.JSON(http.StatusOK, gin.H{
		"status":    "started",
		"timestamp": time.Now().UTC(),
	})
}
