package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers"
	"github.com/gokeys/gokeys/internal/domain/services"
)

// APIRoutes manages all API route configurations with authorization
type APIRoutes struct {
	serviceCoordinator *services.ServiceCoordinator

	// Handlers
	licenseHandler      *handlers.LicenseHandler
	policyHandler       *handlers.PolicyHandler
	productHandler      *handlers.ProductHandler
	userHandler         *handlers.UserHandler
	organizationHandler *handlers.OrganizationHandler
	machineHandler      *handlers.MachineHandler
}

// NewAPIRoutes creates a new API routes manager with authorization
func NewAPIRoutes(serviceCoordinator *services.ServiceCoordinator) *APIRoutes {
	// Create authorization middleware
	// Create handlers
	licenseHandler := handlers.NewLicenseHandler(
		serviceCoordinator.Repositories.License(),
		serviceCoordinator.Repositories.Policy(),
		serviceCoordinator.Repositories.Organization(),
		serviceCoordinator.Repositories.User(),
		serviceCoordinator.GetCryptoService(),
	)

	policyHandler := handlers.NewPolicyHandler(
		serviceCoordinator.Repositories.Policy(),
		serviceCoordinator.Repositories.Product(),
	)

	productHandler := handlers.NewProductHandler(
		serviceCoordinator.Repositories.Product(),
		serviceCoordinator.Repositories.Organization(),
	)

	userHandler := handlers.NewUserHandler(
		serviceCoordinator.Repositories.User(),
	)

	organizationHandler := handlers.NewOrganizationHandler(
		serviceCoordinator.Repositories.Organization(),
	)

	machineHandler := handlers.NewMachineHandler(
		serviceCoordinator.Repositories.Machine(),
		serviceCoordinator.Repositories.License(),
		serviceCoordinator.Repositories.User(),
	)

	return &APIRoutes{
		serviceCoordinator:  serviceCoordinator,
		licenseHandler:      licenseHandler,
		policyHandler:       policyHandler,
		productHandler:      productHandler,
		userHandler:         userHandler,
		organizationHandler: organizationHandler,
		machineHandler:      machineHandler,
	}
}

// RegisterRoutes registers all API routes with authorization
func (ar *APIRoutes) RegisterRoutes(router *gin.Engine) {
	api := router.Group("/api/v1")

	// Public health check (no auth required)
	api.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "GoKeys API is running",
		})
	})

	// License routes with authorization
	ar.registerLicenseRoutes(api)

	// Policy routes with authorization
	ar.registerPolicyRoutes(api)

	// Product routes with authorization
	ar.registerProductRoutes(api)

	// User routes with authorization
	ar.registerUserRoutes(api)

	// Organization routes with authorization
	ar.registerOrganizationRoutes(api)

	// Machine routes with authorization
	ar.registerMachineRoutes(api)
}

// registerLicenseRoutes registers license-related routes with proper authorization
func (ar *APIRoutes) registerLicenseRoutes(api *gin.RouterGroup) {
	licenses := api.Group("/licenses")

	// List licenses - requires license.read permission
	licenses.GET("",
		ar.licenseHandler.GetLicenses,
	)

	// Get single license - requires license.read permission with ownership check
	licenses.GET("/:id",
		ar.licenseHandler.GetLicense,
	)

	// Create license - requires license.create permission
	licenses.POST("",
		ar.licenseHandler.CreateLicense,
	)

	// Update license - requires license.update permission with ownership check
	licenses.PUT("/:id",
		ar.licenseHandler.UpdateLicense,
	)

	// Delete license - requires license.delete permission with ownership check
	licenses.DELETE("/:id",
		ar.licenseHandler.DeleteLicense,
	)

	// License actions - require specific permissions
	licenseActions := licenses.Group("/:id/actions")
	{
		// Renew license - requires license.manage permission
		licenseActions.POST("/renew",
			ar.licenseHandler.RenewLicense,
		)

		// Suspend license - requires license.manage permission
		licenseActions.POST("/suspend",
			ar.licenseHandler.SuspendLicense,
		)

		// Reinstate license - requires license.manage permission
		licenseActions.POST("/reinstate",
			ar.licenseHandler.ReinstateLicense,
		)

		// Transfer license - requires license.manage permission
		licenseActions.POST("/transfer",
			ar.licenseHandler.TransferLicense,
		)
	}
}

// registerPolicyRoutes registers policy-related routes with proper authorization
func (ar *APIRoutes) registerPolicyRoutes(api *gin.RouterGroup) {
	policies := api.Group("/policies")

	// List policies - requires policy.read permission
	policies.GET("",
		ar.policyHandler.GetPolicies,
	)

	// Get single policy - requires policy.read permission
	policies.GET("/:id",
		ar.policyHandler.GetPolicy,
	)

	// Create policy - requires policy.create permission
	policies.POST("",
		ar.policyHandler.CreatePolicy,
	)

	// Update policy - requires policy.update permission
	policies.PUT("/:id",
		ar.policyHandler.UpdatePolicy,
	)

	// Delete policy - requires policy.delete permission
	policies.DELETE("/:id",
		ar.policyHandler.DeletePolicy,
	)
}

// registerProductRoutes registers product-related routes with proper authorization
func (ar *APIRoutes) registerProductRoutes(api *gin.RouterGroup) {
	products := api.Group("/products")

	// List products - requires product.read permission
	products.GET("",
		ar.productHandler.GetProducts,
	)

	// Get single product - requires product.read permission
	products.GET("/:id",
		ar.productHandler.GetProduct,
	)

	// Create product - requires product.create permission
	products.POST("",
		ar.productHandler.CreateProduct,
	)

	// Update product - requires product.update permission
	products.PUT("/:id",
		ar.productHandler.UpdateProduct,
	)

	// Delete product - requires product.delete permission
	products.DELETE("/:id",
		ar.productHandler.DeleteProduct,
	)
}

// registerUserRoutes registers user-related routes with proper authorization
func (ar *APIRoutes) registerUserRoutes(api *gin.RouterGroup) {
	users := api.Group("/users")

	// List users - requires user.read permission
	users.GET("",
		ar.userHandler.GetUsers,
	)

	// Get single user - requires user.read permission with ownership check
	users.GET("/:id",
		ar.userHandler.GetUser,
	)

	// Create user - requires user.create permission
	users.POST("",
		ar.userHandler.CreateUser,
	)

	// Update user - requires user.update permission with ownership check
	users.PUT("/:id",
		ar.userHandler.UpdateUser,
	)

	// Delete user - requires user.delete permission
	users.DELETE("/:id",
		ar.userHandler.DeleteUser,
	)
}

// registerOrganizationRoutes registers organization-related routes with proper authorization
func (ar *APIRoutes) registerOrganizationRoutes(api *gin.RouterGroup) {
	orgs := api.Group("/organizations")

	// List organizations - requires organization.read permission
	orgs.GET("",
		ar.organizationHandler.GetOrganizations,
	)

	// Get single organization - requires organization.read permission
	orgs.GET("/:id",
		ar.organizationHandler.GetOrganization,
	)

	// Create organization - requires organization.create permission (system admin only)
	orgs.POST("",
		ar.organizationHandler.CreateOrganization,
	)

	// Update organization - requires organization.update permission
	orgs.PUT("/:id",
		ar.organizationHandler.UpdateOrganization,
	)

	// Delete organization - requires organization.delete permission (system admin only)
	orgs.DELETE("/:id",
		ar.organizationHandler.DeleteOrganization,
	)
}

// registerMachineRoutes registers machine-related routes with proper authorization
func (ar *APIRoutes) registerMachineRoutes(api *gin.RouterGroup) {
	machines := api.Group("/machines")

	// List machines - requires machine.read permission
	machines.GET("",
		ar.machineHandler.GetMachines,
	)

	// Get single machine - requires machine.read permission
	machines.GET("/:id",
		ar.machineHandler.GetMachine,
	)

	// Create machine - requires machine.create permission
	machines.POST("",
		ar.machineHandler.CreateMachine,
	)

	// Update machine - requires machine.update permission
	machines.PUT("/:id",
		ar.machineHandler.UpdateMachine,
	)

	// Delete machine - requires machine.delete permission
	machines.DELETE("/:id",
		ar.machineHandler.DeleteMachine,
	)
}
