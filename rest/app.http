### GoKeys License Management Platform - REST API Test Cases
### Base URL
@baseUrl = http://localhost:8080
@apiVersion = v1

### Variables
@adminEmail = <EMAIL>
@adminPassword = admin123
@vendorEmail = <EMAIL>
@vendorPassword = vendor123
@customerEmail = <EMAIL>
@customerPassword = customer123

###
### 1. HEALTH CHECKS
###

### Health Check - Live
GET {{baseUrl}}/health/live
Content-Type: application/json

### Health Check - Ready
GET {{baseUrl}}/health/ready
Content-Type: application/json

### Health Check - Startup
GET {{baseUrl}}/health/startup
Content-Type: application/json

### API Health Check
GET {{baseUrl}}/{{apiVersion}}/health
Content-Type: application/json

###
### 2. AUTHENTICATION
###

### Login - Super Admin
# @name loginAdmin
POST {{baseUrl}}/{{apiVersion}}/auth/login
Content-Type: application/json

{
  "email": "{{adminEmail}}",
  "password": "{{adminPassword}}"
}

### Login - Vendor Admin  
# @name loginVendor
POST {{baseUrl}}/{{apiVersion}}/auth/login
Content-Type: application/json

{
  "email": "{{vendorEmail}}",
  "password": "{{vendorPassword}}"
}

### Login - Customer User
# @name loginCustomer
POST {{baseUrl}}/{{apiVersion}}/auth/login
Content-Type: application/json

{
  "email": "{{customerEmail}}",
  "password": "{{customerPassword}}"
}

### Register New User (B2C - Customer without organization)
POST {{baseUrl}}/{{apiVersion}}/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "New",
  "last_name": "Customer"
}

### Register New User (B2B - Admin with organization)
POST {{baseUrl}}/{{apiVersion}}/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "New",
  "last_name": "Admin",
  "organization_name": "New Company Inc"
}

###
### 3. ORGANIZATIONS (Admin Access Required)
### Note: Replace {{adminToken}} with actual token from login response
###

@adminToken = ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
@superAdminToken = ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
### List Organizations
GET {{baseUrl}}/{{apiVersion}}/organizations
Authorization: Bearer {{{{superAdminToken}}}}
Content-Type: application/json

### Create Organization
POST {{baseUrl}}/{{apiVersion}}/organizations
Authorization: Bearer {{{{superAdminToken}}}}
Content-Type: application/json

{
  "name": "Test Organization",
  "slug": "test-org",
  "website": "https://test-org.com",
  "email": "<EMAIL>"
}

### Get Organization by ID
@organizationId = ffffffff-ffff-ffff-ffff-ffffffffffff
GET {{baseUrl}}/{{apiVersion}}/organizations/{{organizationId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Update Organization
PUT {{baseUrl}}/{{apiVersion}}/organizations/{{organizationId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Updated Test Organization",
  "website": "https://updated-test-org.com"
}



###
### 4. USERS MANAGEMENT
###

### List Users
GET {{baseUrl}}/{{apiVersion}}/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Get User by ID
@userId = eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee
GET {{baseUrl}}/{{apiVersion}}/users/{{userId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Update User
PUT {{baseUrl}}/{{apiVersion}}/users/{{userId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "first_name": "Updated",
  "last_name": "Name"
}

### Create User (Admin only)
POST {{baseUrl}}/{{apiVersion}}/admin/users
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "Admin",
  "last_name": "Created"
}

###
### 5. USER-ORGANIZATIONS (Permission Management)
###

### List User-Organization Relationships
GET {{baseUrl}}/{{apiVersion}}/user-organizations
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Create User-Organization Relationship
POST {{baseUrl}}/{{apiVersion}}/user-organizations
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "user_id": "{{userId}}",
  "organization_id": "{{organizationId}}",
  "role": "editor",
  "resource_type": "product",
  "resource_scope": "specific",
  "resource_id": "product-123",
  "allowed_actions": ["read", "update"]
}

### Get User Permissions
GET {{baseUrl}}/{{apiVersion}}/user-organizations/user/{{userId}}/permissions
Authorization: Bearer {{adminToken}}
Content-Type: application/json

###
### 6. PRODUCTS MANAGEMENT
###

### List Products
GET {{baseUrl}}/{{apiVersion}}/products
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Create Product
POST {{baseUrl}}/{{apiVersion}}/products
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Test Product",
  "slug": "test-product",
  "description": "A test product for API testing",
  "organization_id": "{{organizationId}}"
}

### Get Product by ID
@productId = product-test-id
GET {{baseUrl}}/{{apiVersion}}/products/{{productId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Update Product
PUT {{baseUrl}}/{{apiVersion}}/products/{{productId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Updated Test Product",
  "description": "Updated description"
}

###
### 7. POLICIES MANAGEMENT
###

### List Policies
GET {{baseUrl}}/{{apiVersion}}/policies
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Create Policy
POST {{baseUrl}}/{{apiVersion}}/policies
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Test Policy",
  "slug": "test-policy",
  "description": "A test policy for API testing",
  "product_id": "{{productId}}",
  "scheme": "ED25519_SIGN",
  "require_heartbeat": true,
  "heartbeat_duration": 3600,
  "max_machines": 5,
  "max_users": 1,
  "duration": 86400
}

### Get Policy by ID
@policyId = policy-test-id
GET {{baseUrl}}/{{apiVersion}}/policies/{{policyId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Update Policy
PUT {{baseUrl}}/{{apiVersion}}/policies/{{policyId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Updated Test Policy",
  "max_machines": 10
}

###
### 8. LICENSES MANAGEMENT
###

### List Licenses
GET {{baseUrl}}/{{apiVersion}}/licenses
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Create License
POST {{baseUrl}}/{{apiVersion}}/licenses
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "policy_id": "{{policyId}}",
  "user_id": "{{userId}}",
  "name": "Test License",
  "key": "TEST-LICENSE-KEY-001",
  "expires_at": "2025-12-31T23:59:59Z"
}

### Get License by ID
@licenseId = license-test-id
GET {{baseUrl}}/{{apiVersion}}/licenses/{{licenseId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Update License
PUT {{baseUrl}}/{{apiVersion}}/licenses/{{licenseId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Updated Test License"
}

### Validate License (Authenticated)
POST {{baseUrl}}/{{apiVersion}}/licenses/{{licenseId}}/validate
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "fingerprint": "test-machine-fingerprint"
}

###
### 9. LICENSE VALIDATION (Public Endpoints)
### These endpoints are used by end-user software for license validation
###

### Validate License by Key (Public)
POST {{baseUrl}}/{{apiVersion}}/licenses/validate
X-License-Key: TEST-LICENSE-KEY-001
Content-Type: application/json

{
  "fingerprint": "end-user-machine-fingerprint",
  "metadata": {
    "version": "1.0.0",
    "platform": "linux"
  }
}

### Get License Info by Key (Public)
@licenseKey = TEST-LICENSE-KEY-001
GET {{baseUrl}}/{{apiVersion}}/licenses/key/{{licenseKey}}/info
X-License-Key: {{licenseKey}}
Content-Type: application/json

### Checkout License (Public)
POST {{baseUrl}}/{{apiVersion}}/licenses/checkout
X-License-Key: {{licenseKey}}
Content-Type: application/json

{
  "fingerprint": "end-user-machine-fingerprint",
  "metadata": {
    "version": "1.0.0",
    "platform": "linux",
    "hostname": "test-machine"
  }
}

###
### 10. MACHINES MANAGEMENT
###

### List Machines
GET {{baseUrl}}/{{apiVersion}}/machines
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Get Machine by ID
@machineId = machine-test-id
GET {{baseUrl}}/{{apiVersion}}/machines/{{machineId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

### Update Machine
PUT {{baseUrl}}/{{apiVersion}}/machines/{{machineId}}
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Updated Machine Name"
}

### Checkout Machine (Public)
POST {{baseUrl}}/{{apiVersion}}/machines/checkout
X-License-Key: {{licenseKey}}
Content-Type: application/json

{
  "fingerprint": "machine-fingerprint-001",
  "platform": "linux",
  "hostname": "test-machine",
  "cores": 4,
  "name": "Test Machine"
}

### Machine Heartbeat (Public)
POST {{baseUrl}}/{{apiVersion}}/machines/{{machineId}}/heartbeat
X-License-Key: {{licenseKey}}
Content-Type: application/json

{
  "status": "ALIVE",
  "metadata": {
    "cpu_usage": 45.2,
    "memory_usage": 78.5,
    "uptime": 3600
  }
}

### Machine Check-in (Public)
POST {{baseUrl}}/{{apiVersion}}/machines/{{machineId}}/check-in
X-License-Key: {{licenseKey}}
Content-Type: application/json

{
  "components": [
    {
      "name": "main-app",
      "fingerprint": "component-fp-001"
    }
  ],
  "processes": [
    {
      "pid": 1234,
      "name": "myapp"
    }
  ]
}

###
### 11. ERROR TESTING
###

### Test Authentication Required
GET {{baseUrl}}/{{apiVersion}}/organizations
Content-Type: application/json

### Test Invalid Token
GET {{baseUrl}}/{{apiVersion}}/organizations
Authorization: Bearer invalid-token
Content-Type: application/json

### Test Insufficient Permissions (using customer token)
@customerToken = PUT_YOUR_CUSTOMER_JWT_TOKEN_HERE
GET {{baseUrl}}/{{apiVersion}}/organizations
Authorization: Bearer {{customerToken}}
Content-Type: application/json

### Test Invalid JSON
POST {{baseUrl}}/{{apiVersion}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>"
  "password": "missing-comma"
}

### Test Missing Required Fields
POST {{baseUrl}}/{{apiVersion}}/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>"
}

### Test Invalid License Key
POST {{baseUrl}}/{{apiVersion}}/licenses/validate
X-License-Key: INVALID-LICENSE-KEY
Content-Type: application/json

{
  "fingerprint": "test-fingerprint"
}

###
### 12. DOCUMENTATION ENDPOINTS
###

### API Documentation (Swagger UI)
GET {{baseUrl}}/docs

### Swagger JSON
GET {{baseUrl}}/swagger/doc.json

###
### 13. BULK OPERATIONS (for testing multiple records)
###

### Create Multiple Organizations
POST {{baseUrl}}/{{apiVersion}}/organizations
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Organization 1",
  "slug": "org-1"
}

###

POST {{baseUrl}}/{{apiVersion}}/organizations
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Organization 2", 
  "slug": "org-2"
}

###

POST {{baseUrl}}/{{apiVersion}}/organizations
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "name": "Organization 3",
  "slug": "org-3"
}

###
### NOTES:
### 1. Update @adminToken, @customerToken variables with actual JWT tokens from login responses
### 2. Update @organizationId, @userId, @productId, @policyId, @licenseId, @machineId with actual IDs
### 3. Some endpoints may require specific data setup (like creating products before policies)
### 4. Public endpoints use X-License-Key header instead of Authorization Bearer token
### 5. Admin endpoints require admin-level permissions
### 6. Customer endpoints are accessible by users with appropriate constraints
### 7. Use VS Code REST Client extension or similar tools to execute these requests
### 8. For IntelliJ/WebStorm users: Install HTTP Client plugin and use .http files
###