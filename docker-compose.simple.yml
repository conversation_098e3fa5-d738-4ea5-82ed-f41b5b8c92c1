version: "3.8"

services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: gokeys-postgres
    environment:
      POSTGRES_DB: gokeys
      POSTGRES_USER: gokeys
      POSTGRES_PASSWORD: gokeys
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - gokeys
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gokeys -d gokeys"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Valkey Cache
  valkey:
    image: valkey/valkey:8.1-alpine
    container_name: gokeys-valkey
    command: >
      valkey-server
      --appendonly yes
      --requirepass gokeys
      --maxmemory 128mb
      --maxmemory-policy allkeys-lru
      --save ""
    ports:
      - "6379:6379"
    volumes:
      - valkey_data:/data
    networks:
      - gokeys
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "valkey-cli", "--raw", "incr", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 20s

volumes:
  postgres_data:
    driver: local
  valkey_data:
    driver: local

networks:
  gokeys:
    driver: bridge