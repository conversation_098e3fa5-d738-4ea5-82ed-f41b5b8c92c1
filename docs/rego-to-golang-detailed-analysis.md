# Phân Tích Chi T<PERSON>ết: Từ Rego File Đến Golang Code

## 📋 Mục Lục
1. [Rego File Structure](#1-rego-file-structure)
2. [Package Declaration Mapping](#2-package-declaration-mapping)
3. [Input Variable Mapping](#3-input-variable-mapping)
4. [Rules & Evaluation](#4-rules--evaluation)
5. [Functions & Helpers](#5-functions--helpers)
6. [Data Store Integration](#6-data-store-integration)
7. [Query Execution Flow](#7-query-execution-flow)
8. [Error Handling](#8-error-handling)

---

## 1. Rego File Structure

### 1.1 Main Policy File

**File: `policies/auth/main.rego`**
```rego
# 📦 PACKAGE DECLARATION
package gokeys.authz
#       ^^^^^^ ^^^^
#       |      └─ Submodule name
#       └─ Root namespace

import rego.v1

# 🚪 DEFAULT RULE (Entry Point)
default allow = false
#       ^^^^^ 
#       Rule name - sẽ được query từ Golang

# 🔧 COMPUTED VALUE
required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
}
#                                  ^^^^^ 
#                                  Input variable từ Golang

# 📋 CONDITIONAL RULES
allow if {
    required_permission in input.subject.permissions
}
#   ^^^^^^^^^^^^^^^^^ 
#   Reference đến computed value ở trên

# 🔨 HELPER FUNCTION
wildcard_match(pattern, permission) if {
    parts := split(pattern, ".")
    # ... logic
}
```

### 1.2 Golang Mapping

**File: `internal/domain/services/policy/opa_service.go`**
```go
func (s *SimpleOPAService) IsAllowed(ctx context.Context, input AuthorizationInput) (bool, error) {
    // 🔄 CONVERT STRUCT TO MAP
    inputMap := map[string]interface{}{
        "subject":  input.Subject,  // → input.subject trong Rego
        "resource": input.Resource, // → input.resource trong Rego
        "action":   input.Action,   // → input.action trong Rego
        "context":  input.Context,  // → input.context trong Rego
    }
    
    // 🎯 QUERY SPECIFIC RULE
    return s.Evaluate(ctx, "data.gokeys.authz.allow", inputMap)
    //                      ^^^^^^^^^^^^^^^^^^^^^^^^
    //                      package.rule_name
}
```

---

## 2. Package Declaration Mapping

### 2.1 Rego Package

```rego
# File: policies/auth/main.rego
package gokeys.authz
#       ^^^^^^ ^^^^
#       |      └─ Submodule (tự do đặt tên)
#       └─ Root namespace (convention)
```

### 2.2 Golang Query Path

```go
// File: internal/domain/services/policy/opa_service.go
query := "data.gokeys.authz.allow"
//        ^^^^ ^^^^^^ ^^^^ ^^^^^
//        |    |      |    └─ Rule name
//        |    |      └─ Submodule từ package
//        |    └─ Root namespace từ package
//        └─ OPA prefix (luôn là "data")
```

### 2.3 Mapping Rules

| Rego | Golang Query |
|------|--------------|
| `package gokeys.authz` | `data.gokeys.authz` |
| `package myapp.admin` | `data.myapp.admin` |
| `package company.security.rbac` | `data.company.security.rbac` |

**🔍 Pattern:**
```
package A.B.C  →  data.A.B.C
```

---

## 3. Input Variable Mapping

### 3.1 Golang Input Structure

**File: `internal/domain/services/policy/opa_service.go`**
```go
type AuthorizationInput struct {
    Subject  Subject                `json:"subject"`   
    Resource Resource               `json:"resource"`  
    Action   string                 `json:"action"`    
    Context  map[string]interface{} `json:"context"`   
}

type Subject struct {
    ID             string   `json:"id"`              
    Type           string   `json:"type"`            
    OrganizationID string   `json:"organization_id"` 
    Permissions    []string `json:"permissions"`     
}

type Resource struct {
    Type           string                 `json:"type"`            
    ID             string                 `json:"id"`              
    OrganizationID string                 `json:"organization_id"` 
    OwnerID        string                 `json:"owner_id"`        
    Attributes     map[string]interface{} `json:"attributes"`      
}
```

### 3.2 Golang → JSON Conversion

```go
// File: internal/domain/services/policy/opa_service.go
inputMap := map[string]interface{}{
    "subject": Subject{
        ID: "user-123",
        Type: "user",
        OrganizationID: "org-456", 
        Permissions: ["license.read", "machine.create"],
    },
    "resource": Resource{
        Type: "license",
        ID: "license-789",
        OrganizationID: "org-456",
        OwnerID: "user-123",
    },
    "action": "read",
    "context": map[string]interface{}{
        "request_time": "2024-01-15T10:30:00Z",
        "user_agent": "GoKeys/1.0",
    },
}
```

### 3.3 JSON → Rego Input Variable

**OPA Engine tự động convert JSON thành Rego `input` variable:**

```rego
# Trong Rego, input tự động available:
input.subject.id                    # "user-123"
input.subject.type                  # "user" 
input.subject.organization_id       # "org-456"
input.subject.permissions           # ["license.read", "machine.create"]

input.resource.type                 # "license"
input.resource.id                   # "license-789"
input.resource.organization_id      # "org-456"
input.resource.owner_id             # "user-123"

input.action                        # "read"

input.context.request_time          # "2024-01-15T10:30:00Z"
input.context.user_agent           # "GoKeys/1.0"
```

### 3.4 Field Access Examples

```rego
# File: policies/auth/main.rego

# ✅ VALID ACCESS PATTERNS
user_id := input.subject.id
user_permissions := input.subject.permissions
resource_type := input.resource.type
current_action := input.action

# ✅ CONDITIONAL ACCESS
resource_owner := input.resource.owner_id if {
    input.resource.owner_id  # Check field exists
} else := ""

# ✅ ARRAY/SET OPERATIONS
"license.read" in input.subject.permissions
count(input.subject.permissions) > 0

# ✅ NESTED ACCESS
request_time := input.context.request_time
```

---

## 4. Rules & Evaluation

### 4.1 Rule Types trong Rego

#### A. Default Rule
```rego
# File: policies/auth/main.rego
default allow = false
#       ^^^^^ ^^^^^
#       |     └─ Default value
#       └─ Rule name
```

**Golang query:**
```go
result := opa.Evaluate("data.gokeys.authz.allow", input)
// Nếu không có rule nào match → return false (default value)
```

#### B. Conditional Rules
```rego
# Rule với condition
allow if {
    "*" in input.subject.permissions
}
#^^^^
#Rule name (same as default)

# Multiple conditions (AND logic)
allow if {
    input.action == "read"                    # Condition 1
    input.resource.type == "license"          # Condition 2  
    "license.read" in input.subject.permissions # Condition 3
}

# OR logic (multiple rules with same name)
allow if { condition_1 }
allow if { condition_2 }  
allow if { condition_3 }
```

#### C. Computed Values
```rego
# Computed value - không phải rule
required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
}
#^^^^^^^^^^^^^^^^^^^ ^^^^^^^^^
#Variable name       │Condition

# Usage trong rule khác
allow if {
    required_permission in input.subject.permissions
    #^^^^^^^^^^^^^^^^^^
    #Reference computed value
}
```

### 4.2 Evaluation Logic

**Input Example:**
```json
{
    "subject": {
        "permissions": ["license.read", "machine.create"]
    },
    "resource": {
        "type": "license"
    },
    "action": "read"
}
```

**Rego Evaluation Steps:**

**Step 1: Computed Values**
```rego
required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
}
# sprintf("%s.%s", ["license", "read"]) = "license.read"
# => required_permission = "license.read"
```

**Step 2: Rule Evaluation (thứ tự xuất hiện)**
```rego
# Rule 1: Admin check
allow if {
    "*" in input.subject.permissions
}
# "*" in ["license.read", "machine.create"] => FALSE

# Rule 2: Exact permission match
allow if {
    required_permission in input.subject.permissions  
}
# "license.read" in ["license.read", "machine.create"] => TRUE
# => allow = true (STOP EVALUATION)
```

**Final Result:** `allow = true`

### 4.3 Golang Evaluation

```go
// File: internal/domain/services/policy/opa_service.go
func (s *SimpleOPAService) Evaluate(ctx context.Context, query string, input map[string]interface{}) (bool, error) {
    // query = "data.gokeys.authz.allow"
    // input = {"subject": {...}, "resource": {...}, "action": "read"}
    
    result, err := s.EvaluateWithResult(ctx, query, input)
    if err != nil {
        return false, err
    }
    
    // Convert result to boolean
    switch v := result.(type) {
    case bool:
        return v, nil                    // Direct boolean result
    case []interface{}:
        return len(v) > 0, nil          // Array: true if non-empty
    default:
        return false, nil               // Other types: false
    }
}
```

---

## 5. Functions & Helpers

### 5.1 Helper Function Definition

```rego
# File: policies/auth/main.rego

# 🔧 FUNCTION DEFINITION
wildcard_match(pattern, permission) if {
#             ^^^^^^^ ^^^^^^^^^^
#             Param 1 Param 2
    parts := split(pattern, ".")
    perm_parts := split(permission, ".")
    
    # Same length required
    count(parts) == count(perm_parts)
    
    # Check each part
    every i, part in parts {
        part == "*" or part == perm_parts[i]
    }
}
```

### 5.2 Function Usage

```rego
# 📞 FUNCTION CALL trong rule
allow if {
    some permission in input.subject.permissions
    wildcard_match(permission, required_permission)
    #             ^^^^^^^^^^^ ^^^^^^^^^^^^^^^^^^^
    #             Arg 1       Arg 2
}
```

### 5.3 Function Evaluation Example

**Input:**
```json
{
    "subject": {
        "permissions": ["license.*", "machine.read"]
    },
    "resource": {"type": "license"},
    "action": "create"
}
```

**Evaluation:**
```rego
# Step 1: Compute required_permission
required_permission = "license.create"

# Step 2: Check each permission
# permission = "license.*"
wildcard_match("license.*", "license.create"):
    parts = ["license", "*"]
    perm_parts = ["license", "create"]
    count(parts) == count(perm_parts)  # 2 == 2 => TRUE
    
    # Check each part:
    i=0: parts[0] == perm_parts[0]     # "license" == "license" => TRUE
    i=1: parts[1] == "*"               # "*" wildcard => TRUE
    
    # All checks pass => wildcard_match = TRUE

# Step 3: Rule evaluation
allow if {
    some permission in input.subject.permissions      # TRUE (found "license.*")
    wildcard_match(permission, required_permission)   # TRUE
}
# => allow = TRUE
```

### 5.4 Golang Function Interaction

```go
// Golang KHÔNG trực tiếp call Rego functions
// Functions chỉ được dùng TRONG Rego evaluation

// Golang chỉ call top-level rules:
result := opa.Evaluate("data.gokeys.authz.allow", input)
//                       ^^^^^^^^^^^^^^^^^^^^^^^^
//                       Rule name, NOT function name

// ❌ KHÔNG THỂ làm:
// result := opa.Evaluate("data.gokeys.authz.wildcard_match", {...})
```

---

## 6. Data Store Integration

### 6.1 Data Loading (Golang)

**File: `internal/domain/services/policy/opa_manager.go`**
```go
func (m *OPAManager) loadPermissionData(ctx context.Context) error {
    // 📂 READ JSON FILE
    dataFile := filepath.Join(m.config.DataPath, "permissions.json")
    data, err := os.ReadFile(dataFile)
    
    // 🔄 PARSE JSON
    var permissions map[string]interface{}
    json.Unmarshal(data, &permissions)
    
    // 💾 STORE IN OPA
    return m.service.UpdateData(ctx, "/permissions", permissions)
    //                              ^^^^^^^^^^^^^
    //                              Path trong OPA data store
}
```

### 6.2 JSON Data File

**File: `policies/data/permissions.json`**
```json
{
    "token_type_permissions": {
        "environment": [
            "account.read",
            "license.create", 
            "license.read",
            "license.update",
            "license.delete"
        ],
        "user": [
            "account.read",
            "license.read",
            "license.validate"
        ],
        "admin": ["*"]
    },
    "resource_hierarchy": {
        "license": {
            "parent": "product",
            "children": ["machine", "user"]
        }
    }
}
```

### 6.3 Data Access trong Rego

```rego
# File: policies/auth/main.rego

# 📊 ACCESS DATA STORE
env_permissions := data.permissions.token_type_permissions.environment
#                  ^^^^ ^^^^^^^^^^^
#                  |    └─ Path trong UpdateData()
#                  └─ OPA data prefix

user_permissions := data.permissions.token_type_permissions.user
admin_permissions := data.permissions.token_type_permissions.admin

# 🔍 CHECK DATA EXISTENCE
valid_token_type if {
    data.permissions.token_type_permissions[input.subject.type]
}

# 🎯 USE DATA trong RULES
allow if {
    required_permissions := data.permissions.token_type_permissions[input.subject.type]
    some perm in required_permissions
    perm == required_permission
}
```

### 6.4 UpdateData Mapping

| Golang Code | Rego Access |
|-------------|-------------|
| `UpdateData("/permissions", data)` | `data.permissions` |
| `UpdateData("/roles", roles)` | `data.roles` |
| `UpdateData("/config/settings", cfg)` | `data.config.settings` |

**🔍 Pattern:**
```
UpdateData("PATH", value)  →  data.PATH trong Rego
```

---

## 7. Query Execution Flow

### 7.1 Complete Golang → Rego Flow

**File: `internal/domain/services/policy/opa_service.go`**
```go
func (s *SimpleOPAService) EvaluateWithResult(ctx context.Context, query string, input map[string]interface{}) (interface{}, error) {
    // 🏗️ BUILD REGO INSTANCE
    regoOptions := []func(*rego.Rego){
        rego.Query(query),          // "data.gokeys.authz.allow"
        rego.Store(s.store),        // Data store với permissions
        rego.Input(input),          // Input data
    }
    
    // 📜 ADD LOADED POLICIES  
    for name, policy := range s.policies {
        regoOptions = append(regoOptions, rego.Module(name, policy))
        // rego.Module("main", "package gokeys.authz\ndefault allow = false\n...")
    }
    
    // ⚡ CREATE & EVALUATE
    r := rego.New(regoOptions...)
    rs, err := r.Eval(ctx)
    
    // 📤 EXTRACT RESULT
    var result interface{} = false
    if len(rs) > 0 && len(rs[0].Expressions) > 0 {
        result = rs[0].Expressions[0].Value
    }
    
    return result, nil
}
```

### 7.2 Rego Engine Processing

**Internal OPA Steps:**

1. **Parse Query**: `"data.gokeys.authz.allow"` 
   - Namespace: `gokeys.authz`
   - Rule: `allow`

2. **Load Context**:
   - Input: Available as `input` variable
   - Data: Available as `data` variable  
   - Policies: Loaded modules

3. **Evaluate Rules**:
   - Find all `allow` rules trong `gokeys.authz` package
   - Evaluate conditions trong order

4. **Return Result**:
   - Nếu ANY rule returns `true` → `allow = true`
   - Nếu NO rule returns `true` → use default value
   - Nếu không có default → `undefined`

### 7.3 Query Resolution

**Query:** `"data.gokeys.authz.allow"`

**Resolution Steps:**
1. `data` → OPA data store root
2. `gokeys` → Namespace level 1  
3. `authz` → Namespace level 2
4. `allow` → Rule name

**Match với:**
```rego
package gokeys.authz  # ← Namespace match
default allow = false # ← Rule name match
allow if { ... }      # ← Rule name match
```

### 7.4 Multiple Query Examples

```go
// Different queries for different rules
adminCheck := opa.Evaluate("data.gokeys.authz.is_admin", input)
ownerCheck := opa.Evaluate("data.gokeys.authz.is_owner", input)
debugInfo := opa.Evaluate("data.gokeys.authz.debug_info", input)

// Computed values
permission := opa.Evaluate("data.gokeys.authz.required_permission", input)
```

**Corresponding Rego:**
```rego
package gokeys.authz

is_admin if { "*" in input.subject.permissions }
is_owner if { input.resource.owner_id == input.subject.id }
debug_info := {"user": input.subject.id, "action": input.action}
required_permission = sprintf("%s.%s", [input.resource.type, input.action])
```

---

## 8. Error Handling

### 8.1 Rego Evaluation Errors

**Common Error Cases:**

```rego
# ❌ UNDEFINED VARIABLE
allow if {
    undefined_variable == "value"  # Error: undefined variable
}

# ❌ TYPE MISMATCH  
allow if {
    input.subject.permissions + 1  # Error: can't add number to array
}

# ❌ INVALID FUNCTION CALL
allow if {
    split(input.subject.permissions, ".")  # Error: first arg must be string
}
```

### 8.2 Golang Error Handling

```go
// File: internal/domain/services/policy/opa_service.go
func (s *SimpleOPAService) EvaluateWithResult(ctx context.Context, query string, input map[string]interface{}) (interface{}, error) {
    r := rego.New(regoOptions...)
    
    // ⚡ EVALUATION - có thể error
    rs, err := r.Eval(ctx)
    if err != nil {
        return nil, fmt.Errorf("failed to evaluate query: %w", err)
        //           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        //           Wrap OPA error với context
    }
    
    // 📤 RESULT EXTRACTION - safe
    var result interface{} = false
    if len(rs) > 0 && len(rs[0].Expressions) > 0 {
        result = rs[0].Expressions[0].Value
    }
    
    return result, nil
}
```

### 8.3 Input Validation

**Defensive Rego:**
```rego
# File: policies/auth/main.rego

# ✅ SAFE ACCESS với default values
user_id := input.subject.id if {
    input.subject.id
} else := "unknown"

user_permissions := input.subject.permissions if {
    input.subject.permissions
} else := []

# ✅ VALIDATE REQUIRED FIELDS
valid_input if {
    input.subject.id        # Required field
    input.resource.type     # Required field  
    input.action           # Required field
}

# ✅ USE VALIDATION trong RULES
allow if {
    valid_input
    required_permission in input.subject.permissions
}
```

**Corresponding Golang Validation:**
```go
// File: internal/adapters/http/middleware/authorization.go
func (am *AuthorizationMiddleware) buildOPAInput(c *gin.Context) (policy.AuthorizationInput, error) {
    userID, err := GetUserID(c)
    if err != nil {
        return policy.AuthorizationInput{}, fmt.Errorf("user ID required: %w", err)
    }
    
    permissions, err := GetPermissions(c)
    if err != nil {
        return policy.AuthorizationInput{}, fmt.Errorf("permissions required: %w", err)
    }
    
    // ✅ VALIDATE REQUIRED FIELDS
    if userID == "" {
        return policy.AuthorizationInput{}, fmt.Errorf("user ID cannot be empty")
    }
    
    // ✅ PROVIDE DEFAULTS
    if permissions == nil {
        permissions = []string{} // Empty slice thay vì nil
    }
    
    return policy.AuthorizationInput{...}, nil
}
```

---

## 🎯 Summary: Rego ↔ Golang Mapping

### **Package & Namespace**
```
Rego:    package gokeys.authz
Golang:  "data.gokeys.authz.RULE"
```

### **Input Variables**
```
Golang:  map[string]interface{}{"subject": {...}}
Rego:    input.subject
```

### **Data Store**
```
Golang:  UpdateData("/permissions", data)
Rego:    data.permissions
```

### **Rule Evaluation**
```
Rego:    allow if { condition }
Golang:  opa.Evaluate("data.gokeys.authz.allow", input) → bool
```

### **Function Calls**
```
Rego:    wildcard_match(pattern, permission)
Golang:  ❌ Không thể call trực tiếp (chỉ call rules)
```

### **Error Handling**
```
Rego:    Runtime errors trong evaluation
Golang:  Wrap errors với context information
```

**Key Insight:** Rego và Golang communicate qua **structured data** (JSON) và **naming conventions**, không phải direct function calls! 🎯