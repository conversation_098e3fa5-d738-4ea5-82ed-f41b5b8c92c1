# Flexible Authorization Architecture với OPA Integration

## Tổng quan

Hệ thống authorization đã được thiết kế lại để đáp ứng các yêu cầu phức tạp của doanh nghiệp với khả năng mở rộng và linh hoạt cao.

### Cá<PERSON> tính năng chính:

1. **Role-Based Access Control (RBAC)**
   - Roles có thể định nghĩa ở nhiều scope: System, Organization, Custom
   - Hỗ trợ role inheritance và composition
   - Dynamic role assignment với expiration

2. **Permission Delegation**
   - Users có thể delegate permissions cho người khác
   - <PERSON><PERSON><PERSON> soát delegation depth để tránh lạm dụng
   - Audit trail cho mọi delegation

3. **Multi-Organization Support**
   - User có thể có roles khác nhau trong các orgs khác nhau
   - Cross-organization permissions cho specific resources
   - Organization isolation với shared resources

4. **OPA Integration**
   - Policy as Code với Rego
   - Real-time policy evaluation
   - Centralized policy management

## Database Schema

### 1. Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    scope VARCHAR(50) NOT NULL, -- 'system', 'organization', 'custom'
    organization_id UUID, -- NULL for system roles
    permissions JSONB, -- Array of permission keys
    conditions JSONB, -- Time-based, IP-based conditions
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE
);
```

### 2. User_Roles Table
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    role_id UUID NOT NULL,
    organization_id UUID, -- Context for role
    granted_by UUID NOT NULL,
    granted_at TIMESTAMP,
    expires_at TIMESTAMP,
    conditions JSONB,
    reason TEXT
);
```

### 3. Enhanced Permissions Table
```sql
ALTER TABLE permissions ADD:
    delegation_allowed BOOLEAN DEFAULT FALSE,
    delegated_from UUID,
    max_delegation_depth INTEGER DEFAULT 0,
    conditions JSONB,
    reason TEXT
```

## Permission Hierarchy

### 1. System Level (S)
- `S:*:*` - Full system access
- `S:license:read` - Read all licenses system-wide

### 2. Organization Level (O:uuid)
- `O:org-123:*:*` - Full access to organization
- `O:org-123:product:create` - Create products in org

### 3. Resource Level (R)
- `R:license:read` - Read specific licenses
- Resource IDs constraint: `["license-1", "license-2"]`

## Use Cases

### 1. System Admin
```json
{
  "role": "system_admin",
  "permissions": ["S:*:*"]
}
```

### 2. Organization Admin (nhiều orgs)
```json
{
  "user_id": "user-123",
  "roles": [
    {
      "role": "org_admin",
      "organization_id": "org-1",
      "permissions": ["O:org-1:*:*"]
    },
    {
      "role": "org_admin", 
      "organization_id": "org-2",
      "permissions": ["O:org-2:*:*"]
    }
  ]
}
```

### 3. User không thuộc org với quyền cụ thể
```json
{
  "user_id": "external-user",
  "permissions": [
    {
      "key": "R:product:read",
      "resource_ids": ["product-abc", "product-xyz"]
    }
  ]
}
```

### 4. Permission Delegation
```json
{
  "delegator": "admin-user",
  "delegatee": "team-member",
  "permission": "O:org-123:license:*",
  "resource_ids": ["license-1", "license-2"],
  "delegation_allowed": true,
  "max_delegation_depth": 1,
  "expires_at": "2024-12-31"
}
```

## OPA Policy Examples

### 1. Basic Permission Check
```rego
allow if {
    some perm in input.user.permissions
    perm.key == "S:*:*"  # System admin
}

allow if {
    some role in input.user.roles
    role.name == "org_admin"
    role.organization_id == input.resource.organization_id
}
```

### 2. Delegation Check
```rego
allow if {
    some perm in input.user.permissions
    perm.delegation_allowed == true
    perm.delegated_from != null
    delegation_valid(perm)
}

delegation_valid(perm) if {
    time.parse_rfc3339_ns(perm.expires_at) > time.now_ns()
}
```

### 3. Conditional Access
```rego
allow if {
    some perm in input.user.permissions
    permission_matches(perm)
    conditions_met(perm.conditions)
}

conditions_met(conditions) if {
    ip_allowed(conditions.allowed_ips)
    time_allowed(conditions.valid_hours)
}
```

## API Endpoints

### Role Management
- `POST /api/v1/admin/roles` - Create role
- `PUT /api/v1/admin/roles/{id}` - Update role
- `DELETE /api/v1/admin/roles/{id}` - Delete role
- `POST /api/v1/admin/roles/{id}/assign` - Assign role to user
- `DELETE /api/v1/admin/roles/{id}/revoke` - Revoke role from user

### Permission Delegation
- `POST /api/v1/permissions/delegate` - Delegate permission
- `GET /api/v1/permissions/delegated` - List delegated permissions
- `DELETE /api/v1/permissions/delegate/{id}` - Revoke delegation

### OPA Integration
- `POST /api/v1/permissions/evaluate` - Evaluate permission via OPA
- `PUT /api/v1/admin/policies/{name}` - Update OPA policy
- `GET /api/v1/admin/policies` - List policies

## Implementation Status

### ✅ Completed:
1. Database migrations với roles và enhanced permissions
2. Role, UserRole, và enhanced Permission entities
3. Repository interfaces cho tất cả entities
4. Enhanced PermissionService với role support
5. OPA integration service
6. Comprehensive documentation

### 🔄 Pending:
1. PostgreSQL implementation của repositories
2. API handlers cho role management
3. API handlers cho permission delegation
4. Integration tests
5. Performance optimization

## Performance Considerations

1. **Caching Strategy**
   - Cache user's effective permissions với TTL
   - Invalidate on role/permission changes
   - Pre-compute permission sets cho common queries

2. **Database Optimization**
   - Composite indexes on (user_id, organization_id)
   - Partial indexes cho active records
   - Materialized views cho complex permission queries

3. **OPA Optimization**
   - Bundle policies for faster evaluation
   - Data replication to OPA for local evaluation
   - Connection pooling for OPA requests

## Security Considerations

1. **Audit Trail**
   - Log mọi permission changes
   - Track delegation chain
   - Monitor abnormal access patterns

2. **Delegation Controls**
   - Max delegation depth
   - Time-based expiration
   - Resource constraints

3. **Separation of Duties**
   - System admin không tự động có org permissions
   - Org admin không thể modify system roles
   - Delegation requires explicit permission

## Migration Path

1. **Phase 1**: Deploy new schema
2. **Phase 2**: Migrate existing permissions to new format
3. **Phase 3**: Enable role-based access
4. **Phase 4**: Enable delegation features
5. **Phase 5**: Full OPA integration

Hệ thống này cung cấp flexibility cần thiết cho authorization phức tạp trong khi vẫn maintain performance và security.