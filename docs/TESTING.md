# GoKeys Testing Guide

This guide explains how to set up and run tests for the GoKeys project.

## Test Environment Setup

### Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- Go 1.21+ installed
- Make utility

### Quick Start

1. **Start test dependencies:**
   ```bash
   make test-deps-up
   # or
   ./scripts/start-test-env.sh
   ```

2. **Run tests:**
   ```bash
   make test-handlers           # Handler tests
   make test-handlers-integration  # Integration tests
   make test-with-deps          # All tests with dependencies
   ```

3. **Stop test dependencies:**
   ```bash
   make test-deps-down
   # or
   ./scripts/stop-test-env.sh
   ```

## Test Dependencies

The test environment includes:

### PostgreSQL
- **Host:** localhost:5432
- **Database:** gokeys_test
- **User:** gokeys
- **Password:** gokeys_test_password
- **Connection String:** `postgres://gokeys:gokeys_test_password@localhost:5432/gokeys_test`

### Valkey (Redis-compatible)
- **Host:** localhost:6379
- **Password:** gokeys_test_password
- **Connection String:** `valkey://localhost:6379`

### pgAdmin (Optional)
- **URL:** http://localhost:8080
- **Email:** <EMAIL>
- **Password:** admin123

## Available Make Commands

### Test Dependencies
```bash
make test-deps-up       # Start PostgreSQL and Valkey
make test-deps-down     # Stop test dependencies
make test-deps-logs     # Show service logs
make test-deps-clean    # Remove all containers and volumes
make test-deps-status   # Check service status
make test-deps-admin    # Start pgAdmin
```

### Testing
```bash
make test                    # Run all tests (unit tests only)
make test-handlers           # Run handler tests with database
make test-handlers-integration  # Run integration tests
make test-with-deps          # Run all tests with dependencies
make test-coverage           # Run tests with coverage report
```

## Test Types

### Unit Tests
- Run without external dependencies
- Fast execution
- Mock external services
- Command: `go test ./... -short`

### Integration Tests
- Require PostgreSQL and Valkey
- Test real HTTP endpoints
- Test database interactions
- Command: `go test ./internal/adapters/http/handlers/ -run Integration`

### Handler Tests
- Test HTTP handlers with real database
- Test Go-style request/response structures
- Test validation and error handling
- Command: `go test ./internal/adapters/http/handlers/`

## Integration Test Structure

The integration tests use a real HTTP server setup:

```go
// Create test server with real HTTP endpoints
testServer := NewIntegrationTestServer(serviceCoordinator)
defer testServer.Close()

// Make real HTTP requests
resp, err := testServer.makeRequest("POST", "/api/v1/accounts", createReq)
```

### Available Endpoints in Test Server
- `POST /api/v1/accounts` - Create account
- `GET /api/v1/accounts/:id` - Get account
- `POST /api/v1/products` - Create product
- `GET /api/v1/products/:id` - Get product
- `POST /api/v1/policies` - Create policy
- `GET /api/v1/policies/:id` - Get policy
- `POST /api/v1/licenses` - Create license
- `GET /api/v1/licenses/:id` - Get license
- `POST /api/v1/licenses/validate` - Validate license

## Test Workflow Example

```bash
# 1. Start test environment
make test-deps-up

# 2. Run specific tests
go test ./internal/adapters/http/handlers/ -v -run TestHTTPIntegrationDemo

# 3. Run integration tests
make test-handlers-integration

# 4. Check service status
make test-deps-status

# 5. View logs if needed
make test-deps-logs

# 6. Stop when done
make test-deps-down
```

## Troubleshooting

### Services won't start
```bash
# Check Docker status
docker info

# Check service logs
make test-deps-logs

# Clean and restart
make test-deps-clean
make test-deps-up
```

### Database connection issues
```bash
# Check PostgreSQL is ready
docker-compose -f docker-compose.test.yml exec postgres-test pg_isready -U gokeys -d gokeys_test

# Check connection manually
psql postgres://gokeys:gokeys_test_password@localhost:5432/gokeys_test
```

### Port conflicts
If ports 5432 or 6379 are already in use, modify `docker-compose.test.yml`:
```yaml
ports:
  - "15432:5432"  # Use different external port
```

## CI/CD Integration

For CI environments, use:
```bash
# Start services in background
make test-deps-up

# Wait for services to be ready
sleep 10

# Run tests
make test-with-deps

# Cleanup
make test-deps-clean
```

## Performance Tips

- Use `make test-deps-status` to check if services are already running
- Keep services running between test runs for faster development
- Use `make test-deps-clean` only when you need to reset data
- Use `make test-handlers` for faster handler-only testing
