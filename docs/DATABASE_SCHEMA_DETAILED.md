# Database Schema Documentation - GoKeys License Management System

## Overview

This document provides detailed documentation for the GoKeys license management system database schema. The schema is designed to support a comprehensive license management platform with multi-tenant architecture, cryptographic security, and flexible policy management.

## Schema Relationship Diagram

```mermaid
erDiagram
    organizations
    users
    products
    policies
    licenses
    machines
    sessions
    api_tokens
    users_organizations
    permissions

    organizations ||--o{ products : "has"
    organizations ||--o{ policies : "has"
    organizations ||--o{ licenses : "has"
    organizations ||--o{ api_tokens : "has"
    organizations ||--|{ users_organizations : "links"

    users ||--o{ sessions : "has"
    users ||--o{ api_tokens : "has"
    users ||--o{ licenses : "owns"
    users ||--o{ machines : "owns"
    users ||--|{ users_organizations : "links"
    users ||--o{ permissions : "granted"

    products ||--o{ policies : "has"
    products ||--o{ licenses : "has"

    policies ||--o{ licenses : "governs"
    policies ||--o{ machines : "governs"

    licenses ||--o{ machines : "activates"
```

## Database Extensions

```sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

- **uuid-ossp**: Enables UUID generation functions for primary keys

## Core Tables

### 1. Organizations Table

The `organizations` table represents vendors, resellers, and enterprise customers in the system.

```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) DEFAULT 'vendor' CHECK (type IN ('vendor', 'reseller', 'customer')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'canceled')),
    protected BOOLEAN DEFAULT FALSE,
    public_key TEXT,
    private_key TEXT,
    secret_key TEXT,
    ed25519_private_key TEXT,
    ed25519_public_key TEXT,
    max_users INTEGER,
    max_licenses INTEGER,
    max_machines INTEGER,
    settings JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique identifier for the organization | Auto-generated |
| **name** | VARCHAR(255) | Display name of the organization | Required |
| **slug** | VARCHAR(255) | URL-friendly unique identifier | Unique, required |
| **email** | VARCHAR(255) | Primary contact email | Unique, required |
| **type** | VARCHAR(50) | Organization classification | `vendor`, `reseller`, `customer` |
| **status** | VARCHAR(50) | Current operational status | `active`, `suspended`, `canceled` |
| **protected** | BOOLEAN | System flag to prevent accidental deletion | Default: FALSE |
| **public_key** | TEXT | RSA public key for cryptographic operations | Nullable |
| **private_key** | TEXT | RSA private key (encrypted at rest) | Nullable, encrypted |
| **secret_key** | TEXT | 128-character hex secret for symmetric encryption | Nullable |
| **ed25519_private_key** | TEXT | Ed25519 private key for modern cryptographic signing | Nullable, encrypted |
| **ed25519_public_key** | TEXT | Ed25519 public key for signature verification | Nullable |
| **max_users** | INTEGER | Maximum users allowed in organization | Nullable (unlimited) |
| **max_licenses** | INTEGER | Maximum licenses this organization can create | Nullable (unlimited) |
| **max_machines** | INTEGER | Maximum machines that can be activated | Nullable (unlimited) |
| **settings** | JSONB | JSON configuration specific to organization | Default: '{}' |
| **metadata** | JSONB | Additional flexible data storage | Default: '{}' |
| **created_at** | TIMESTAMP | When the organization was created | Auto-generated |
| **updated_at** | TIMESTAMP | Timestamp of last modification | Auto-updated |
| **deleted_at** | TIMESTAMP | Soft deletion timestamp | Nullable |

### 2. Users Table

The `users` table stores individual user accounts that can optionally belong to organizations.

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    totp_secret TEXT,
    totp_enabled BOOLEAN DEFAULT FALSE,
    password_reset_token VARCHAR(255),
    password_reset_expires_at TIMESTAMP WITH TIME ZONE,
    banned_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique identifier for the user | Auto-generated |
| **email** | VARCHAR(255) | User's email address | Unique, required |
| **password** | VARCHAR(255) | Hashed password for authentication | Required |
| **first_name** | VARCHAR(255) | User's first name | Optional |
| **last_name** | VARCHAR(255) | User's last name | Optional |
| **status** | VARCHAR(50) | User account status | `active`, `inactive`, `suspended` |
| **totp_secret** | TEXT | Time-based One-Time Password secret for 2FA | Nullable |
| **totp_enabled** | BOOLEAN | Whether 2FA is enabled for this user | Default: FALSE |
| **password_reset_token** | VARCHAR(255) | Token for password reset functionality | Nullable |
| **password_reset_expires_at** | TIMESTAMP | Expiration time for password reset token | Nullable |
| **banned_at** | TIMESTAMP | Timestamp when user was banned | Nullable |
| **last_login** | TIMESTAMP | Timestamp of user's last successful login | Nullable |
| **metadata** | JSONB | Additional flexible data storage for user preferences | Default: '{}' |
| **created_at** | TIMESTAMP | When the user account was created | Auto-generated |
| **updated_at** | TIMESTAMP | Timestamp of last modification | Auto-updated |
| **deleted_at** | TIMESTAMP | Soft deletion timestamp | Nullable |

### 3. Users Organizations Table

The `users_organizations` table represents simple membership links between users and organizations. Permission scopes and actions are now modeled separately in the `permissions` table.

```sql
CREATE TABLE users_organizations (
    user_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    invited_by UUID,
    PRIMARY KEY (user_id, organization_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **user_id** | UUID | Reference to the user | FK to users |
| **organization_id** | UUID | Reference to the organization | FK to organizations |
| **joined_at** | TIMESTAMP | When the user joined the organization | Auto-generated |
| **invited_by** | UUID | User who invited this member | FK to users |

### 4. Permissions Table

The `permissions` table stores resource-based permission records and replaces the role/scope columns previously embedded in join tables. Each permission *belongs* to a target **user_id** and is *granted* by another **granted_by** user, enabling clear audit trails.

```sql
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    scope VARCHAR(255) NOT NULL, -- "system", "org:<id>", "resource:type:id", "owner"
    resource_type VARCHAR(50) NOT NULL, -- "product", "license", "user", "*"
    actions TEXT[] NOT NULL, -- ["create", "read", "update", "delete"] or ["*"]
    granted_by UUID,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE (user_id, scope, resource_type),
    CONSTRAINT valid_scope CHECK (
        scope = 'system' OR scope = 'owner' OR scope LIKE 'org:%' OR scope LIKE 'resource:%:%'
    ),
    CONSTRAINT valid_resource_type CHECK (
        resource_type IN ('*', 'organization', 'product', 'policy', 'license', 'machine', 'user', 'api_token', 'session')
    ),
    CONSTRAINT valid_actions CHECK (array_length(actions, 1) > 0),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique permission record identifier | Auto-generated |
| **user_id** | UUID | User the permission applies to | FK to users |
| **scope** | VARCHAR(255) | Scope in which permission is valid | Required |
| **resource_type** | VARCHAR(50) | Type of resource | Required |
| **actions** | TEXT[] | Allowed actions or `*` | Required |
| **granted_by** | UUID | User who granted the permission | FK to users |
| **granted_at** | TIMESTAMP | When permission was granted | Auto-generated |
| **expires_at** | TIMESTAMP | Expiration time (optional) | Nullable |

### 5. Products Table

The `products` table stores information about software products that can be licensed.

```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    description TEXT,
    url VARCHAR(255),
    distribution_strategy VARCHAR(255),
    platforms TEXT[],
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE (organization_id, code)
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique identifier for the product | Auto-generated |
| **organization_id** | UUID | Reference to the organization that owns this product | Required, FK to organizations |
| **name** | VARCHAR(255) | Display name of the product | Required |
| **code** | VARCHAR(255) | Unique code identifier within the organization | Required, unique per org |
| **description** | TEXT | Detailed description of the product | Optional |
| **url** | VARCHAR(255) | Product website or documentation URL | Optional |
| **distribution_strategy** | VARCHAR(255) | How the product is distributed | e.g., 'download', 'cloud', 'hybrid' |
| **platforms** | TEXT[] | Array of supported platforms | e.g., ['windows', 'linux', 'macos'] |
| **metadata** | JSONB | Additional product-specific configuration | Default: '{}' |
| **created_at** | TIMESTAMP | When the product was created | Auto-generated |
| **updated_at** | TIMESTAMP | Timestamp of last modification | Auto-updated |
| **deleted_at** | TIMESTAMP | Soft deletion timestamp | Nullable |

### 6. Policies Table

The `policies` table defines comprehensive license behavior and validation rules.

```sql
CREATE TABLE policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    product_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    strict BOOLEAN DEFAULT FALSE,
    scheme VARCHAR(255),
    duration INTEGER,
    concurrent_machines INTEGER,
    allowed_machines INTEGER,
    allowed_fingerprints TEXT[],
    allowed_scopes TEXT[],
    allowed_uses INTEGER,
    max_uses INTEGER,
    protected BOOLEAN DEFAULT FALSE,
    require_heartbeat BOOLEAN DEFAULT FALSE,
    heartbeat_duration INTEGER,
    heartbeat_basis VARCHAR(255),
    heartbeat_cull_strategy VARCHAR(255),
    heartbeat_resurrection_strategy VARCHAR(255),
    require_check_in BOOLEAN DEFAULT FALSE,
    check_in_interval VARCHAR(255),
    check_in_interval_count INTEGER,
    fingerprint_uniqueness_strategy VARCHAR(255),
    fingerprint_matching_strategy VARCHAR(255),
    expiration_strategy VARCHAR(255),
    expiration_basis VARCHAR(255),
    renewal_basis VARCHAR(255),
    transfer_strategy VARCHAR(255),
    authentication_strategy VARCHAR(255),
    machine_uniqueness_strategy VARCHAR(255),
    machine_matching_strategy VARCHAR(255),
    component_uniqueness_strategy VARCHAR(255),
    component_matching_strategy VARCHAR(255),
    lease_strategy VARCHAR(255),
    lease_basis VARCHAR(255),
    rate_limit_strategy VARCHAR(255),
    overages_strategy VARCHAR(255),
    use_pool_strategy VARCHAR(255),
    require_product_scope BOOLEAN DEFAULT FALSE,
    require_policy_scope BOOLEAN DEFAULT FALSE,
    require_machine_scope BOOLEAN DEFAULT FALSE,
    require_fingerprint_scope BOOLEAN DEFAULT FALSE,
    require_user_scope BOOLEAN DEFAULT FALSE,
    require_checksum_scope BOOLEAN DEFAULT FALSE,
    require_version_scope BOOLEAN DEFAULT FALSE,
    require_environment_scope BOOLEAN DEFAULT FALSE,
    require_components_scope BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique identifier for the policy | Auto-generated |
| **organization_id** | UUID | Reference to the organization that owns this policy | Required, FK to organizations |
| **product_id** | UUID | Reference to the product this policy applies to | Required, FK to products |
| **name** | VARCHAR(255) | Display name of the policy | Required |
| **description** | TEXT | Detailed description of the policy rules | Optional |
| **strict** | BOOLEAN | Whether to enforce strict validation rules | Default: FALSE |
| **scheme** | VARCHAR(255) | License validation scheme | e.g., 'RSA', 'Ed25519' |
| **duration** | INTEGER | License duration in seconds | Nullable (perpetual) |
| **concurrent_machines** | INTEGER | Max machines using license simultaneously | Nullable |
| **allowed_machines** | INTEGER | Total machines allowed to activate | Nullable |
| **allowed_fingerprints** | TEXT[] | Array of specific machine fingerprints allowed | Nullable |
| **allowed_scopes** | TEXT[] | Array of allowed validation scopes | Nullable |
| **allowed_uses** | INTEGER | Current number of uses allowed | Nullable |
| **max_uses** | INTEGER | Maximum number of uses permitted | Nullable |
| **protected** | BOOLEAN | System flag to prevent accidental deletion | Default: FALSE |
| **require_heartbeat** | BOOLEAN | Whether machines must send periodic heartbeats | Default: FALSE |
| **heartbeat_duration** | INTEGER | Interval between required heartbeats (seconds) | Nullable |
| **heartbeat_basis** | VARCHAR(255) | What triggers heartbeat requirements | Nullable |
| **heartbeat_cull_strategy** | VARCHAR(255) | How to handle missed heartbeats | Nullable |
| **heartbeat_resurrection_strategy** | VARCHAR(255) | How to handle machines coming back online | Nullable |
| **require_check_in** | BOOLEAN | Whether periodic check-ins are required | Default: FALSE |
| **check_in_interval** | VARCHAR(255) | Check-in frequency unit | e.g., 'days', 'hours' |
| **check_in_interval_count** | INTEGER | Number of intervals between check-ins | Nullable |
| **fingerprint_uniqueness_strategy** | VARCHAR(255) | How to handle duplicate fingerprints | Nullable |
| **fingerprint_matching_strategy** | VARCHAR(255) | How to match machine fingerprints | Nullable |
| **expiration_strategy** | VARCHAR(255) | How license expiration is handled | Nullable |
| **expiration_basis** | VARCHAR(255) | What determines license expiration | Nullable |
| **renewal_basis** | VARCHAR(255) | How license renewal works | Nullable |
| **transfer_strategy** | VARCHAR(255) | Whether and how licenses can be transferred | Nullable |
| **authentication_strategy** | VARCHAR(255) | Required authentication method | Nullable |
| **machine_uniqueness_strategy** | VARCHAR(255) | How to ensure machine uniqueness | Nullable |
| **machine_matching_strategy** | VARCHAR(255) | How to match machines to licenses | Nullable |
| **component_uniqueness_strategy** | VARCHAR(255) | How to handle hardware component changes | Nullable |
| **component_matching_strategy** | VARCHAR(255) | How to match hardware components | Nullable |
| **lease_strategy** | VARCHAR(255) | How license leasing works | Nullable |
| **lease_basis** | VARCHAR(255) | What determines lease duration | Nullable |
| **rate_limit_strategy** | VARCHAR(255) | How to handle rate limiting | Nullable |
| **overages_strategy** | VARCHAR(255) | How to handle usage overages | Nullable |
| **use_pool_strategy** | VARCHAR(255) | How to manage shared license pools | Nullable |
| **require_product_scope** | BOOLEAN | Whether product scope validation is required | Default: FALSE |
| **require_policy_scope** | BOOLEAN | Whether policy scope validation is required | Default: FALSE |
| **require_machine_scope** | BOOLEAN | Whether machine scope validation is required | Default: FALSE |
| **require_fingerprint_scope** | BOOLEAN | Whether fingerprint scope validation is required | Default: FALSE |
| **require_user_scope** | BOOLEAN | Whether user scope validation is required | Default: FALSE |
| **require_checksum_scope** | BOOLEAN | Whether checksum validation is required | Default: FALSE |
| **require_version_scope** | BOOLEAN | Whether version validation is required | Default: FALSE |
| **require_environment_scope** | BOOLEAN | Whether environment validation is required | Default: FALSE |
| **require_components_scope** | BOOLEAN | Whether component validation is required | Default: FALSE |
| **metadata** | JSONB | Additional policy-specific configuration | Default: '{}' |
| **created_at** | TIMESTAMP | When the policy was created | Auto-generated |
| **updated_at** | TIMESTAMP | Timestamp of last modification | Auto-updated |
| **deleted_at** | TIMESTAMP | Soft deletion timestamp | Nullable |

### 7. Licenses Table

The `licenses` table stores individual license instances with their current state and tracking information.

```sql
CREATE TABLE licenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    product_id UUID NOT NULL,
    policy_id UUID NOT NULL,
    key VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired', 'suspended')),
    uses INTEGER DEFAULT 0,
    protected BOOLEAN DEFAULT FALSE,
    version VARCHAR(255),
    user_id UUID,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_validated_at TIMESTAMP WITH TIME ZONE,
    last_validated_checksum VARCHAR(255),
    last_validated_version VARCHAR(255),
    last_check_in_at TIMESTAMP WITH TIME ZONE,
    last_check_out_at TIMESTAMP WITH TIME ZONE,
    last_expiration_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_expiring_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    UNIQUE (organization_id, key)
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique identifier for the license | Auto-generated |
| **organization_id** | UUID | Reference to the organization that owns this license | Required, FK to organizations |
| **product_id** | UUID | Reference to the product this license is for | Required, FK to products |
| **policy_id** | UUID | Reference to the policy governing this license | Required, FK to policies |
| **key** | VARCHAR(255) | Unique license key string within the organization | Required, unique per org |
| **name** | VARCHAR(255) | Optional display name for the license | Optional |
| **status** | VARCHAR(50) | Current license status | `active`, `inactive`, `expired`, `suspended` |
| **uses** | INTEGER | Current number of times the license has been used | Default: 0 |
| **protected** | BOOLEAN | System flag to prevent accidental deletion | Default: FALSE |
| **version** | VARCHAR(255) | Version of the licensed software | Optional |
| **user_id** | UUID | Optional reference to the user who owns this license | FK to users |
| **expires_at** | TIMESTAMP | When the license expires | Nullable (never expires) |
| **last_validated_at** | TIMESTAMP | Timestamp of last successful validation | Nullable |
| **last_validated_checksum** | VARCHAR(255) | Checksum from last validation | Nullable |
| **last_validated_version** | VARCHAR(255) | Software version from last validation | Nullable |
| **last_check_in_at** | TIMESTAMP | Timestamp of last check-in | Nullable |
| **last_check_out_at** | TIMESTAMP | Timestamp of last check-out (for floating licenses) | Nullable |
| **last_expiration_event_sent_at** | TIMESTAMP | When expiration notification was last sent | Nullable |
| **last_check_in_event_sent_at** | TIMESTAMP | When check-in notification was last sent | Nullable |
| **last_expiring_soon_event_sent_at** | TIMESTAMP | When "expiring soon" notification was last sent | Nullable |
| **last_check_in_soon_event_sent_at** | TIMESTAMP | When "check-in soon" notification was last sent | Nullable |
| **metadata** | JSONB | Additional license-specific data | Default: '{}' |
| **created_at** | TIMESTAMP | When the license was created | Auto-generated |
| **updated_at** | TIMESTAMP | Timestamp of last modification | Auto-updated |
| **deleted_at** | TIMESTAMP | Soft deletion timestamp | Nullable |

### 8. Machines Table

The `machines` table tracks individual machines that have activated licenses.

```sql
CREATE TABLE machines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    license_id UUID NOT NULL,
    policy_id UUID NOT NULL,
    owner_id UUID,
    fingerprint VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    hostname VARCHAR(255),
    platform VARCHAR(255),
    ip VARCHAR(45),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    activated_at TIMESTAMP WITH TIME ZONE,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    last_seen TIMESTAMP WITH TIME ZONE,
    cores INTEGER DEFAULT 0,
    components JSONB DEFAULT '{}',
    last_heartbeat_at TIMESTAMP WITH TIME ZONE,
    next_heartbeat_at TIMESTAMP WITH TIME ZONE,
    last_death_event_sent_at TIMESTAMP WITH TIME ZONE,
    heartbeat_jid VARCHAR(255),
    last_check_out_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE (license_id, fingerprint)
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique identifier for the machine | Auto-generated |
| **license_id** | UUID | Reference to the license this machine is using | Required, FK to licenses |
| **policy_id** | UUID | Reference to the policy governing this machine | Required, FK to policies |
| **owner_id** | UUID | Optional reference to the user who owns this machine | FK to users |
| **fingerprint** | VARCHAR(255) | Unique machine fingerprint for identification | Required, unique per license |
| **name** | VARCHAR(255) | Optional display name for the machine | Optional |
| **hostname** | VARCHAR(255) | Machine's network hostname | Optional |
| **platform** | VARCHAR(255) | Operating system/platform | e.g., 'windows', 'linux', 'macos' |
| **ip** | VARCHAR(45) | Machine's IP address | IPv4 or IPv6 |
| **status** | VARCHAR(50) | Current machine status | `active`, `inactive` |
| **activated_at** | TIMESTAMP | When the machine was first activated | Nullable |
| **deactivated_at** | TIMESTAMP | When the machine was deactivated | Nullable (still active) |
| **last_seen** | TIMESTAMP | Timestamp of last communication from this machine | Nullable |
| **cores** | INTEGER | Number of CPU cores on the machine | Default: 0 |
| **components** | JSONB | JSON object containing hardware component information | Default: '{}' |
| **last_heartbeat_at** | TIMESTAMP | Timestamp of last heartbeat signal | Nullable |
| **next_heartbeat_at** | TIMESTAMP | When the next heartbeat is expected | Nullable |
| **last_death_event_sent_at** | TIMESTAMP | When "machine death" notification was last sent | Nullable |
| **heartbeat_jid** | VARCHAR(255) | Job ID for heartbeat processing | Nullable |
| **last_check_out_at** | TIMESTAMP | Timestamp of last check-out (for floating licenses) | Nullable |
| **metadata** | JSONB | Additional machine-specific data | Default: '{}' |
| **created_at** | TIMESTAMP | When the machine was registered | Auto-generated |
| **updated_at** | TIMESTAMP | Timestamp of last modification | Auto-updated |
| **deleted_at** | TIMESTAMP | Soft deletion timestamp | Nullable |

### 9. Sessions Table

The `sessions` table manages user authentication sessions.

```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    ip VARCHAR(45),
    user_agent TEXT,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique identifier for the session | Auto-generated |
| **user_id** | UUID | Reference to the user who owns this session | Required, FK to users |
| **token_hash** | VARCHAR(255) | Hashed session token for security | Unique, required |
| **ip** | VARCHAR(45) | IP address from which the session was created | IPv4 or IPv6 |
| **user_agent** | TEXT | Browser/client user agent string | Optional |
| **last_used_at** | TIMESTAMP | Timestamp of last session activity | Nullable |
| **expires_at** | TIMESTAMP | When the session expires | Required |
| **created_at** | TIMESTAMP | When the session was created | Auto-generated |

### 10. API Tokens Table

The `api_tokens` table manages API access tokens for programmatic access.

```sql
CREATE TABLE api_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    organization_id UUID,
    name VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    permissions TEXT[],
    active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

#### Field Descriptions:

| Field | Type | Description | Notes |
|-------|------|-------------|-------|
| **id** | UUID | Unique identifier for the API token | Auto-generated |
| **user_id** | UUID | Optional reference to the user who owns this token | FK to users |
| **organization_id** | UUID | Optional reference to the organization this token belongs to | FK to organizations |
| **name** | VARCHAR(255) | Display name for the API token | Required |
| **token_hash** | VARCHAR(255) | Hashed API token for security | Unique, required |
| **permissions** | TEXT[] | Array of permissions for Open Policy Agent (OPA) authorization | Nullable |
| **active** | BOOLEAN | Whether the token is currently active | Default: TRUE |
| **expires_at** | TIMESTAMP | When the token expires | Nullable (never expires) |
| **last_used_at** | TIMESTAMP | Timestamp of last token usage | Nullable |
| **created_at** | TIMESTAMP | When the token was created | Auto-generated |
| **updated_at** | TIMESTAMP | Timestamp of last modification | Auto-updated |

## Relationships and Constraints

### Primary Relationships:
- Organizations can have multiple users, products, policies, and licenses
- Users can belong to multiple organizations with different roles
- Products belong to organizations and can have multiple policies
- Policies define rules for licenses and machines
- Licenses are instances of products with specific policies
- Machines are activated against specific licenses
- Sessions and API tokens provide authentication mechanisms

### Key Constraints:
- All tables use UUID primary keys for security and scalability
- Soft deletion is implemented via `deleted_at` timestamps
- Unique constraints prevent duplicate keys and slugs
- Foreign key constraints maintain referential integrity
- Check constraints enforce valid enum values
- Timestamps use timezone-aware types for global compatibility

## Security Considerations

1. **Cryptographic Keys**: Private keys are stored encrypted at rest
2. **Password Security**: User passwords are hashed before storage
3. **Token Security**: Session and API tokens are hashed for storage
4. **Soft Deletion**: Sensitive data is soft-deleted to maintain audit trails
5. **UUID Usage**: UUIDs prevent enumeration attacks
6. **Permission System**: Fine-grained permissions support OPA integration

## Performance Considerations

1. **Indexing**: Unique constraints create implicit indexes on frequently queried fields
2. **JSONB**: Metadata fields use JSONB for efficient JSON operations
3. **Timestamps**: All timestamps include timezone information
4. **Foreign Keys**: Proper cascading rules prevent orphaned records
5. **Array Fields**: PostgreSQL arrays are used for multi-value fields like permissions

This schema provides a robust foundation for a comprehensive license management system with enterprise-grade security, flexibility, and scalability.
