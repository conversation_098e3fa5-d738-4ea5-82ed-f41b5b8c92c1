# License Vietnamese Comments Guide - Complete Documentation

Tài liệu này mô tả việc thêm comments chi tiết bằng tiếng Việt cho license implementation, giải thích ý nghĩa business logic và architecture của core entity quan trọng nhất.

## 🎯 **<PERSON><PERSON><PERSON> Đích Comments Tiếng Việt cho License**

### **Tại Sao License Cần Comments Chi Tiết?**
- ✅ **Core Entity**: License là entity quan trọng nhất trong licensing system
- ✅ **Complex Business Logic**: License key generation, validation, expiration, usage tracking
- ✅ **Policy Integration**: License inherit và override policy rules
- ✅ **Multiple Services**: Integration với ValidationService, CheckoutService, LookupService
- ✅ **Ruby Compatibility**: Cần giải thích mapping từ Ruby keygen-api
- ✅ **Team Collaboration**: Giúp team hiểu license lifecycle và business rules

## 📋 **C<PERSON>u Trúc Comments Đã Thêm**

### **1. Handler Level Comments**

#### **Constants với Business Context**
```go
// === LICENSE KEY GENERATION CONSTANTS ===
// Các hằng số cho việc tạo license key (mapping từ Ruby keygen-api)
const (
    LicenseKeyLength    = 16         // bytes cho SecureRandom.hex(16) - tạo 32 ký tự hex
    LicenseKeyGroupSize = 6          // số ký tự per group khi format key (XXXXXX-XXXXXX-...)
    LicenseKeyHexLength = 32         // tổng số ký tự hex (16 bytes * 2)
    MaxIntegerValue     = ********** // int32 max để tránh overflow trong usage tracking
)

// === HTTP ERROR MESSAGES ===
// Các thông báo lỗi chuẩn cho license operations
const (
    ErrInvalidLicenseID  = "invalid license ID"                                                // License ID không phải UUID hợp lệ
    ErrLicenseNotFound   = "license not found"                                                 // Không tìm thấy license
    ErrInvalidRequest    = "invalid request"                                                   // Request body không hợp lệ
    ErrAlreadySuspended  = "license is already suspended"                                      // License đã bị suspend rồi
    ErrNotSuspended      = "license is not suspended"                                          // License không ở trạng thái suspended
    ErrCannotRenew       = "cannot be renewed because the policy does not have a duration"    // Policy không có duration nên không thể renew
    ErrIncrementTooLarge = "integer is too large"                                              // Increment value quá lớn
    ErrIncrementNegative = "increment must be positive"                                        // Increment phải là số dương
    ErrIntegerOverflow   = "increment would cause integer overflow"                            // Increment sẽ gây overflow
    ErrDecrementNegative = "decrement must be positive"                                        // Decrement phải là số dương
    ErrDecrementTooLarge = "decrement is too large"                                            // Decrement value quá lớn
)
```

#### **Request/Response Types với Detailed Explanation**
```go
// === REQUEST/RESPONSE TYPES ===
// Các struct định nghĩa request/response cho type safety và validation
type (
    // CreateLicenseRequest đại diện cho request payload khi tạo license mới
    // Mapping từ Ruby keygen-api typed_params với tất cả fields có thể
    CreateLicenseRequest struct {
        // === CORE ATTRIBUTES ===
        ID                   *string                `json:"id"`                    // Custom UUID cho license (optional)
        Name                 *string                `json:"name"`                  // Tên license do user đặt
        Key                  *string                `json:"key"`                   // Custom license key (optional, sẽ auto-generate nếu không có)
        Protected            *bool                  `json:"protected"`             // License có được bảo vệ không
        Suspended            *bool                  `json:"suspended"`             // License có bị suspend không
        Expiry               *time.Time             `json:"expiry"`                // Thời điểm hết hạn (optional)
        
        // === LIMIT OVERRIDES ===
        // Các override limits từ policy (optional, sẽ dùng policy defaults nếu không set)
        MaxMachinesOverride  *int                   `json:"max_machines"`          // Override số máy tối đa
        MaxCoresOverride     *int                   `json:"max_cores"`             // Override số cores tối đa
        MaxUsesOverride      *int                   `json:"max_uses"`              // Override số lần sử dụng tối đa
        MaxProcessesOverride *int                   `json:"max_processes"`         // Override số processes tối đa
        MaxUsersOverride     *int                   `json:"max_users"`             // Override số users tối đa
        
        // === FLEXIBLE DATA ===
        Metadata             map[string]interface{} `json:"metadata"`              // Custom metadata key-value
        
        // === RELATIONSHIPS ===
        PolicyID             *string                `json:"policy_id" binding:"required"` // Policy ID (bắt buộc)
        OwnerID              *string                `json:"owner_id"`              // User sở hữu license (optional)
        GroupID              *string                `json:"group_id"`              // Group chứa license (optional)
    }

    // UpdateLicenseRequest đại diện cho request payload khi update license
    // Tất cả fields đều optional, chỉ update những fields được cung cấp
    UpdateLicenseRequest struct {
        // === CORE ATTRIBUTES ===
        Name      *string    `json:"name"`      // Tên license mới
        Protected *bool      `json:"protected"` // Thay đổi protection status
        Suspended *bool      `json:"suspended"` // Thay đổi suspension status
        Expiry    *time.Time `json:"expiry"`    // Thay đổi thời điểm hết hạn
        
        // === LIMIT OVERRIDES ===
        // Update các override limits (set nil để remove override, dùng policy default)
        MaxMachinesOverride  *int `json:"max_machines"`  // Update override số máy tối đa
        MaxCoresOverride     *int `json:"max_cores"`     // Update override số cores tối đa
        MaxUsesOverride      *int `json:"max_uses"`      // Update override số lần sử dụng tối đa
        MaxProcessesOverride *int `json:"max_processes"` // Update override số processes tối đa
        MaxUsersOverride     *int `json:"max_users"`     // Update override số users tối đa
        
        // === FLEXIBLE DATA ===
        Metadata map[string]interface{} `json:"metadata"` // Update custom metadata
    }

    // UsageActionRequest đại diện cho request increment/decrement usage
    // Dùng cho tracking số lần sử dụng license (Ruby: increment_uses/decrement_uses)
    UsageActionRequest struct {
        Meta struct {
            Increment *int `json:"increment"` // Số lượng cần tăng (phải > 0)
            Decrement *int `json:"decrement"` // Số lượng cần giảm (phải > 0)
        } `json:"meta"`
    }

    // ValidationRequest đại diện cho license validation request
    // Dùng để validate license key và fingerprint (Ruby: license validation)
    ValidationRequest struct {
        Key         string                 `json:"key" binding:"required"` // License key cần validate (bắt buộc)
        Fingerprint *string                `json:"fingerprint"`            // Machine fingerprint (optional)
        Metadata    map[string]interface{} `json:"metadata"`               // Additional metadata cho validation
    }
)
```

#### **LicenseHandler Structure**
```go
// LicenseHandler xử lý các HTTP requests liên quan đến license
// License trong keygen-api là core entity chứa license key và các rules
// Handler này sử dụng các services có sẵn để xử lý business logic phức tạp
type LicenseHandler struct {
    licenseRepo       repositories.LicenseRepository        // Repository để CRUD operations với license
    validationService *licenseService.ValidationService     // Service để validate license keys và policies
    checkoutService   *licenseService.CheckoutService       // Service để checkout/checkin licenses
    lookupService     *licenseService.LookupService         // Service để lookup licenses theo various criteria
}

// NewLicenseHandler tạo một license handler mới
// Cần tất cả services để xử lý đầy đủ license operations
// Services này đã implement business logic phức tạp từ Ruby keygen-api
func NewLicenseHandler(
    licenseRepo repositories.LicenseRepository,        // Repository cho database operations
    validationService *licenseService.ValidationService, // Service cho license validation logic
    checkoutService *licenseService.CheckoutService,     // Service cho checkout/checkin operations
    lookupService *licenseService.LookupService,         // Service cho license lookup operations
) *LicenseHandler {
    return &LicenseHandler{
        licenseRepo:       licenseRepo,
        validationService: validationService,
        checkoutService:   checkoutService,
        lookupService:     lookupService,
    }
}
```

#### **CreateLicense Method với Comprehensive Comments**
```go
// CreateLicense tạo một license mới theo patterns của keygen-api
// License là core entity chứa license key và các rules từ policy
// Endpoint: POST /organizations/{org_id}/products/{product_id}/licenses
func (h *LicenseHandler) CreateLicense(c *gin.Context) {
    // Lấy organization_id và product_id từ URL path parameters
    // License phải thuộc về organization và product cụ thể
    organizationID := c.Param("organization_id")
    productID := c.Param("product_id")

    // === REQUEST PARSING ===
    // Parse request body sử dụng typed struct để type safety
    var requestData CreateLicenseRequest
    if err := c.ShouldBindJSON(&requestData); err != nil {
        // Trả về lỗi 400 nếu request body không hợp lệ
        c.JSON(http.StatusBadRequest, gin.H{
            "error":  ErrInvalidRequest,
            "reason": err.Error(),
        })
        return
    }

    // === POLICY VALIDATION ===
    // Validate policy tồn tại và thuộc về product này
    policyUUID, err := uuid.Parse(*requestData.PolicyID)
    if err != nil {
        // Policy ID phải là UUID hợp lệ
        c.JSON(http.StatusBadRequest, gin.H{"error": "invalid policy ID"})
        return
    }

    // TODO: Validate policy belongs to product
    // policy, err := h.policyRepo.GetByID(c.Request.Context(), policyUUID)
    // Cần check policy.ProductID == productID để đảm bảo security

    // === FUNCTIONAL OPTIONS CONSTRUCTION ===
    // Xây dựng functional options từ request data
    // Pattern này cho phép flexible license creation với optional fields
    var options []LicenseOption

    // Optional license name
    if requestData.Name != nil {
        options = append(options, WithLicenseName(*requestData.Name))
    }
    
    // Optional custom license key (nếu không có sẽ auto-generate)
    if requestData.Key != nil && *requestData.Key != "" {
        options = append(options, WithLicenseKey(*requestData.Key))
    }
    
    // Optional protection flag
    if requestData.Protected != nil {
        options = append(options, WithLicenseProtected(*requestData.Protected))
    }
    
    // Optional expiry date
    if requestData.Expiry != nil {
        options = append(options, WithLicenseExpiry(*requestData.Expiry))
    }
    
    // Optional custom metadata
    if requestData.Metadata != nil {
        options = append(options, WithLicenseMetadata(requestData.Metadata))
    }

    // === LICENSE ENTITY CREATION ===
    // Tạo license entity sử dụng functional options pattern
    // Pattern này cho phép flexible construction với clean code
    license, err := h.createLicenseEntity(organizationID, productID, policyUUID.String(), options...)
    if err != nil {
        // Trả về lỗi 500 nếu có vấn đề khi tạo entity
        c.JSON(http.StatusInternalServerError, gin.H{
            "error":  "failed to create license entity",
            "reason": err.Error(),
        })
        return
    }

    // === ADDITIONAL FIELDS APPLICATION ===
    // Apply các fields không được cover bởi functional options
    // Các fields này cần special handling hoặc validation
    
    // Custom license ID (nếu không có sẽ dùng auto-generated UUID)
    if requestData.ID != nil {
        license.ID = *requestData.ID
    }
    
    // Suspension status (license có bị suspend không)
    if requestData.Suspended != nil {
        license.Suspended = *requestData.Suspended
    }
    
    // Owner assignment (user sở hữu license)
    if requestData.OwnerID != nil {
        license.OwnerID = *requestData.OwnerID
        license.OwnerType = entities.LicenseOwnerTypeUser // Assume user owner (có thể là group)
    }
    
    // === LIMIT OVERRIDES ===
    // Apply các override limits từ request (override policy defaults)
    if requestData.MaxUsesOverride != nil {
        license.MaxUsesOverride = requestData.MaxUsesOverride
    }
    if requestData.MaxMachinesOverride != nil {
        license.MaxMachinesOverride = requestData.MaxMachinesOverride
    }
    if requestData.MaxCoresOverride != nil {
        license.MaxCoresOverride = requestData.MaxCoresOverride
    }
    if requestData.MaxProcessesOverride != nil {
        license.MaxProcessesOverride = requestData.MaxProcessesOverride
    }
    
    // ... continue with database save and response
}
```

### **2. Entity Level Comments**

#### **License Struct với Comprehensive Field Documentation**
```go
// License đại diện cho một license key với tất cả metadata và rules
// Trong keygen-api, license là core entity chứa license key và inherit rules từ policy
// License có thể override policy limits và track usage/validation
// Mapping từ Ruby License model với đầy đủ relationships và business logic
type License struct {
    // === CORE IDENTIFIERS ===
    // Các trường định danh cơ bản
    ID             string  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"` // UUID của license
    OrganizationID string  `json:"organization_id" gorm:"type:uuid;not null"`                 // Organization sở hữu license
    ProductID      string  `json:"product_id" gorm:"type:uuid;not null"`                      // Product mà license thuộc về
    PolicyID       string  `json:"policy_id" gorm:"type:uuid;not null"`                       // Policy chứa rules cho license
    Key            string  `json:"key" gorm:"size:255;not null"`                              // License key (unique identifier)
    Name           *string `json:"name,omitempty" gorm:"size:255"`                            // Tên license do user đặt (optional)
    
    // === LICENSE OWNERSHIP ===
    // Ownership polymorphic - license có thể thuộc về user hoặc organization
    OwnerType string `json:"owner_type" gorm:"size:50;not null;check:owner_type IN ('user', 'organization')"` // Loại owner (user/organization)
    OwnerID   string `json:"owner_id" gorm:"type:uuid;not null"`                                               // ID của owner

    // === LICENSE STATE ===
    // Trạng thái và lifecycle của license
    Status    string `json:"status" gorm:"size:50;default:'active';check:status IN ('active', 'expired', 'suspended', 'banned')"` // Trạng thái license
    Suspended bool   `json:"suspended" gorm:"default:false"`                                                                        // License có bị suspend không
    Protected bool   `json:"protected" gorm:"default:false"`                                                                        // License có được bảo vệ không

    // === USAGE TRACKING ===
    // Theo dõi việc sử dụng license
    Uses      int        `json:"uses" gorm:"default:0"` // Số lần đã sử dụng license
    ExpiresAt *time.Time `json:"expires_at,omitempty"`  // Thời điểm hết hạn (từ policy duration)
    LastUsed  *time.Time `json:"last_used,omitempty"`   // Lần cuối sử dụng license

    // === POLICY OVERRIDES ===
    // Cho phép override policy limits per license (flexibility)
    MaxUsesOverride      *int `json:"max_uses_override,omitempty"`      // Override số lần sử dụng tối đa
    MaxMachinesOverride  *int `json:"max_machines_override,omitempty"`  // Override số máy tối đa
    MaxCoresOverride     *int `json:"max_cores_override,omitempty"`     // Override số cores tối đa
    MaxUsersOverride     *int `json:"max_users_override,omitempty"`     // Override số users tối đa
    MaxProcessesOverride *int `json:"max_processes_override,omitempty"` // Override số processes tối đa

    // === CACHED COUNTS ===
    // Cache các counts để performance (tránh expensive queries)
    MachinesCount     int `json:"machines_count" gorm:"default:0"`      // Số machines hiện tại
    MachinesCoreCount int `json:"machines_core_count" gorm:"default:0"` // Tổng số cores đang sử dụng
    LicenseUsersCount int `json:"license_users_count" gorm:"default:0"` // Số users đang sử dụng

    // === HEARTBEAT & VALIDATION TRACKING ===
    // Theo dõi check-in, validation và checkout activities
    LastCheckInAt         *time.Time `json:"last_check_in_at"`        // Lần cuối check-in
    LastValidatedAt       *time.Time `json:"last_validated_at"`       // Lần cuối validate license
    LastValidatedChecksum *string    `json:"last_validated_checksum"` // Checksum của lần validate cuối
    LastValidatedVersion  *string    `json:"last_validated_version"`  // Version của lần validate cuối
    LastCheckOutAt        *time.Time `json:"last_check_out_at"`       // Lần cuối check-out (floating licenses)

    // === EVENT TRACKING ===
    // Theo dõi các events đã gửi để tránh spam notifications
    LastExpirationEventSentAt   *time.Time `json:"last_expiration_event_sent_at"`    // Lần cuối gửi expiration event
    LastCheckInEventSentAt      *time.Time `json:"last_check_in_event_sent_at"`      // Lần cuối gửi check-in event
    LastExpiringSoonEventSentAt *time.Time `json:"last_expiring_soon_event_sent_at"` // Lần cuối gửi expiring soon event
    LastCheckInSoonEventSentAt  *time.Time `json:"last_check_in_soon_event_sent_at"` // Lần cuối gửi check-in soon event

    // === FLEXIBLE DATA ===
    // Custom metadata key-value cho license
    Metadata Metadata `json:"metadata" gorm:"type:jsonb;default:'{}'"` // Custom metadata JSONB

    // === AUDIT FIELDS ===
    // Các trường audit chuẩn cho tracking changes
    CreatedAt time.Time      `json:"created_at" gorm:"not null"`        // Thời điểm tạo license
    UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`        // Thời điểm cập nhật cuối
    DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"` // Soft delete timestamp

    // === RELATIONSHIPS ===
    // Các mối quan hệ với entities khác (lazy loading)
    Organization Organization `json:"organization,omitempty" gorm:"foreignKey:OrganizationID"` // Organization sở hữu license
    Product      Product      `json:"product,omitempty" gorm:"foreignKey:ProductID"`           // Product mà license thuộc về
    Policy       Policy       `json:"policy,omitempty" gorm:"foreignKey:PolicyID"`             // Policy chứa rules cho license
    Machines     []Machine    `json:"machines,omitempty" gorm:"foreignKey:LicenseID"`          // Danh sách machines sử dụng license
}

// TableName override tên table cho GORM
// Đảm bảo GORM sử dụng đúng tên table "licenses" trong database
func (License) TableName() string {
    return "licenses"
}
```

## 🎯 **Phong Cách Comments cho License**

### **1. Business Context First**
- ✅ **License key generation** - Giải thích SecureRandom.hex(16) mapping
- ✅ **Policy inheritance** - Giải thích cách license inherit từ policy
- ✅ **Override mechanism** - Giải thích flexibility của per-license limits
- ✅ **Usage tracking** - Giải thích increment/decrement operations
- ✅ **Event management** - Giải thích notification system

### **2. Service Integration Explanation**
- ✅ **ValidationService** - Giải thích license validation logic
- ✅ **CheckoutService** - Giải thích floating license checkout/checkin
- ✅ **LookupService** - Giải thích license lookup mechanisms
- ✅ **Repository pattern** - Giải thích database operations

### **3. Ruby Compatibility Notes**
- ✅ **typed_params mapping** - Giải thích request structure
- ✅ **Functional options** - Giải thích Go pattern vs Ruby
- ✅ **Error handling** - Giải thích HTTP status codes
- ✅ **Business logic** - Giải thích exact same behavior

## 🏆 **Kết Quả**

- **✅ 100+ Comments** được thêm chi tiết
- **✅ Entity Documentation** - Comprehensive field explanations
- **✅ Handler Documentation** - Request processing, validation, responses
- **✅ Business Logic Explanation** - License lifecycle, policy integration
- **✅ Service Architecture** - Integration patterns và dependencies
- **✅ Ruby Compatibility** - Mapping và equivalent features
- **✅ Production Ready** - Complete documentation cho maintenance

**License implementation giờ đây có documentation hoàn chỉnh bằng tiếng Việt!** 🚀

**Key Insight:** License là core entity phức tạp nhất với nhiều integrations, nên comments focus vào business context, service dependencies, và Ruby compatibility để team hiểu rõ architecture decisions và business logic flow.
