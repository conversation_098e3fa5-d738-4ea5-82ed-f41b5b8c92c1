# API Authentication Reference

This document provides a comprehensive reference for authentication requirements across all GoKeys API endpoints.

## Authentication Overview

All API endpoints (except public ones) require authentication. The API supports multiple authentication methods and uses fine-grained permissions to control access.

### Authentication Methods

| Method | Header Format | Use Case |
|--------|---------------|----------|
| Bearer <PERSON> | `Authorization: Bearer <token>` | Primary method for all clients |
| Basic Auth (Token) | `Authorization: Basic <base64(token:)>` | Alternative token method |
| Basic Auth (License) | `Authorization: Basic <base64(license:<key>)>` | License key authentication |
| License Header | `Authorization: License <key>` | Direct license key method |
| Query Parameter | `?token=<token>` or `?auth=<type>:<value>` | URL-based authentication |

### Token Types

| Token Type | Prefix | Primary Use Case | Permission Level |
|------------|--------|------------------|------------------|
| Environment | `env-` | Automation, CI/CD | Broad access |
| User | `user-` | End-user applications | User-specific operations |
| License | `activ-` | License activation | Minimal activation rights |
| Product | `prod-` | Product management | Product-scoped operations |

## Endpoint Authentication Requirements

### Account Operations

#### GET /api/v1/account
**Description**: Get current account information  
**Authentication**: Required  
**Permissions**: `account.read`  
**Allowed Token Types**: All  

```bash
curl -H "Authorization: Bearer env-abc123...v3" \
     https://api.example.com/api/v1/account
```

#### PUT /api/v1/account
**Description**: Update account information  
**Authentication**: Required  
**Permissions**: `account.update`  
**Allowed Token Types**: Environment, Admin User  

```bash
curl -X PUT \
     -H "Authorization: Bearer env-abc123...v3" \
     -H "Content-Type: application/json" \
     -d '{"name": "Updated Account Name"}' \
     https://api.example.com/api/v1/account
```

### License Operations

#### GET /api/v1/licenses
**Description**: List licenses  
**Authentication**: Required  
**Permissions**: `license.read`  
**Allowed Token Types**: Environment, User, License  

```bash
# Environment token
curl -H "Authorization: Bearer env-abc123...v3" \
     https://api.example.com/api/v1/licenses

# User token  
curl -H "Authorization: Bearer user-def456...v3" \
     https://api.example.com/api/v1/licenses
```

#### POST /api/v1/licenses
**Description**: Create a new license  
**Authentication**: Required  
**Permissions**: `license.create`  
**Allowed Token Types**: Environment, Admin User  

```bash
curl -X POST \
     -H "Authorization: Bearer env-abc123...v3" \
     -H "Content-Type: application/json" \
     -d '{"policy": "550e8400-e29b-41d4-a716-************"}' \
     https://api.example.com/api/v1/licenses
```

#### GET /api/v1/licenses/:id
**Description**: Get specific license  
**Authentication**: Required  
**Permissions**: `license.read`  
**Allowed Token Types**: Environment, User, License (own license only)  

```bash
# Environment token (any license)
curl -H "Authorization: Bearer env-abc123...v3" \
     https://api.example.com/api/v1/licenses/550e8400-e29b-41d4-a716-************

# License token (own license only)
curl -H "Authorization: License LIC-12345-ABCDE" \
     https://api.example.com/api/v1/licenses/550e8400-e29b-41d4-a716-************
```

#### PUT /api/v1/licenses/:id
**Description**: Update license  
**Authentication**: Required  
**Permissions**: `license.update`  
**Allowed Token Types**: Environment, Admin User  

#### DELETE /api/v1/licenses/:id
**Description**: Delete license  
**Authentication**: Required  
**Permissions**: `license.delete`  
**Allowed Token Types**: Environment, Admin User  

### License Actions

#### POST /api/v1/licenses/:id/actions/validate
**Description**: Validate license by ID  
**Authentication**: Required  
**Permissions**: `license.validate` OR `license.read`  
**Allowed Token Types**: All  

```bash
# Environment token
curl -X POST \
     -H "Authorization: Bearer env-abc123...v3" \
     -H "Content-Type: application/json" \
     -d '{"meta": {"machine": "fp-12345"}}' \
     https://api.example.com/api/v1/licenses/550e8400-e29b-41d4-a716-************/actions/validate

# License key authentication
curl -X POST \
     -H "Authorization: License LIC-12345-ABCDE" \
     -H "Content-Type: application/json" \
     -d '{"meta": {"machine": "fp-12345"}}' \
     https://api.example.com/api/v1/licenses/550e8400-e29b-41d4-a716-************/actions/validate
```

#### GET /api/v1/licenses/:id/actions/validate
**Description**: Quick validate license by ID  
**Authentication**: Required  
**Permissions**: `license.validate` OR `license.read`  
**Allowed Token Types**: All  

#### POST /api/v1/licenses/actions/validate-key
**Description**: Validate license by key  
**Authentication**: Required  
**Permissions**: `license.validate` OR `license.read`  
**Allowed Token Types**: All  

```bash
curl -X POST \
     -H "Authorization: Bearer env-abc123...v3" \
     -H "Content-Type: application/json" \
     -d '{"meta": {"key": "LIC-12345-ABCDE", "machine": "fp-12345"}}' \
     https://api.example.com/api/v1/licenses/actions/validate-key
```

#### GET /api/v1/licenses/:id/actions/checkout
#### POST /api/v1/licenses/:id/actions/checkout
**Description**: Checkout license certificate  
**Authentication**: Required  
**Permissions**: `license.check-out`  
**Allowed Token Types**: Environment, User, License  

### Machine Operations

#### GET /api/v1/machines
**Description**: List machines  
**Authentication**: Required  
**Permissions**: `machine.read`  
**Allowed Token Types**: Environment, User, License  

#### POST /api/v1/machines
**Description**: Create machine  
**Authentication**: Required  
**Permissions**: `machine.create`  
**Allowed Token Types**: Environment, User, License  

```bash
curl -X POST \
     -H "Authorization: Bearer env-abc123...v3" \
     -H "Content-Type: application/json" \
     -d '{"fingerprint": "fp-12345", "license": "550e8400-e29b-41d4-a716-************"}' \
     https://api.example.com/api/v1/machines
```

#### GET /api/v1/machines/:id
**Description**: Get specific machine  
**Authentication**: Required  
**Permissions**: `machine.read`  
**Allowed Token Types**: Environment, User, License  

#### PUT /api/v1/machines/:id
**Description**: Update machine  
**Authentication**: Required  
**Permissions**: `machine.update`  
**Allowed Token Types**: Environment, User  

#### DELETE /api/v1/machines/:id
**Description**: Delete machine  
**Authentication**: Required  
**Permissions**: `machine.delete`  
**Allowed Token Types**: Environment, Admin User  

### Machine Actions

#### POST /api/v1/machines/:id/actions/ping
**Description**: Send machine heartbeat ping  
**Authentication**: Required  
**Permissions**: `machine.heartbeat.ping`  
**Allowed Token Types**: Environment, User, License  

```bash
# Environment token
curl -X POST \
     -H "Authorization: Bearer env-abc123...v3" \
     https://api.example.com/api/v1/machines/550e8400-e29b-41d4-a716-************/actions/ping

# License token
curl -X POST \
     -H "Authorization: License LIC-12345-ABCDE" \
     https://api.example.com/api/v1/machines/550e8400-e29b-41d4-a716-************/actions/ping
```

#### POST /api/v1/machines/:id/actions/reset
**Description**: Reset machine heartbeat  
**Authentication**: Required  
**Permissions**: `machine.heartbeat.reset`  
**Allowed Token Types**: Environment, User  

#### GET /api/v1/machines/:id/actions/checkout
#### POST /api/v1/machines/:id/actions/checkout
**Description**: Checkout machine certificate  
**Authentication**: Required  
**Permissions**: `machine.check-out`  
**Allowed Token Types**: Environment, User, License  

### User Operations

#### GET /api/v1/users
**Description**: List users  
**Authentication**: Required  
**Permissions**: `user.read`  
**Allowed Token Types**: Environment, Admin User  

#### POST /api/v1/users
**Description**: Create user  
**Authentication**: Required  
**Permissions**: `user.create`  
**Allowed Token Types**: Environment, Admin User  

#### GET /api/v1/users/:id
**Description**: Get specific user  
**Authentication**: Required  
**Permissions**: `user.read`  
**Allowed Token Types**: Environment, User (own profile), Admin User  

#### PUT /api/v1/users/:id
**Description**: Update user  
**Authentication**: Required  
**Permissions**: `user.update`  
**Allowed Token Types**: Environment, User (own profile), Admin User  

#### DELETE /api/v1/users/:id
**Description**: Delete user  
**Authentication**: Required  
**Permissions**: `user.delete`  
**Allowed Token Types**: Environment, Admin User  

### Product Operations

#### GET /api/v1/products
**Description**: List products  
**Authentication**: Required  
**Permissions**: `product.read`  
**Allowed Token Types**: All  

#### POST /api/v1/products
**Description**: Create product  
**Authentication**: Required  
**Permissions**: `product.create`  
**Allowed Token Types**: Environment, Admin User  

#### GET /api/v1/products/:id
**Description**: Get specific product  
**Authentication**: Required  
**Permissions**: `product.read`  
**Allowed Token Types**: All  

#### PUT /api/v1/products/:id
**Description**: Update product  
**Authentication**: Required  
**Permissions**: `product.update`  
**Allowed Token Types**: Environment, Product (own product), Admin User  

#### DELETE /api/v1/products/:id
**Description**: Delete product  
**Authentication**: Required  
**Permissions**: `product.delete`  
**Allowed Token Types**: Environment, Admin User  

### Policy Operations

#### GET /api/v1/policies
**Description**: List policies  
**Authentication**: Required  
**Permissions**: `policy.read`  
**Allowed Token Types**: All  

#### POST /api/v1/policies
**Description**: Create policy  
**Authentication**: Required  
**Permissions**: `policy.create`  
**Allowed Token Types**: Environment, Admin User  

#### GET /api/v1/policies/:id
**Description**: Get specific policy  
**Authentication**: Required  
**Permissions**: `policy.read`  
**Allowed Token Types**: All  

#### PUT /api/v1/policies/:id
**Description**: Update policy  
**Authentication**: Required  
**Permissions**: `policy.update`  
**Allowed Token Types**: Environment, Admin User  

#### DELETE /api/v1/policies/:id
**Description**: Delete policy  
**Authentication**: Required  
**Permissions**: `policy.delete`  
**Allowed Token Types**: Environment, Admin User  

## Account-Scoped Routes

All endpoints also support account-scoped versions with the prefix `/api/v1/accounts/:account_id/`. These have the same authentication requirements as their non-scoped counterparts.

### Examples

```bash
# Account-scoped license list
curl -H "Authorization: Bearer env-abc123...v3" \
     https://api.example.com/api/v1/accounts/550e8400-e29b-41d4-a716-************/licenses

# Account-scoped license validation
curl -X POST \
     -H "Authorization: Bearer env-abc123...v3" \
     https://api.example.com/api/v1/accounts/550e8400-e29b-41d4-a716-************/licenses/550e8400-e29b-41d4-a716-************/actions/validate

# Account-scoped machine ping
curl -X POST \
     -H "Authorization: License LIC-12345-ABCDE" \
     https://api.example.com/api/v1/accounts/550e8400-e29b-41d4-a716-************/machines/550e8400-e29b-41d4-a716-************/actions/ping
```

## Public Endpoints

These endpoints do not require authentication:

- `GET /health` - Health check
- `GET /version` - API version information
- `GET /swagger/*` - API documentation

## Error Responses

### Authentication Errors

```json
// 401 - No authentication provided
{
  "error": "authentication_required",
  "message": "Authentication required to access this resource"
}

// 401 - Invalid token
{
  "error": "invalid_token",
  "message": "The provided token is invalid"
}

// 401 - Expired token
{
  "error": "token_expired",
  "message": "Token has expired"
}

// 401 - Revoked token
{
  "error": "token_revoked",
  "message": "Token has been revoked"
}
```

### Authorization Errors

```json
// 403 - Insufficient permissions
{
  "error": "insufficient_permissions",
  "message": "Insufficient permissions to access this resource",
  "required_permissions": ["license.create"]
}

// 403 - Wrong token type
{
  "error": "role_not_allowed",
  "message": "Token role is not allowed to access this resource",
  "bearer_type": "license",
  "allowed_roles": ["user", "environment"]
}

// 403 - Resource access denied
{
  "error": "access_denied",
  "message": "You don't have access to this resource"
}
```

## Best Practices

### Token Selection

1. **Environment Tokens**: Use for server-to-server communication, automation, CI/CD
2. **User Tokens**: Use for end-user applications, customer portals
3. **License Tokens**: Use for license activation, machine binding scenarios
4. **Product Tokens**: Use for product-specific management operations

### Security

1. **Use HTTPS**: Always use TLS in production
2. **Token Storage**: Store tokens securely, never in plain text
3. **Token Rotation**: Implement regular token rotation
4. **Least Privilege**: Use tokens with minimal required permissions
5. **Monitor Usage**: Track token usage and detect anomalies

### Error Handling

1. **Handle 401s**: Implement token refresh or re-authentication
2. **Handle 403s**: Show appropriate error messages to users
3. **Retry Logic**: Implement exponential backoff for transient errors
4. **Logging**: Log authentication failures for security monitoring

---

For implementation details, see:
- [Authentication & Authorization System](./authentication-authorization.md)
- [Quick Start Guide](./auth-quick-start.md)
- [Swagger Documentation](http://localhost:8080/swagger/index.html)
