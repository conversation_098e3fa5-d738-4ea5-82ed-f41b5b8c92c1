# OPA Authorization Code Walkthrough - Từ Code Thực Tế

## 🎯 Mục Tiêu
<PERSON> thích OPA authorization bằng cách **đi qua code thực tế** từ dự án GoKeys, từ HTTP request đến Rego policy evaluation.

---

## 📁 Cấu Trúc File Liên Quan

```
gokeys/
├── internal/
│   ├── adapters/http/
│   │   ├── middleware/
│   │   │   └── authorization.go          # 🔑 Authorization middleware
│   │   └── routes/
│   │       └── api.go                    # 🛣️ Route definitions
│   └── domain/services/policy/
│       ├── opa_service.go                # 🏗️ OPA service core
│       └── opa_manager.go                # 📋 OPA manager
├── policies/
│   ├── auth/
│   │   └── main.rego                     # 📜 Rego policies  
│   └── data/
│       └── permissions.json              # 📊 Permission data
└── configs/
    └── config.yaml                       # ⚙️ Config
```

---

## 🚀 Flow 1: HTTP Request → Authorization

### 1.1 Route Definition (routes/api.go)

**Code thực tế:**
```go
// File: internal/adapters/http/routes/api.go
// Lines: 166-171

licenses := protected.Group("/licenses")
{
    licenses.GET("",
        r.authzMiddleware.CreatePermissionChecker("license.read"),
        r.licenseHandler.ListLicensesHandler)
    licenses.GET("/:id",
        r.authzMiddleware.RequireOwnershipOrPermission("license", "id", "license.read"),
        r.licenseHandler.GetLicenseHandler)
}
```

**❓ Phân tích:**
- Route: `GET /licenses/:id`
- Middleware: `CreatePermissionChecker("license.read")`
- Handler: `GetLicenseHandler`

**🔄 Luồng xử lý:**
```
HTTP GET /licenses/123 
    ↓
CreatePermissionChecker("license.read") ← Kiểm tra permission
    ↓
GetLicenseHandler ← Chỉ chạy nếu có quyền
```

### 1.2 Authorization Middleware (middleware/authorization.go)

**Code thực tế:**
```go
// File: internal/adapters/http/middleware/authorization.go  
// Lines: 431-486

func (am *AuthorizationMiddleware) CreatePermissionChecker(requiredPermissions ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 🔍 BƯỚC 1: Thử dùng OPA trước
        opaManager := am.serviceCoordinator.GetOPAManager()
        if opaManager != nil && opaManager.IsEnabled() {
            allowed, err := am.checkPermissionWithOPA(c, requiredPermissions)
            if err == nil {
                if allowed {
                    c.Next() // ✅ Cho phép tiếp tục
                    return
                }
                // ❌ OPA từ chối
                c.JSON(http.StatusForbidden, gin.H{
                    "error": "insufficient_permissions",
                    "message": fmt.Sprintf("One of the following permissions required: %v", requiredPermissions),
                })
                c.Abort()
                return
            }
            // ⚠️ Fallback nếu OPA lỗi
        }

        // 🔙 BƯỚC 2: Traditional permission check (backup)
        permissions, err := GetPermissions(c)
        if err != nil {
            c.JSON(http.StatusForbidden, gin.H{
                "error": "permission_error",
                "message": "Unable to check permissions",
            })
            c.Abort()
            return
        }

        // Kiểm tra user có ít nhất 1 trong các permissions yêu cầu không
        hasAnyPermission := false
        for _, required := range requiredPermissions {
            if hasPermission(permissions, required) {
                hasAnyPermission = true
                break
            }
        }

        if !hasAnyPermission {
            c.JSON(http.StatusForbidden, gin.H{
                "error": "insufficient_permissions",
                "message": fmt.Sprintf("One of the following permissions required: %v", requiredPermissions),
            })
            c.Abort()
            return
        }

        c.Next() // ✅ Cho phép tiếp tục
    }
}
```

**❓ Phân tích:**
1. **Hybrid approach**: OPA primary + traditional fallback
2. **Input**: `requiredPermissions = ["license.read"]`
3. **Output**: `c.Next()` hoặc `c.Abort()`

---

## 🏗️ Flow 2: OPA Integration

### 2.1 checkPermissionWithOPA (middleware/authorization.go)

**Code thực tế:**
```go
// File: internal/adapters/http/middleware/authorization.go
// Lines: 542-574

func (am *AuthorizationMiddleware) checkPermissionWithOPA(c *gin.Context, requiredPermissions []string) (bool, error) {
    // 🔧 BƯỚC 1: Tạo authorization input từ context
    input, err := am.buildOPAInput(c)
    if err != nil {
        return false, err
    }

    // 🔄 BƯỚC 2: Kiểm tra từng permission được yêu cầu
    for _, permission := range requiredPermissions {
        // Chuyển đổi format permission từ "resource:action" thành "resource.action"
        parts := strings.Split(permission, ":")
        if len(parts) == 2 {
            input.Action = parts[1]        // "read", "create", "update"
            input.Resource.Type = parts[0] // "license", "machine", "user"
        } else {
            // Xử lý permissions đặc biệt như "*" (wildcard)
            if permission == "*" {
                input.Subject.Permissions = []string{"*"}
            }
        }

        // 🎯 BƯỚC 3: Kiểm tra với OPA engine
        allowed, err := am.serviceCoordinator.GetOPAManager().IsAllowed(context.Background(), input)
        if err != nil {
            return false, err // Lỗi trong quá trình evaluation
        }
        if allowed {
            return true, nil // Tìm thấy ít nhất 1 permission được cho phép
        }
    }

    return false, nil // Không có permission nào được cho phép
}
```

**❓ Phân tích:**
- **Input**: `requiredPermissions = ["license.read"]`
- **Processing**: Convert `"license.read"` → `{action: "read", resource.type: "license"}`
- **Output**: `true/false` + `error`

### 2.2 buildOPAInput (middleware/authorization.go)

**Tìm method này:**
```bash
# Tìm buildOPAInput trong code
grep -n "buildOPAInput" internal/adapters/http/middleware/authorization.go
```

**Code thực tế (phần quan trọng):**
```go
// File: internal/adapters/http/middleware/authorization.go
// Lines: 600+ (ước tính)

func (am *AuthorizationMiddleware) buildOPAInput(c *gin.Context) (policy.AuthorizationInput, error) {
    // 👤 BƯỚC 1: Lấy thông tin user từ JWT token
    userID, err := GetUserID(c)
    if err != nil {
        return policy.AuthorizationInput{}, err
    }
    
    organizationID, err := GetOrganizationID(c)
    if err != nil {
        return policy.AuthorizationInput{}, err
    }
    
    permissions, err := GetPermissions(c)
    if err != nil {
        return policy.AuthorizationInput{}, err
    }

    // 📋 BƯỚC 2: Xác định resource từ URL
    resourceType := ""
    resourceID := ""
    
    // Parse URL để determine resource type
    path := c.Request.URL.Path
    if strings.Contains(path, "/licenses") {
        resourceType = "license"
        resourceID = c.Param("id")  // Từ /licenses/:id
    } else if strings.Contains(path, "/machines") {
        resourceType = "machine"
        resourceID = c.Param("id")  // Từ /machines/:id
    } else if strings.Contains(path, "/users") {
        resourceType = "user"
        resourceID = c.Param("id")
    }

    // 🎬 BƯỚC 3: Xác định action từ HTTP method
    action := ""
    switch c.Request.Method {
    case "GET":    action = "read"
    case "POST":   action = "create"
    case "PUT":    action = "update"
    case "DELETE": action = "delete"
    default:       action = "unknown"
    }

    // 🔧 BƯỚC 4: Tạo input struct
    return policy.AuthorizationInput{
        Subject: policy.Subject{
            ID:             userID,
            Type:           "user", // hoặc get từ token
            OrganizationID: organizationID,
            Permissions:    permissions,
        },
        Resource: policy.Resource{
            Type:           resourceType,
            ID:             resourceID,
            OrganizationID: organizationID, // Assume same org
        },
        Action: action,
        Context: map[string]interface{}{
            "request_time": time.Now(),
            "user_agent":   c.GetHeader("User-Agent"),
        },
    }, nil
}
```

**❓ Phân tích Input Generation:**

**HTTP Request:**
```http
GET /api/v1/licenses/license-123 HTTP/1.1
Authorization: Bearer user-abc123...
```

**Generated Input:**
```go
AuthorizationInput{
    Subject: Subject{
        ID: "user-456",                    // ← Từ JWT token
        OrganizationID: "org-789",         // ← Từ JWT token  
        Permissions: ["license.read"],     // ← Từ JWT token
    },
    Resource: Resource{
        Type: "license",                   // ← Từ URL "/licenses"
        ID: "license-123",                 // ← Từ URL param ":id"
        OrganizationID: "org-789",         // ← Inherit từ user
    },
    Action: "read",                        // ← Từ HTTP method "GET"
}
```

---

## 📋 Flow 3: OPA Manager

### 3.1 OPA Manager IsAllowed (policy/opa_manager.go)

**Code thực tế:**
```go
// File: internal/domain/services/policy/opa_manager.go  
// Lines: 164-176

func (m *OPAManager) IsAllowed(ctx context.Context, input AuthorizationInput) (bool, error) {
    m.mu.RLock()         // Read lock vì chỉ đọc state
    defer m.mu.RUnlock()

    // Kiểm tra OPA có được bật và đã khởi tạo chưa
    if !m.config.Enabled || !m.initialized {
        // Nếu OPA bị tắt hoặc chưa khởi tạo thì trả về lỗi
        return false, fmt.Errorf("OPA is not enabled or initialized")
    }

    // Delegate xuống OPA service để thực hiện authorization
    return m.service.IsAllowed(ctx, input)
}
```

**❓ Phân tích:**
- **Thread safety**: `RWMutex` để concurrent access
- **State check**: Kiểm tra enabled + initialized
- **Delegation**: Forward xuống `OPAService`

### 3.2 Simple OPA Service IsAllowed (policy/opa_service.go)

**Code thực tế:**
```go
// File: internal/domain/services/policy/opa_service.go
// Lines: 270-281

func (s *SimpleOPAService) IsAllowed(ctx context.Context, input AuthorizationInput) (bool, error) {
    // Chuyển đổi AuthorizationInput thành map để truyền vào OPA
    inputMap := map[string]interface{}{
        "subject":  input.Subject,  // Thông tin về người/token thực hiện hành động
        "resource": input.Resource, // Thông tin về tài nguyên được truy cập
        "action":   input.Action,   // Hành động cần thực hiện
        "context":  input.Context,  // Thông tin bối cảnh bổ sung
    }
    
    // 🎯 Gọi policy "data.gokeys.authz.allow" để đánh giá authorization
    return s.Evaluate(ctx, "data.gokeys.authz.allow", inputMap)
}
```

**❓ Phân tích:**
- **Data transformation**: `AuthorizationInput` struct → `map[string]interface{}`
- **Fixed query**: Luôn gọi `"data.gokeys.authz.allow"`
- **Key insight**: Query path hard-coded!

### 3.3 Simple OPA Service Evaluate (policy/opa_service.go)

**Code thực tế:**
```go
// File: internal/domain/services/policy/opa_service.go
// Lines: 149-212

func (s *SimpleOPAService) EvaluateWithResult(ctx context.Context, query string, input map[string]interface{}) (interface{}, error) {
    // 🔑 BƯỚC 1: Tạo cache key từ query và input để kiểm tra cache
    cacheKey := s.generateCacheKey(query, input)
    
    // 💾 BƯỚC 2: Kiểm tra cache trước nếu cache được bật
    if s.cacheEnabled {
        if cached := s.getCachedResult(cacheKey); cached != nil {
            return cached, nil // Trả về kết quả từ cache nếu có
        }
    }
    
    // 🔧 BƯỚC 3: Tạo Rego instance với tất cả các options cần thiết
    regoOptions := []func(*rego.Rego){
        rego.Query(query),     // Query cần đánh giá: "data.gokeys.authz.allow"
        rego.Store(s.store),   // Data store chứa permissions/roles
        rego.Input(input),     // Input data cho evaluation
    }
    
    // 📜 BƯỚC 4: Thêm tất cả policies đã load vào Rego instance
    for name, policy := range s.policies {
        regoOptions = append(regoOptions, rego.Module(name, policy))
    }
    
    // 🏗️ BƯỚC 5: Tạo Rego instance mới với tất cả options
    r := rego.New(regoOptions...)
    
    // ⚡ BƯỚC 6: Thực hiện evaluation
    rs, err := r.Eval(ctx)
    if err != nil {
        return nil, fmt.Errorf("failed to evaluate query: %w", err)
    }
    
    // 📤 BƯỚC 7: Lấy kết quả từ evaluation result
    var result interface{} = false
    if len(rs) > 0 && len(rs[0].Expressions) > 0 {
        result = rs[0].Expressions[0].Value
    }
    
    // 💾 BƯỚC 8: Lưu kết quả vào cache nếu cache được bật
    if s.cacheEnabled {
        s.setCachedResult(cacheKey, result)
    }
    
    return result, nil
}
```

**❓ Phân tích Evaluation Process:**

**Input vào Evaluate:**
```go
query = "data.gokeys.authz.allow"
input = {
    "subject": {
        "id": "user-456",
        "permissions": ["license.read"]
    },
    "resource": {
        "type": "license", 
        "id": "license-123"
    },
    "action": "read"
}
```

**Rego Options:**
```go
regoOptions = [
    rego.Query("data.gokeys.authz.allow"),     // Query path
    rego.Store(s.store),                       // Data store với permissions
    rego.Input(input),                         // Input data
    rego.Module("main", policyContent),        // Policy content từ main.rego
]
```

---

## 📜 Flow 4: Rego Policy Evaluation

### 4.1 Policy File (policies/auth/main.rego)

**Code thực tế:**
```rego
# File: policies/auth/main.rego
# Lines: 1-74

# ===== GOKEYS AUTHORIZATION POLICY =====
package gokeys.authz

import rego.v1

# ===== ENTRY POINT =====
# Điểm bắt đầu chính cho authorization - mặc định từ chối tất cả
default allow = false

# ===== ADMIN PERMISSIONS =====
# Cho phép nếu user có quyền admin (wildcard permission)
allow if {
    "*" in input.subject.permissions
}

# ===== EXACT PERMISSION MATCH =====
# Cho phép nếu user có đúng permission được yêu cầu
allow if {
    required_permission in input.subject.permissions
}

# ===== WILDCARD PERMISSION MATCH =====
# Cho phép nếu user có wildcard permission khớp với resource
allow if {
    some permission in input.subject.permissions
    wildcard_match(permission, required_permission)
}

# ===== RESOURCE OWNERSHIP =====
# Cho phép nếu user là chủ sở hữu của resource
allow if {
    input.action in ["read", "update", "delete"]
    input.resource.owner_id == input.subject.id
}

# ===== ORGANIZATION SCOPE =====
# Cho phép nếu user thuộc cùng organization với resource VÀ có permission
allow if {
    input.resource.organization_id == input.subject.organization_id
    required_permission in input.subject.permissions
}

# ===== PERMISSION COMPUTATION =====
# Tính toán permission cần thiết dựa trên resource type và action
required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
}

# ===== HELPER FUNCTIONS =====
# wildcard_match: Kiểm tra xem pattern có khớp với permission không
wildcard_match(pattern, permission) if {
    parts := split(pattern, ".")
    perm_parts := split(permission, ".")
    
    # Độ dài phải giống nhau để có thể match
    count(parts) == count(perm_parts)
    
    # Kiểm tra từng phần - "*" khớp với bất kỳ giá trị nào
    every i, part in parts {
        part == "*" or part == perm_parts[i]
    }
}
```

### 4.2 Evaluation Steps với Input Cụ Thể

**Input từ Golang:**
```json
{
    "subject": {
        "id": "user-456",
        "permissions": ["license.read", "machine.create"]
    },
    "resource": {
        "type": "license",
        "id": "license-123",
        "organization_id": "org-789"
    },
    "action": "read"
}
```

**Rego Evaluation Process:**

**Step 1: Tính `required_permission`**
```rego
required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
}
# sprintf("%s.%s", ["license", "read"]) = "license.read"
# => required_permission = "license.read"
```

**Step 2: Check Rule - Admin Permission**
```rego
allow if {
    "*" in input.subject.permissions
}
# "*" in ["license.read", "machine.create"] => FALSE
```

**Step 3: Check Rule - Exact Permission Match**
```rego
allow if {
    required_permission in input.subject.permissions
}
# "license.read" in ["license.read", "machine.create"] => TRUE
# => allow = true (DỪNG LẠI)
```

**Final Result:** `allow = true`

---

## 🔧 Flow 5: Configuration & Initialization

### 5.1 OPA Manager Initialization (policy/opa_manager.go)

**Code thực tế:**
```go
// File: internal/domain/services/policy/opa_manager.go
// Lines: 24-58

func NewOPAManager(cfg *config.Config) (*OPAManager, error) {
    // Tạo cấu hình OPA mặc định nếu chưa có
    if cfg.OPA == nil {
        cfg.OPA = &config.OPAConfig{
            Enabled:      true,            // Mặc định bật OPA
            PolicyPath:   "policies",      // Thư mục chứa file .rego
            DataPath:     "policies/data", // Thư mục chứa file JSON data
            CacheEnabled: true,            // Bật cache để tối ưu hiệu suất
            CacheTTL:     300,            // Cache TTL 5 phút
        }
    }

    // Tạo OPA service instance
    service, err := NewSimpleOPAService()
    if err != nil {
        return nil, fmt.Errorf("failed to create OPA service: %w", err)
    }

    // Tạo manager instance
    manager := &OPAManager{
        service: service,
        config:  cfg.OPA,
    }

    // Khởi tạo và load policies nếu OPA được bật
    if cfg.OPA.Enabled {
        if err := manager.Initialize(context.Background()); err != nil {
            return nil, fmt.Errorf("failed to initialize OPA manager: %w", err)
        }
    }

    return manager, nil
}
```

### 5.2 Policy Loading (policy/opa_manager.go)

**Code thực tế:**
```go
// File: internal/domain/services/policy/opa_manager.go
// Lines: 122-160

func (m *OPAManager) loadPolicies(ctx context.Context) error {
    // Duyệt recursively qua tất cả file .rego trong thư mục policy
    err := filepath.Walk(m.config.PolicyPath, func(path string, info os.FileInfo, err error) error {
        if err != nil {
            return err
        }

        // Bỏ qua thư mục và file không phải .rego
        if info.IsDir() || filepath.Ext(path) != ".rego" {
            return nil
        }

        // Tính toán tên module từ đường dẫn file
        relPath, err := filepath.Rel(m.config.PolicyPath, path)
        if err != nil {
            return fmt.Errorf("failed to get relative path: %w", err)
        }

        // Chuyển đổi đường dẫn thành tên module
        // Ví dụ: auth/main.rego -> main (chỉ lấy tên file không có extension)
        moduleName := filepath.ToSlash(relPath)
        moduleName = moduleName[:len(moduleName)-5] // Loại bỏ .rego extension
        moduleName = filepath.Base(moduleName)      // Lấy tên file cuối cùng

        // Load policy file vào OPA service
        if err := m.service.LoadPolicyFromFile(ctx, moduleName, path); err != nil {
            return fmt.Errorf("failed to load policy %s: %w", path, err)
        }

        log.Printf("Loaded policy: %s as module %s", path, moduleName)
        return nil
    })

    if err != nil {
        return fmt.Errorf("failed to walk policy directory: %w", err)
    }

    return nil
}
```

**❓ Phân tích Policy Loading:**

**File Structure:**
```
policies/
├── auth/
│   └── main.rego         # package gokeys.authz
└── data/
    └── permissions.json
```

**Loading Process:**
1. **Scan files**: `filepath.Walk("policies", ...)`
2. **Filter .rego**: Chỉ process file `.rego`
3. **Module name**: `auth/main.rego` → `main`
4. **Load content**: Read file content vào OPA service
5. **Register**: OPA service store policy với key `"main"`

### 5.3 Permission Data Loading (policy/opa_manager.go)

**Code thực tế:**
```go
// File: internal/domain/services/policy/opa_manager.go
// Lines: 89-118

func (m *OPAManager) loadPermissionData(ctx context.Context) error {
    // Tạo đường dẫn đến file permissions.json
    dataFile := filepath.Join(m.config.DataPath, "permissions.json")

    // Kiểm tra xem file có tồn tại không
    if _, err := os.Stat(dataFile); os.IsNotExist(err) {
        log.Printf("Permission data file not found: %s", dataFile)
        return nil // Không có file permissions thì bỏ qua, không lỗi
    }

    // Đọc nội dung file JSON
    data, err := os.ReadFile(dataFile)
    if err != nil {
        return fmt.Errorf("failed to read permission data: %w", err)
    }

    // Parse JSON thành map để load vào OPA
    var permissions map[string]interface{}
    if err := json.Unmarshal(data, &permissions); err != nil {
        return fmt.Errorf("failed to parse permission data: %w", err)
    }

    // Cập nhật dữ liệu permissions vào OPA data store tại path "/permissions"
    if err := m.service.UpdateData(ctx, "/permissions", permissions); err != nil {
        return fmt.Errorf("failed to update permission data in OPA: %w", err)
    }

    log.Printf("Loaded permission data from %s", dataFile)
    return nil
}
```

**❓ Permission Data:**

**File: policies/data/permissions.json**
```json
{
  "token_type_permissions": {
    "environment": [
      "account.read",
      "license.create",
      "license.read",
      "license.update",
      "license.delete",
      "license.validate"
    ],
    "user": [
      "account.read",
      "license.read",
      "license.validate"
    ]
  }
}
```

**Accessible trong Rego:**
```rego
# Access permission data
env_perms := data.permissions.token_type_permissions.environment
user_perms := data.permissions.token_type_permissions.user
```

---

## 📊 Summary: Complete Request Flow

### Input
```http
GET /api/v1/licenses/license-123 HTTP/1.1
Authorization: Bearer user-abc123...
```

### Code Path
```
1. routes/api.go:169
   └── licenses.GET("/:id", authz.CreatePermissionChecker("license.read"), ...)

2. middleware/authorization.go:431
   └── CreatePermissionChecker("license.read")
       └── checkPermissionWithOPA(c, ["license.read"])

3. middleware/authorization.go:542  
   └── checkPermissionWithOPA()
       └── buildOPAInput(c) → AuthorizationInput
       └── GetOPAManager().IsAllowed(input)

4. policy/opa_manager.go:164
   └── IsAllowed(input)
       └── service.IsAllowed(input)

5. policy/opa_service.go:270
   └── IsAllowed(input)
       └── Evaluate("data.gokeys.authz.allow", inputMap)

6. policy/opa_service.go:149
   └── EvaluateWithResult()
       └── rego.New(...).Eval()

7. policies/auth/main.rego:23
   └── allow if { required_permission in input.subject.permissions }
       └── required_permission = "license.read"
       └── "license.read" in ["license.read"] → TRUE
```

### Output
```
allow = true → c.Next() → handlers.GetLicense()
```

---

## 🎯 Key Takeaways

### 1. **Fixed Elements**
- Query path: `"data.gokeys.authz.allow"` (hard-coded)
- Package: `gokeys.authz` (trong main.rego)
- Rule: `allow` (entry point)

### 2. **Dynamic Elements**  
- Resource type: từ URL path (`/licenses` → `"license"`)
- Action: từ HTTP method (`GET` → `"read"`)
- Subject: từ JWT token
- Resource ID: từ URL params

### 3. **Architecture Pattern**
- **Middleware**: Entry point cho authorization
- **Manager**: Lifecycle management
- **Service**: Core OPA integration  
- **Policy**: Business logic trong Rego

### 4. **Error Handling**
- **Primary**: OPA evaluation
- **Fallback**: Traditional permission check
- **Graceful degradation**: Không crash nếu OPA fail

**Code trong GoKeys được thiết kế để dễ debug, maintain và extend!** 🚀