// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "https://gokeys.com/terms",
        "contact": {
            "name": "GoKeys API Support",
            "url": "https://gokeys.com/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "MIT",
            "url": "https://opensource.org/licenses/MIT"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/admin/organizations": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get paginated list of all organizations in the system (system admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System Admin"
                ],
                "summary": "List all organizations",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Page size",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search organizations by name or email",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "active",
                            "suspended"
                        ],
                        "type": "string",
                        "description": "Filter by organization status",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of organizations with pagination",
                        "schema": {
                            "$ref": "#/definitions/PaginatedOrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "System admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new organization in the system (system admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System Admin"
                ],
                "summary": "Create organization",
                "parameters": [
                    {
                        "description": "Organization creation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateOrganizationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Organization created successfully",
                        "schema": {
                            "$ref": "#/definitions/OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "System admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Organization name or slug already exists",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/admin/stats": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get comprehensive system-wide statistics and metrics (system admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "System Admin"
                ],
                "summary": "Get system statistics",
                "responses": {
                    "200": {
                        "description": "System statistics and metrics",
                        "schema": {
                            "$ref": "#/definitions/SystemStatsResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "System admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/licenses/actions/validate-key": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Validate a license key across all organizations (for client applications)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Global License Operations"
                ],
                "summary": "Validate license by key (Global)",
                "parameters": [
                    {
                        "description": "License validation with scope details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/LicenseValidationByKeyRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License validation successful with license details",
                        "schema": {
                            "$ref": "#/definitions/LicenseValidationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or missing license key",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "License suspended, expired, or policy violations",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "License key not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error during validation",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/licenses/{key}/validate": {
            "post": {
                "description": "Validate a license key and return license status and information (public endpoint)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public License Validation"
                ],
                "summary": "Validate license key",
                "parameters": [
                    {
                        "description": "License validation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/LicenseValidationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License validation successful with license details",
                        "schema": {
                            "$ref": "#/definitions/LicenseValidationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or missing license key",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "License suspended, expired, or policy violations",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "License key not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error during validation",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get detailed information about a specific organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Get organization details",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Organization details",
                        "schema": {
                            "$ref": "#/definitions/OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid organization ID format",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update organization information and settings (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organizations"
                ],
                "summary": "Update organization",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Organization update details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateOrganizationRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Organization updated successfully",
                        "schema": {
                            "$ref": "#/definitions/OrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Organization name or slug already exists",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/machines": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get paginated list of machines in an organization with filtering options",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "List organization machines",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "description": "Filter by machine fingerprint",
                        "name": "fingerprint",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by IP address",
                        "name": "ip",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "active",
                            "inactive",
                            "suspended"
                        ],
                        "type": "string",
                        "description": "Filter by machine status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Page size",
                        "name": "page_size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of machines with pagination",
                        "schema": {
                            "$ref": "#/definitions/PaginatedMachineResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new machine for license activation and tracking (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Create machine",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Machine creation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateMachineRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Machine created successfully",
                        "schema": {
                            "$ref": "#/definitions/MachineResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or license not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Machine fingerprint already exists",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/machines/{machine_id}/actions/heartbeat": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update machine heartbeat to indicate it's still active (for license monitoring)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Machines"
                ],
                "summary": "Machine heartbeat",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Machine ID",
                        "name": "machine_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Heartbeat details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/MachineHeartbeatRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Heartbeat updated successfully",
                        "schema": {
                            "$ref": "#/definitions/MachineResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid machine ID format",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied or machine suspended",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Machine not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/policies": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get paginated list of policies in an organization, optionally filtered by product",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Policies"
                ],
                "summary": "List organization policies",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Filter by product ID",
                        "name": "product_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Page size",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search policies by name",
                        "name": "search",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of policies with pagination",
                        "schema": {
                            "$ref": "#/definitions/PaginatedPolicyResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get paginated list of products in an organization",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "List organization products",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Page size",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search products by name or description",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "active",
                            "inactive"
                        ],
                        "type": "string",
                        "description": "Filter by product status",
                        "name": "status",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of products with pagination",
                        "schema": {
                            "$ref": "#/definitions/PaginatedProductResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new product in an organization (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Create product",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Product creation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateProductRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Product created successfully",
                        "schema": {
                            "$ref": "#/definitions/ProductResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Product name already exists",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get detailed information about a specific product",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Products"
                ],
                "summary": "Get product details",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Product details",
                        "schema": {
                            "$ref": "#/definitions/ProductResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid product ID format",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or product not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}/licenses": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get paginated list of licenses for a specific product",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product Licenses"
                ],
                "summary": "List product licenses",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Page size",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "active",
                            "suspended",
                            "expired",
                            "revoked"
                        ],
                        "type": "string",
                        "description": "License status filter",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Policy ID filter",
                        "name": "policy_id",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of licenses with pagination",
                        "schema": {
                            "$ref": "#/definitions/PaginatedLicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or product not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new license for the product (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Product Licenses"
                ],
                "summary": "Create license",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "License creation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreateLicenseRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "License created successfully",
                        "schema": {
                            "$ref": "#/definitions/LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, product, or policy not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}/licenses/{license_id}": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get detailed information about a specific license",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Licenses"
                ],
                "summary": "Get license details",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License details",
                        "schema": {
                            "$ref": "#/definitions/LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid license ID format",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, product, or license not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update license information and configuration (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Licenses"
                ],
                "summary": "Update license",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "License update details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateLicenseRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License updated successfully",
                        "schema": {
                            "$ref": "#/definitions/LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, product, or license not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Delete a license and all associated machines (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Licenses"
                ],
                "summary": "Delete license",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "204": {
                        "description": "License deleted successfully"
                    },
                    "400": {
                        "description": "Invalid license ID format",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization, product, or license not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}/licenses/{license_id}/actions/increment-usage": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Increment the usage count for a license (for usage-based licensing)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Actions"
                ],
                "summary": "Increment license usage",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Usage increment details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UsageActionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Usage incremented successfully",
                        "schema": {
                            "$ref": "#/definitions/LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or usage limit exceeded",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied or license suspended",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "License not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}/licenses/{license_id}/actions/reinstate": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Reinstate a suspended license, allowing its use again (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Actions"
                ],
                "summary": "Reinstate license",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Reinstatement details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/LicenseActionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License reinstated successfully",
                        "schema": {
                            "$ref": "#/definitions/LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid license ID format or license not suspended",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "License not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}/licenses/{license_id}/actions/revoke": {
            "delete": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Permanently revoke a license, making it unusable (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Actions"
                ],
                "summary": "Revoke license",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Revocation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/LicenseActionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License revoked successfully",
                        "schema": {
                            "$ref": "#/definitions/LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid license ID format or license already revoked",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "License not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}/licenses/{license_id}/actions/suspend": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Suspend a license, preventing its use until reinstated (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Actions"
                ],
                "summary": "Suspend license",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Suspension details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/LicenseActionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License suspended successfully",
                        "schema": {
                            "$ref": "#/definitions/LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid license ID format or license already suspended",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "License not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}/licenses/{license_id}/actions/validate": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Quickly validate a license by its ID (internal validation without machine fingerprinting)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "License Actions"
                ],
                "summary": "Quick validate license by ID",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "License ID",
                        "name": "license_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License validation result",
                        "schema": {
                            "$ref": "#/definitions/LicenseValidationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid license ID format",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Access denied or license suspended",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "License not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/products/{product_id}/policies": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Create a new policy for a product with licensing rules and constraints (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Policies"
                ],
                "summary": "Create policy",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Product ID",
                        "name": "product_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Policy creation details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/CreatePolicyRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Policy created successfully",
                        "schema": {
                            "$ref": "#/definitions/PolicyResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or product not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Policy name already exists",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/organizations/{organization_id}/users": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get paginated list of users in an organization (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization Users"
                ],
                "summary": "List organization users",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "Page size",
                        "name": "page_size",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Search users by name or email",
                        "name": "search",
                        "in": "query"
                    },
                    {
                        "enum": [
                            "admin",
                            "member",
                            "viewer"
                        ],
                        "type": "string",
                        "description": "Filter by user role",
                        "name": "role",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of organization users with pagination",
                        "schema": {
                            "$ref": "#/definitions/PaginatedUserResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request parameters",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Add a user to an organization with specified role (admin access required)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Organization Users"
                ],
                "summary": "Add user to organization",
                "parameters": [
                    {
                        "type": "string",
                        "format": "uuid",
                        "description": "Organization ID",
                        "name": "organization_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "User addition details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/AddUserToOrganizationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "User added to organization successfully",
                        "schema": {
                            "$ref": "#/definitions/UserOrganizationResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Admin access required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "Organization or user not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "User already in organization",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/public/auth/login": {
            "post": {
                "description": "Authenticate user with email and password, returns JWT token and user information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "User login",
                "parameters": [
                    {
                        "description": "Login credentials",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/LoginRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Successful authentication with JWT token and user details",
                        "schema": {
                            "$ref": "#/definitions/LoginResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or missing required fields",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Invalid credentials or account banned",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/public/auth/register": {
            "post": {
                "description": "Register a new user account with optional organization creation",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Authentication"
                ],
                "summary": "User registration",
                "parameters": [
                    {
                        "description": "Registration details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/RegisterRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Successful registration with JWT token and user details",
                        "schema": {
                            "$ref": "#/definitions/RegisterResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format, validation errors, or email already exists",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error during user creation",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/public/licenses/{key}/info": {
            "get": {
                "description": "Get basic license information by license key (public endpoint for client applications)",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Public License Validation"
                ],
                "summary": "Get license information",
                "parameters": [
                    {
                        "type": "string",
                        "description": "License key",
                        "name": "key",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "License information",
                        "schema": {
                            "$ref": "#/definitions/LicenseResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid license key format",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "License suspended or revoked",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "License key not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        },
        "/user/profile": {
            "get": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Get the authenticated user's profile information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Profile"
                ],
                "summary": "Get current user profile",
                "responses": {
                    "200": {
                        "description": "User profile information",
                        "schema": {
                            "$ref": "#/definitions/UserResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Update the authenticated user's profile information",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "User Profile"
                ],
                "summary": "Update current user profile",
                "parameters": [
                    {
                        "description": "User profile update details",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateUserRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Updated user profile",
                        "schema": {
                            "$ref": "#/definitions/UserResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format or validation errors",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Authentication required",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "404": {
                        "description": "User not found",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "409": {
                        "description": "Email already exists",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/ErrorResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "AddUserToOrganizationRequest": {
            "description": "Request to add user to organization",
            "type": "object",
            "required": [
                "role"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "role": {
                    "type": "string",
                    "example": "member"
                },
                "send_email": {
                    "type": "boolean",
                    "example": true
                },
                "user_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                }
            }
        },
        "CreateLicenseRequest": {
            "description": "License creation details",
            "type": "object",
            "required": [
                "policy_id"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-01-15T10:30:00Z"
                },
                "max_machines": {
                    "type": "integer",
                    "example": 10
                },
                "max_usage": {
                    "type": "integer",
                    "example": 1000
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise License"
                },
                "policy_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440001"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                }
            }
        },
        "CreateMachineRequest": {
            "description": "Machine creation details with fingerprint and system information",
            "type": "object",
            "required": [
                "fingerprint",
                "name"
            ],
            "properties": {
                "cores": {
                    "type": "integer",
                    "example": 8
                },
                "fingerprint": {
                    "type": "string",
                    "example": "machine-fingerprint-hash"
                },
                "hostname": {
                    "type": "string",
                    "example": "prod-server-01"
                },
                "ip_address": {
                    "type": "string",
                    "example": "*************"
                },
                "memory": {
                    "type": "integer",
                    "example": 16777216
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Production Server 01"
                },
                "platform": {
                    "type": "string",
                    "example": "linux"
                }
            }
        },
        "CreateOrganizationRequest": {
            "description": "Organization creation details",
            "type": "object",
            "required": [
                "name",
                "slug"
            ],
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Enterprise software company"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise Corp"
                },
                "settings": {
                    "type": "object",
                    "additionalProperties": true
                },
                "slug": {
                    "type": "string",
                    "example": "enterprise-corp"
                },
                "website": {
                    "type": "string",
                    "example": "https://enterprise.com"
                }
            }
        },
        "CreatePolicyRequest": {
            "description": "Policy creation details",
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "duration": {
                    "type": "integer",
                    "example": 31536000
                },
                "floating": {
                    "type": "boolean",
                    "example": false
                },
                "heartbeat_duration": {
                    "type": "integer",
                    "example": 3600
                },
                "machine_uniqueness_strategy": {
                    "type": "string",
                    "example": "unique_per_license"
                },
                "max_cores": {
                    "type": "integer",
                    "example": 8
                },
                "max_machines": {
                    "type": "integer",
                    "example": 10
                },
                "max_processes": {
                    "type": "integer",
                    "example": 5
                },
                "max_uses": {
                    "type": "integer",
                    "example": 1000
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise Policy"
                },
                "require_heartbeat": {
                    "type": "boolean",
                    "example": true
                },
                "strict": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "CreateProductRequest": {
            "description": "Product creation details",
            "type": "object",
            "required": [
                "name"
            ],
            "properties": {
                "code": {
                    "type": "string",
                    "example": "enterprise-software"
                },
                "description": {
                    "type": "string",
                    "example": "Comprehensive enterprise software solution"
                },
                "distribution_strategy": {
                    "type": "string",
                    "example": "licensed"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise Software"
                },
                "platforms": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "linux",
                        "windows",
                        "macos"
                    ]
                },
                "url": {
                    "type": "string",
                    "example": "https://enterprise.com/software"
                }
            }
        },
        "ErrorResponse": {
            "description": "Standard error response format",
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer",
                    "example": 400
                },
                "details": {
                    "type": "object",
                    "additionalProperties": true
                },
                "error": {
                    "type": "string",
                    "example": "validation_failed"
                },
                "message": {
                    "type": "string",
                    "example": "Request validation failed"
                }
            }
        },
        "LicenseActionRequest": {
            "description": "License action details (suspend, reinstate, revoke)",
            "type": "object",
            "properties": {
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "reason": {
                    "type": "string",
                    "example": "Policy violation"
                }
            }
        },
        "LicenseResponse": {
            "description": "License information",
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-12-31T23:59:59Z"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "key": {
                    "type": "string",
                    "example": "GLK-1234-5678-ABCD-EFGH"
                },
                "last_validated": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise License"
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440001"
                },
                "policy_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440003"
                },
                "product_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                },
                "status": {
                    "type": "string",
                    "example": "active"
                },
                "suspended": {
                    "type": "boolean",
                    "example": false
                },
                "updated_at": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "user_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440004"
                },
                "uses": {
                    "type": "integer",
                    "example": 42
                },
                "validation_count": {
                    "type": "integer",
                    "example": 1337
                }
            }
        },
        "LicenseValidationByKeyRequest": {
            "description": "License validation request with key and optional scope",
            "type": "object",
            "required": [
                "key"
            ],
            "properties": {
                "key": {
                    "type": "string",
                    "example": "GLK-1234-5678-ABCD-EFGH"
                },
                "scope": {
                    "type": "object",
                    "properties": {
                        "fingerprint": {
                            "type": "string",
                            "example": "machine-fingerprint-abc123"
                        },
                        "metadata": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "LicenseValidationRequest": {
            "description": "License validation request payload",
            "type": "object",
            "required": [
                "license_key"
            ],
            "properties": {
                "license_key": {
                    "type": "string",
                    "example": "GLK-1234-5678-ABCD-EFGH"
                },
                "machine_fingerprint": {
                    "type": "string",
                    "example": "fp-machine-12345"
                },
                "machine_metadata": {
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                }
            }
        },
        "LicenseValidationResponse": {
            "description": "License validation result",
            "type": "object",
            "properties": {
                "errors": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-12-31T23:59:59Z"
                },
                "license": {
                    "$ref": "#/definitions/LicenseResponse"
                },
                "machines_allowed": {
                    "type": "integer",
                    "example": 5
                },
                "machines_used": {
                    "type": "integer",
                    "example": 3
                },
                "organization": {
                    "$ref": "#/definitions/OrganizationResponse"
                },
                "policy": {
                    "$ref": "#/definitions/PolicyResponse"
                },
                "ttl": {
                    "type": "integer",
                    "example": 3600
                },
                "valid": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "LoginRequest": {
            "description": "User login credentials",
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "group_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "password": {
                    "type": "string",
                    "example": "secretpassword"
                }
            }
        },
        "LoginResponse": {
            "description": "Login result with JWT token",
            "type": "object",
            "properties": {
                "expires_at": {
                    "type": "string",
                    "example": "2024-01-16T10:30:00Z"
                },
                "refresh_token": {
                    "type": "string",
                    "example": "refresh_token_here"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                },
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                },
                "user": {
                    "$ref": "#/definitions/UserResponse"
                }
            }
        },
        "MachineHeartbeatRequest": {
            "description": "Heartbeat details with optional system metrics",
            "type": "object",
            "properties": {
                "cpu_usage": {
                    "type": "number",
                    "example": 45.2
                },
                "disk_usage": {
                    "type": "number",
                    "example": 32.1
                },
                "memory_usage": {
                    "type": "number",
                    "example": 68.5
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "uptime": {
                    "type": "integer",
                    "example": 86400
                }
            }
        },
        "MachineResponse": {
            "description": "Machine information",
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "fingerprint": {
                    "type": "string",
                    "example": "fp-machine-12345"
                },
                "hostname": {
                    "type": "string",
                    "example": "prod-srv-01"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "ip": {
                    "type": "string",
                    "example": "*************"
                },
                "last_seen": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "license_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Production Server 01"
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440001"
                },
                "platform": {
                    "type": "string",
                    "example": "linux"
                },
                "require_license": {
                    "type": "boolean",
                    "example": true
                },
                "updated_at": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                }
            }
        },
        "OrganizationResponse": {
            "description": "Organization information",
            "type": "object",
            "properties": {
                "billing_email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "created_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise Corp"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                },
                "slug": {
                    "type": "string",
                    "example": "enterprise-corp"
                },
                "suspended": {
                    "type": "boolean",
                    "example": false
                },
                "updated_at": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                }
            }
        },
        "PaginatedLicenseResponse": {
            "description": "Paginated license response",
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/LicenseResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/PaginationInfo"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "PaginatedMachineResponse": {
            "description": "Paginated machine response",
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/MachineResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/PaginationInfo"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "PaginatedOrganizationResponse": {
            "description": "Paginated organization response",
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/OrganizationResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/PaginationInfo"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "PaginatedPolicyResponse": {
            "description": "Paginated policy response",
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/PolicyResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/PaginationInfo"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "PaginatedProductResponse": {
            "description": "Paginated product response",
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/ProductResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/PaginationInfo"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "PaginatedUserResponse": {
            "description": "Paginated user response",
            "type": "object",
            "properties": {
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/UserResponse"
                    }
                },
                "pagination": {
                    "$ref": "#/definitions/PaginationInfo"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "PaginationInfo": {
            "description": "Pagination metadata",
            "type": "object",
            "properties": {
                "page": {
                    "type": "integer",
                    "example": 1
                },
                "page_size": {
                    "type": "integer",
                    "example": 20
                },
                "total": {
                    "type": "integer",
                    "example": 150
                },
                "total_pages": {
                    "type": "integer",
                    "example": 8
                }
            }
        },
        "PolicyResponse": {
            "description": "Policy configuration",
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "duration": {
                    "type": "string",
                    "example": "P1Y"
                },
                "floating": {
                    "type": "boolean",
                    "example": false
                },
                "heartbeat_duration": {
                    "type": "string",
                    "example": "PT1H"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "max_machines": {
                    "type": "integer",
                    "example": 5
                },
                "max_processes": {
                    "type": "integer",
                    "example": 10
                },
                "max_users": {
                    "type": "integer",
                    "example": 100
                },
                "max_uses": {
                    "type": "integer",
                    "example": 1000
                },
                "name": {
                    "type": "string",
                    "example": "Standard Policy"
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440001"
                },
                "product_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440002"
                },
                "protected": {
                    "type": "boolean",
                    "example": false
                },
                "require_heartbeat": {
                    "type": "boolean",
                    "example": true
                },
                "scheme": {
                    "type": "string",
                    "example": "RSA-2048"
                },
                "strict": {
                    "type": "boolean",
                    "example": true
                },
                "updated_at": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                }
            }
        },
        "ProductResponse": {
            "description": "Product information",
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "distribution_strategy": {
                    "type": "string",
                    "example": "licensed"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise Software"
                },
                "organization_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440001"
                },
                "platforms": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "linux",
                        "windows",
                        "macos"
                    ]
                },
                "updated_at": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "url": {
                    "type": "string",
                    "example": "https://enterprise.com/software"
                }
            }
        },
        "RegisterRequest": {
            "description": "User registration details",
            "type": "object",
            "required": [
                "email",
                "password"
            ],
            "properties": {
                "create_personal_group": {
                    "type": "boolean",
                    "example": true
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "first_name": {
                    "type": "string",
                    "example": "John"
                },
                "group_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "last_name": {
                    "type": "string",
                    "example": "Doe"
                },
                "password": {
                    "type": "string",
                    "example": "SecurePassword123!"
                }
            }
        },
        "RegisterResponse": {
            "description": "Registration response with JWT token and user information",
            "type": "object",
            "properties": {
                "expires_at": {
                    "type": "string",
                    "example": "2024-01-16T10:30:00Z"
                },
                "organization": {
                    "$ref": "#/definitions/OrganizationResponse"
                },
                "refresh_token": {
                    "type": "string",
                    "example": "refresh_token_here"
                },
                "success": {
                    "type": "boolean",
                    "example": true
                },
                "token": {
                    "type": "string",
                    "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                },
                "user": {
                    "$ref": "#/definitions/UserResponse"
                }
            }
        },
        "SystemStatsResponse": {
            "description": "Comprehensive system statistics",
            "type": "object",
            "properties": {
                "licenses": {
                    "type": "object",
                    "properties": {
                        "active": {
                            "type": "integer",
                            "example": 1350
                        },
                        "expired": {
                            "type": "integer",
                            "example": 100
                        },
                        "suspended": {
                            "type": "integer",
                            "example": 50
                        },
                        "total": {
                            "type": "integer",
                            "example": 1500
                        }
                    }
                },
                "machines": {
                    "type": "object",
                    "properties": {
                        "active": {
                            "type": "integer",
                            "example": 750
                        },
                        "total": {
                            "type": "integer",
                            "example": 800
                        }
                    }
                },
                "organizations": {
                    "type": "object",
                    "properties": {
                        "active": {
                            "type": "integer",
                            "example": 23
                        },
                        "total": {
                            "type": "integer",
                            "example": 25
                        }
                    }
                },
                "usage": {
                    "type": "object",
                    "properties": {
                        "error_rate": {
                            "type": "number",
                            "example": 0.02
                        },
                        "validation_rate_per_hour": {
                            "type": "number",
                            "example": 208.3
                        },
                        "validations_today": {
                            "type": "integer",
                            "example": 5000
                        }
                    }
                },
                "users": {
                    "type": "object",
                    "properties": {
                        "active": {
                            "type": "integer",
                            "example": 142
                        },
                        "total": {
                            "type": "integer",
                            "example": 150
                        }
                    }
                }
            }
        },
        "UpdateLicenseRequest": {
            "description": "License update details",
            "type": "object",
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "expires_at": {
                    "type": "string",
                    "example": "2025-12-31T23:59:59Z"
                },
                "max_machines": {
                    "type": "integer",
                    "example": 20
                },
                "max_usage": {
                    "type": "integer",
                    "example": 2000
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Updated Enterprise License"
                },
                "protected": {
                    "type": "boolean",
                    "example": true
                }
            }
        },
        "UpdateOrganizationRequest": {
            "description": "Organization update details",
            "type": "object",
            "properties": {
                "description": {
                    "type": "string",
                    "example": "Updated description"
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                },
                "name": {
                    "type": "string",
                    "example": "Enterprise Corp Updated"
                },
                "settings": {
                    "type": "object",
                    "additionalProperties": true
                },
                "website": {
                    "type": "string",
                    "example": "https://enterprise-updated.com"
                }
            }
        },
        "UpdateUserRequest": {
            "description": "User profile update details",
            "type": "object",
            "properties": {
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "first_name": {
                    "type": "string",
                    "example": "John"
                },
                "last_name": {
                    "type": "string",
                    "example": "Doe"
                }
            }
        },
        "UsageActionRequest": {
            "description": "Usage increment/decrement details",
            "type": "object",
            "properties": {
                "decrement": {
                    "type": "integer",
                    "example": 3
                },
                "increment": {
                    "type": "integer",
                    "example": 5
                },
                "metadata": {
                    "type": "object",
                    "additionalProperties": true
                }
            }
        },
        "UserOrganizationResponse": {
            "description": "User organization membership details",
            "type": "object",
            "properties": {
                "joined_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "organization": {
                    "$ref": "#/definitions/OrganizationResponse"
                },
                "permissions": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "example": [
                        "[\"license.read\"",
                        " \"machine.read\"]"
                    ]
                },
                "role": {
                    "type": "string",
                    "example": "admin"
                }
            }
        },
        "UserResponse": {
            "description": "User information",
            "type": "object",
            "properties": {
                "banned": {
                    "type": "boolean",
                    "example": false
                },
                "banned_at": {
                    "type": "string"
                },
                "banned_reason": {
                    "type": "string"
                },
                "created_at": {
                    "type": "string",
                    "example": "2024-01-01T00:00:00Z"
                },
                "email": {
                    "type": "string",
                    "example": "<EMAIL>"
                },
                "email_verified": {
                    "type": "boolean",
                    "example": true
                },
                "email_verified_at": {
                    "type": "string",
                    "example": "2024-01-01T12:00:00Z"
                },
                "first_name": {
                    "type": "string",
                    "example": "John"
                },
                "group_id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440001"
                },
                "id": {
                    "type": "string",
                    "example": "550e8400-e29b-41d4-a716-446655440000"
                },
                "last_login_at": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "last_name": {
                    "type": "string",
                    "example": "Doe"
                },
                "role": {
                    "type": "string",
                    "example": "admin"
                },
                "updated_at": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "API key for authentication",
            "type": "apiKey",
            "name": "X-API-Key",
            "in": "header"
        },
        "BearerAuth": {
            "description": "Type \"Bearer\" followed by a space and JWT token.",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        },
        "LicenseKeyAuth": {
            "description": "License key for validation endpoints",
            "type": "apiKey",
            "name": "X-License-Key",
            "in": "header"
        }
    },
    "tags": [
        {
            "description": "Authentication and authorization endpoints",
            "name": "Authentication"
        },
        {
            "description": "System administration endpoints (organizations, users, statistics)",
            "name": "System Admin"
        },
        {
            "description": "Organization management and configuration",
            "name": "Organizations"
        },
        {
            "description": "Organization user management and permissions",
            "name": "Organization Users"
        },
        {
            "description": "Product management within organizations",
            "name": "Products"
        },
        {
            "description": "Policy configuration and management",
            "name": "Policies"
        },
        {
            "description": "License management, validation, and lifecycle operations",
            "name": "Licenses"
        },
        {
            "description": "License validation, suspension, renewal, and usage operations",
            "name": "License Actions"
        },
        {
            "description": "Machine registration, tracking, and monitoring",
            "name": "Machines"
        },
        {
            "description": "User profile and account management",
            "name": "User Profile"
        },
        {
            "description": "Global license and machine operations",
            "name": "Global Operations"
        },
        {
            "description": "System health and monitoring",
            "name": "Health"
        },
        {
            "description": "System metrics and analytics",
            "name": "Metrics"
        }
    ]
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "localhost:8080",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "GoKeys License Management API",
	Description:      "Enterprise License Management Platform with comprehensive license validation, machine tracking, and policy management capabilities.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
