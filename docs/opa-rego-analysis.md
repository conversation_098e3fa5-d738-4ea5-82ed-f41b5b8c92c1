# Tài Liệu Phân Tích: OpenPolicyAgent & <PERSON><PERSON> trong GoKeys

## Mục <PERSON>
1. [Giới Thiệu OpenPolicyAgent (OPA)](#giới-thiệu-openpolicyagent-opa)
2. [<PERSON>ô<PERSON>](#ngôn-ngữ-rego-cơ-bản)
3. [Kiến Trúc OPA trong GoKeys](#kiến-trúc-opa-trong-gokeys)
4. [Flow Xử Lý Authorization](#flow-xử-lý-authorization)
5. [<PERSON><PERSON> D<PERSON>ực Tế](#ví-dụ-thực-tế)
6. [So Sánh: Tr<PERSON>ớ<PERSON> vs Sau OPA](#so-sánh-trước-vs-sau-opa)
7. [Best Practices](#best-practices)

---

## Giới Thiệu OpenPolicyAgent (OPA)

### OPA là gì?

**OpenPolicyAgent (OPA)** là một engine để đánh giá policies theo cách **declarative**. Thay vì viết code imperative (if-else), bạn định nghĩa **rules** và OPA sẽ tự động evaluate.

### Tại sao cần OPA?

#### ❌ **Trước OPA (Hard-coded Authorization)**
```go
// Code cứng trong ứng dụng
func CheckPermission(userRole string, action string, resource string) bool {
    if userRole == "admin" {
        return true
    }
    
    if userRole == "user" && action == "read" {
        return true
    }
    
    if userRole == "manager" && resource == "license" {
        return true
    }
    
    // ... hàng trăm dòng if-else khác
    return false
}
```

**Vấn đề:**
- ❌ Thay đổi logic cần rebuild toàn bộ app
- ❌ Code phình to, khó maintain
- ❌ Không thể test policies riêng biệt
- ❌ Business rules lẫn với technical code

#### ✅ **Sau OPA (Policy-based Authorization)**
```rego
# File policy.rego - Tách biệt hoàn toàn
package myapp.authz

allow if {
    input.user.role == "admin"
}

allow if {
    input.user.role == "user"
    input.action == "read"
}

allow if {
    input.user.role == "manager"
    input.resource == "license"
}
```

```go
// Code Golang đơn giản
func CheckPermission(user User, action string, resource string) bool {
    input := map[string]interface{}{
        "user": user,
        "action": action,
        "resource": resource,
    }
    
    return opa.Evaluate("data.myapp.authz.allow", input)
}
```

**Lợi ích:**
- ✅ Thay đổi policy không cần rebuild app
- ✅ Code clean, dễ đọc
- ✅ Test policies độc lập
- ✅ Business rules tách biệt

---

## Ngôn Ngữ Rego Cơ Bản

### 1. Syntax Cơ Bản

#### Package Declaration
```rego
package myapp.authz  # Định nghĩa namespace
```

#### Rules & Facts
```rego
# Rule cơ bản - trả về true/false
allow if {
    input.user.role == "admin"
}

# Rule với điều kiện - chỉ true khi conditions match
allow if {
    input.user.role == "user"
    input.action == "read"  # AND condition
}

# Rule với OR logic
allow if {
    input.user.role in ["admin", "manager"]
}
```

#### Variables & Assignment
```rego
# Biến với giá trị tính toán
user_permissions := input.user.permissions

# Rule sử dụng biến
allow if {
    "license.read" in user_permissions
}
```

### 2. Data Types trong Rego

```rego
# String
name := "John Doe"

# Number
age := 30

# Boolean
is_active := true

# Array
roles := ["admin", "user", "manager"]

# Object
user := {
    "id": "123",
    "name": "John",
    "roles": ["admin"]
}

# Set
permissions := {"read", "write", "delete"}
```

### 3. Operators & Functions

```rego
# Comparison operators
allow if { input.age >= 18 }
allow if { input.status != "banned" }

# Membership
allow if { "admin" in input.user.roles }

# String operations
allow if { startswith(input.resource, "license") }
allow if { contains(input.path, "/api/") }

# Array operations
allow if { count(input.permissions) > 0 }

# Iteration
allow if {
    some permission in input.permissions
    startswith(permission, "license.")
}
```

### 4. Advanced Rego Concepts

#### Comprehensions
```rego
# Tạo array mới từ array cũ
admin_users := [user | 
    user := input.users[_]
    "admin" in user.roles
]

# Tạo object mới
user_permissions := {user.id: user.permissions | 
    user := input.users[_]
}
```

#### Functions
```rego
# Function helper
is_weekend(day) if {
    day in ["saturday", "sunday"]
}

# Sử dụng function
allow if {
    is_weekend(input.current_day)
    input.action == "relax"
}
```

---

## Kiến Trúc OPA trong GoKeys

### 1. Tổng Quan Kiến Trúc

```
┌─────────────────────────────────────────────────────────────┐
│                    HTTP Request                             │
│  GET /api/v1/licenses/123                                   │
│  Headers: Authorization: Bearer user-abc123...             │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│             Authentication Middleware                       │
│  • Verify JWT token                                        │
│  • Extract user info                                       │
│  • Set context: user_id, organization_id, permissions      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│             Authorization Middleware                        │
│  • CreatePermissionChecker("license.read")                 │
│  • Build OPA input từ context                              │
│  • Call OPA engine để evaluate                             │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  OPA Manager                                │
│  • Quản lý OPA service lifecycle                           │
│  • Load policies từ files                                  │
│  • IsAllowed(input) -> bool                                │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│               SimpleOPAService                              │
│  • Embedded OPA engine                                     │
│  • Evaluate Rego policies                                  │
│  • Cache kết quả                                           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Rego Policies                                │
│  • policies/auth/main.rego                                 │
│  • policies/data/permissions.json                          │
└─────────────────────────────────────────────────────────────┘
```

### 2. Component Details

#### A. OPA Input Structure
```go
// Cấu trúc input được gửi vào OPA
type AuthorizationInput struct {
    Subject  Subject    `json:"subject"`   // Ai đang request?
    Resource Resource   `json:"resource"`  // Resource nào?
    Action   string     `json:"action"`    // Action gì?
    Context  map[string]interface{} `json:"context"` // Thông tin thêm
}

type Subject struct {
    ID             string   `json:"id"`              // "user-123"
    Type           string   `json:"type"`            // "user", "environment", "license"
    OrganizationID string   `json:"organization_id"` // Organization scope
    Permissions    []string `json:"permissions"`     // ["license.read", "machine.create"]
}

type Resource struct {
    Type           string `json:"type"`            // "license", "machine", "user"
    ID             string `json:"id"`              // "license-456"
    OrganizationID string `json:"organization_id"` // Organization của resource
    OwnerID        string `json:"owner_id"`        // Chủ sở hữu resource
}
```

#### B. Rego Policy Structure
```rego
# policies/auth/main.rego
package gokeys.authz

# Entry point - mặc định deny all
default allow = false

# Rule 1: Admin có thể làm mọi thứ
allow if {
    "*" in input.subject.permissions
}

# Rule 2: Exact permission match
allow if {
    required_permission in input.subject.permissions
}

# Rule 3: Wildcard permission match
allow if {
    some permission in input.subject.permissions
    wildcard_match(permission, required_permission)
}

# Rule 4: Resource ownership
allow if {
    input.action in ["read", "update", "delete"]
    input.resource.owner_id == input.subject.id
}

# Rule 5: Organization scope
allow if {
    input.resource.organization_id == input.subject.organization_id
    required_permission in input.subject.permissions
}

# Helper: Tính permission cần thiết
required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
}

# Helper: Wildcard matching
wildcard_match(pattern, permission) if {
    parts := split(pattern, ".")
    perm_parts := split(permission, ".")
    count(parts) == count(perm_parts)
    every i, part in parts {
        part == "*" or part == perm_parts[i]
    }
}
```

---

## Flow Xử Lý Authorization

### Step-by-Step Process

#### 1. **HTTP Request Arrives**
```http
GET /api/v1/licenses/123 HTTP/1.1
Authorization: Bearer user-abc123def456...v3
```

#### 2. **Authentication Middleware**
```go
// internal/adapters/http/middleware/authentication.go
func (am *AuthMiddleware) RequireAuth() gin.HandlerFunc {
    return func(c *gin.Context) {
        // Parse JWT token
        token := extractTokenFromHeader(c)
        
        // Verify token và lấy user info
        user, err := am.verifyToken(token)
        
        // Set vào context
        c.Set("user_id", user.ID)
        c.Set("organization_id", user.OrganizationID)
        c.Set("permissions", user.Permissions)
        
        c.Next()
    }
}
```

#### 3. **Authorization Middleware**
```go
// internal/adapters/http/middleware/authorization.go
func (am *AuthorizationMiddleware) CreatePermissionChecker(requiredPermissions ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Build OPA input từ context
        input := am.buildOPAInput(c) // -> AuthorizationInput struct
        
        // Call OPA để evaluate
        allowed, err := am.serviceCoordinator.GetOPAManager().IsAllowed(ctx, input)
        
        if !allowed {
            c.JSON(403, gin.H{"error": "insufficient_permissions"})
            c.Abort()
            return
        }
        
        c.Next() // Cho phép tiếp tục
    }
}
```

#### 4. **OPA Manager**
```go
// internal/domain/services/policy/opa_manager.go
func (m *OPAManager) IsAllowed(ctx context.Context, input AuthorizationInput) (bool, error) {
    // Convert struct thành map cho OPA
    inputMap := map[string]interface{}{
        "subject":  input.Subject,
        "resource": input.Resource,
        "action":   input.Action,
        "context":  input.Context,
    }
    
    // Gọi OPA service với query "data.gokeys.authz.allow"
    return m.service.Evaluate(ctx, "data.gokeys.authz.allow", inputMap)
}
```

#### 5. **SimpleOPAService**
```go
// internal/domain/services/policy/opa_service.go
func (s *SimpleOPAService) Evaluate(ctx context.Context, query string, input map[string]interface{}) (bool, error) {
    // Tạo Rego instance với policies đã load
    r := rego.New(
        rego.Query(query),      // "data.gokeys.authz.allow"
        rego.Store(s.store),    // Data store chứa permissions
        rego.Input(input),      // Input data
        rego.Module("main", s.policies["main"]), // Load main.rego
    )
    
    // Execute evaluation
    rs, err := r.Eval(ctx)
    
    // Return boolean result
    return rs[0].Expressions[0].Value.(bool), nil
}
```

#### 6. **Rego Policy Evaluation**
```rego
# OPA nhận input:
{
    "subject": {
        "id": "user-123",
        "type": "user",
        "organization_id": "org-456",
        "permissions": ["license.read", "machine.create"]
    },
    "resource": {
        "type": "license",
        "id": "license-789",
        "organization_id": "org-456"
    },
    "action": "read"
}

# Tính required_permission = "license.read"
# Check rule: "license.read" in ["license.read", "machine.create"] -> TRUE
# Return: allow = true
```

---

## Ví Dụ Thực Tế

### Scenario 1: User Read License

#### Request
```http
GET /api/v1/licenses/license-123
Authorization: Bearer user-abc123...
```

#### Authorization Flow
```go
// 1. Route definition
router.GET("/licenses/:id", 
    authz.CreatePermissionChecker("license.read"),
    handlers.GetLicense)

// 2. Build OPA input
input := AuthorizationInput{
    Subject: Subject{
        ID: "user-456",
        Type: "user", 
        OrganizationID: "org-789",
        Permissions: ["license.read", "license.validate"],
    },
    Resource: Resource{
        Type: "license",
        ID: "license-123",
        OrganizationID: "org-789",
    },
    Action: "read",
}

// 3. OPA evaluation
```

#### Rego Policy Evaluation
```rego
# Input vào Rego:
{
    "subject": {
        "id": "user-456",
        "type": "user",
        "organization_id": "org-789", 
        "permissions": ["license.read", "license.validate"]
    },
    "resource": {
        "type": "license",
        "id": "license-123",
        "organization_id": "org-789"
    },
    "action": "read"
}

# Step 1: Tính required_permission
required_permission = sprintf("%s.%s", ["license", "read"]) 
# => required_permission = "license.read"

# Step 2: Check rules theo thứ tự:

# Rule 1: Admin check
"*" in ["license.read", "license.validate"] # => FALSE

# Rule 2: Exact permission match  
"license.read" in ["license.read", "license.validate"] # => TRUE
# => allow = true (DỪNG LẠI)
```

**Kết quả: ✅ ALLOWED**

### Scenario 2: User Delete License (Denied)

#### Request
```http
DELETE /api/v1/licenses/license-123
Authorization: Bearer user-abc123...
```

#### OPA Input
```json
{
    "subject": {
        "id": "user-456",
        "permissions": ["license.read", "license.validate"]
    },
    "resource": {
        "type": "license",
        "id": "license-123"
    },
    "action": "delete"
}
```

#### Rego Evaluation
```rego
# required_permission = "license.delete"

# Rule 1: Admin check
"*" in ["license.read", "license.validate"] # => FALSE

# Rule 2: Exact permission match
"license.delete" in ["license.read", "license.validate"] # => FALSE

# Rule 3: Wildcard match
# Không có wildcard permissions

# Rule 4: Resource ownership  
# input.resource.owner_id không được set

# Rule 5: Organization scope
# Không có "license.delete" permission

# => Tất cả rules đều FALSE
# => allow = false (default)
```

**Kết quả: ❌ DENIED**

### Scenario 3: Admin Access (Always Allowed)

#### Request
```http
DELETE /api/v1/licenses/license-123
Authorization: Bearer admin-xyz789...
```

#### OPA Input
```json
{
    "subject": {
        "id": "admin-999",
        "permissions": ["*"]
    },
    "resource": {
        "type": "license", 
        "id": "license-123"
    },
    "action": "delete"
}
```

#### Rego Evaluation
```rego
# Rule 1: Admin check
"*" in ["*"] # => TRUE
# => allow = true (DỪNG LẠI, không check rules khác)
```

**Kết quả: ✅ ALLOWED**

### Scenario 4: Wildcard Permission

#### Request
```http
POST /api/v1/licenses
Authorization: Bearer env-token123...
```

#### OPA Input  
```json
{
    "subject": {
        "id": "env-123",
        "permissions": ["license.*", "machine.read"]
    },
    "resource": {
        "type": "license"
    },
    "action": "create"
}
```

#### Rego Evaluation
```rego
# required_permission = "license.create"

# Rule 1: Admin check
"*" in ["license.*", "machine.read"] # => FALSE

# Rule 2: Exact permission match
"license.create" in ["license.*", "machine.read"] # => FALSE

# Rule 3: Wildcard match
# Check permission "license.*" vs required "license.create"
wildcard_match("license.*", "license.create"):
  - parts = ["license", "*"]  
  - perm_parts = ["license", "create"]
  - count(parts) == count(perm_parts) # => TRUE
  - parts[0] == perm_parts[0] # => "license" == "license" => TRUE
  - parts[1] == "*" # => TRUE (wildcard matches anything)
# => wildcard_match = TRUE
# => allow = true
```

**Kết quả: ✅ ALLOWED**

---

## So Sánh: Trước vs Sau OPA

### Trước OPA (Hard-coded)

#### Code Structure
```go
// internal/adapters/http/middleware/auth_permission.go (216 dòng)
const (
    PermissionLicenseRead    = "license.read"
    PermissionLicenseCreate  = "license.create"
    PermissionLicenseUpdate  = "license.update"
    PermissionLicenseDelete  = "license.delete"
    // ... 200+ constants khác
)

func RequirePermissions(permissions ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        userPermissions := getUserPermissions(c)
        
        hasPermission := false
        for _, required := range permissions {
            for _, userPerm := range userPermissions {
                if userPerm == required || userPerm == "*" {
                    hasPermission = true
                    break
                }
            }
        }
        
        if !hasPermission {
            c.JSON(403, gin.H{"error": "insufficient_permissions"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

// Route definition
router.GET("/licenses/:id", 
    RequirePermissions(PermissionLicenseRead),
    handlers.GetLicense)
```

#### Vấn đề
- ❌ **Hard-coded logic**: Không thể thay đổi mà không rebuild
- ❌ **Code duplication**: Logic giống nhau lặp lại nhiều nơi  
- ❌ **Limited flexibility**: Chỉ support exact match và wildcard đơn giản
- ❌ **No business context**: Không thể check ownership, organization scope
- ❌ **Testing difficulty**: Khó test logic authorization riêng biệt

### Sau OPA (Policy-based)

#### Code Structure
```go
// internal/adapters/http/middleware/authorization.go (Clean)
func (am *AuthorizationMiddleware) CreatePermissionChecker(requiredPermissions ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        input := am.buildOPAInput(c)
        allowed, err := am.serviceCoordinator.GetOPAManager().IsAllowed(ctx, input)
        
        if !allowed {
            c.JSON(403, gin.H{"error": "insufficient_permissions"})
            c.Abort()
            return
        }
        
        c.Next()
    }
}

// Route definition (unchanged)
router.GET("/licenses/:id",
    authz.CreatePermissionChecker("license.read"), 
    handlers.GetLicense)
```

```rego
# policies/auth/main.rego (Separate business logic)
package gokeys.authz

default allow = false

allow if { "*" in input.subject.permissions }
allow if { required_permission in input.subject.permissions }
allow if { 
    some permission in input.subject.permissions
    wildcard_match(permission, required_permission) 
}
allow if {
    input.action in ["read", "update", "delete"]
    input.resource.owner_id == input.subject.id
}

required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
}
```

#### Lợi ích
- ✅ **Dynamic policies**: Có thể update policies mà không rebuild app
- ✅ **Centralized logic**: Tất cả authorization logic ở một nơi
- ✅ **Rich context**: Support ownership, organization scope, complex conditions
- ✅ **Easy testing**: Test policies độc lập với unit tests
- ✅ **Declarative**: Business rules dễ đọc, dễ hiểu

### Performance Comparison

#### Trước OPA
```go
// O(n*m) complexity - n permissions, m required
func hasPermission(userPerms []string, required []string) bool {
    for _, req := range required {      // m iterations
        for _, perm := range userPerms { // n iterations  
            if perm == req || perm == "*" {
                return true
            }
        }
    }
    return false
}
```

#### Sau OPA
```go
// O(1) with caching + O(policy_complexity)
func (s *SimpleOPAService) Evaluate(ctx context.Context, query string, input map[string]interface{}) (bool, error) {
    // Check cache first - O(1)
    if cached := s.getCachedResult(cacheKey); cached != nil {
        return cached.(bool), nil
    }
    
    // Evaluate policy - O(policy_complexity)
    result, err := s.opaEngine.Eval(ctx, query, input)
    
    // Cache result - O(1)
    s.setCachedResult(cacheKey, result)
    
    return result, err
}
```

---

## Best Practices

### 1. Policy Organization

#### ✅ **Good Structure**
```
policies/
├── auth/
│   ├── main.rego              # Core authorization rules
│   ├── admin.rego             # Admin-specific rules  
│   └── resource_access.rego   # Resource ownership rules
├── data/
│   ├── permissions.json       # Permission definitions
│   ├── roles.json            # Role definitions
│   └── resources.json        # Resource metadata
└── tests/
    ├── auth_test.rego        # Policy unit tests
    └── test_data.json        # Test fixtures
```

#### ❌ **Bad Structure**
```
policies/
└── everything.rego           # 1000+ lines policy file
```

### 2. Policy Writing

#### ✅ **Good Practices**
```rego
# Clear, descriptive rule names
allow_admin_access if {
    "*" in input.subject.permissions
}

allow_resource_owner if {
    input.action in ["read", "update", "delete"]
    input.resource.owner_id == input.subject.id
}

# Reusable helper functions
is_admin(subject) if {
    "*" in subject.permissions
}

is_resource_owner(subject, resource) if {
    resource.owner_id == subject.id
}

# Use helpers in main rules
allow if { is_admin(input.subject) }
allow if { is_resource_owner(input.subject, input.resource) }
```

#### ❌ **Bad Practices**
```rego
# Unclear, monolithic rules
allow if {
    input.subject.permissions[_] == "*" or 
    (input.resource.owner_id == input.subject.id and input.action != "delete") or
    (input.subject.organization_id == input.resource.organization_id and 
     input.subject.permissions[_] == sprintf("%s.%s", [input.resource.type, input.action]))
}
```

### 3. Input Validation

#### ✅ **Robust Input Handling**
```rego
# Validate required fields
allow if {
    # Ensure required fields exist
    input.subject.id
    input.subject.permissions
    input.resource.type
    input.action
    
    # Main authorization logic
    required_permission in input.subject.permissions
}

# Handle missing fields gracefully
resource_organization := input.resource.organization_id if {
    input.resource.organization_id
} else := ""
```

### 4. Testing Policies

#### ✅ **Comprehensive Testing**
```rego
# policies/tests/auth_test.rego
package gokeys.authz

test_admin_access_allowed if {
    allow with input as {
        "subject": {"permissions": ["*"]},
        "resource": {"type": "license"},
        "action": "delete"
    }
}

test_user_read_allowed if {
    allow with input as {
        "subject": {"permissions": ["license.read"]},
        "resource": {"type": "license"},
        "action": "read"
    }
}

test_user_delete_denied if {
    not allow with input as {
        "subject": {"permissions": ["license.read"]},
        "resource": {"type": "license"},
        "action": "delete"
    }
}
```

### 5. Performance Optimization

#### ✅ **Caching Strategy**
```go
// Cache key generation
func (s *SimpleOPAService) generateCacheKey(query string, input map[string]interface{}) string {
    // Hash input for consistent, short keys
    hasher := sha256.New()
    hasher.Write([]byte(fmt.Sprintf("%s:%v", query, input)))
    return hex.EncodeToString(hasher.Sum(nil))[:16] // 16 chars sufficient
}

// TTL-based cache invalidation
func (s *SimpleOPAService) setCachedResult(key string, result interface{}) {
    s.cache[key] = &DecisionCacheEntry{
        Result:    result,
        ExpiresAt: time.Now().Add(s.cacheTTL), // 5 minutes default
    }
}
```

#### ✅ **Policy Hot Reloading**
```go
// Watch policy files for changes
func (m *OPAManager) WatchPolicies() {
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        return
    }
    
    for {
        select {
        case event := <-watcher.Events:
            if event.Op&fsnotify.Write == fsnotify.Write {
                m.ReloadPolicies(context.Background())
            }
        }
    }
}
```

### 6. Error Handling

#### ✅ **Graceful Degradation**
```go
func (am *AuthorizationMiddleware) CreatePermissionChecker(requiredPermissions ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // Try OPA first
        if opaManager := am.serviceCoordinator.GetOPAManager(); opaManager != nil && opaManager.IsEnabled() {
            allowed, err := am.checkPermissionWithOPA(c, requiredPermissions)
            if err == nil {
                if allowed {
                    c.Next()
                    return
                }
                c.JSON(403, gin.H{"error": "insufficient_permissions"})
                c.Abort()
                return
            }
            // Log OPA error but continue with fallback
            log.Printf("OPA evaluation failed: %v", err)
        }
        
        // Fallback to traditional permission check
        if am.checkTraditionalPermissions(c, requiredPermissions) {
            c.Next()
            return
        }
        
        c.JSON(403, gin.H{"error": "insufficient_permissions"})
        c.Abort()
    }
}
```

---

## Kết Luận

### Tại sao GoKeys chọn OPA?

1. **Flexibility**: Policies có thể thay đổi mà không cần deploy lại application
2. **Separation of Concerns**: Business logic tách biệt khỏi technical implementation  
3. **Rich Context**: Support complex authorization scenarios (ownership, organization scope)
4. **Testing**: Policies có thể được test độc lập
5. **Performance**: Caching giúp optimize repeated evaluations
6. **Future-proof**: Dễ dàng mở rộng với requirements mới

### Migration Strategy

GoKeys implement **graceful migration** với:
- ✅ OPA làm primary authorization method
- ✅ Traditional permission check làm fallback
- ✅ Zero-downtime deployment
- ✅ Backward compatibility

Điều này đảm bảo hệ thống luôn hoạt động ổn định trong quá trình chuyển đổi.

---

**Tài liệu này cung cấp foundation để hiểu và làm việc với OPA/Rego trong GoKeys. Để tìm hiểu sâu hơn, tham khảo:**
- [OPA Documentation](https://www.openpolicyagent.org/docs/)
- [Rego Language Reference](https://www.openpolicyagent.org/docs/latest/policy-language/)
- [GoKeys Authorization Implementation](./authentication-authorization.md)