# Phân tích hệ thống gokeys



## 1. <PERSON><PERSON><PERSON> trúc tổng thể và Công nghệ



gokeys là một nền tảng quản lý cấp phép và xác thực mạnh mẽ, đ<PERSON><PERSON><PERSON> thiết kế để cung cấp các dịch vụ cấp phép ph<PERSON><PERSON> mề<PERSON>, quản lý người dùng, và xử lý các sự kiện liên quan đến giấy phép. Hệ thống này được xây dựng trên một kiến trúc phân tán, sử dụng các công nghệ hiện đại để đảm bảo hiệu suất, khả năng mở rộng và bảo mật.



### 1.1. Ng<PERSON>n xếp công nghệ (Technology Stack)


gokeys sử dụng một ngăn xếp công nghệ đa dạng, bao gồm:

Golang 1.24.5
GORM - Primary choice cho development speed và maintainability
Valkey - Essential cho license validation caching
golang-migrate - Database migration management
Connection pooling - Critical cho concurrent license validations
Primary Framework: Gin cho high-performance APIs
Architecture Pattern: Modular Monolith với Hexagonal Architecture
Database: PostgreSQL (main) + Valkey (cache)
Message Queue:  NATS cho event streaming



### 1.2. Thiết lập và Cấu hình



Hệ thống được thiết kế để có thể triển khai linh hoạt, với các cấu hình được quản lý thông qua biến môi trường và các tệp cấu hình chuyên biệt. Điều này cho phép điều chỉnh dễ dàng cho các môi trường khác nhau (phát triển, thử nghiệm, sản xuất) và các yêu cầu cụ thể của khách hàng.









## 2. Các mô hình Domain chính



gokeys được xây dựng xung quanh một số mô hình domain cốt lõi, đại diện cho các thực thể và mối quan hệ chính trong hệ thống cấp phép và quản lý tài khoản.



### 2.1. Hệ thống cấp phép (Licensing System)



Hệ thống cấp phép là trái tim của gokeys, quản lý vòng đời của giấy phép phần mềm. Các thành phần chính bao gồm:



* **Licenses (Giấy phép):** Đại diện cho một giấy phép phần mềm cụ thể, bao gồm các thuộc tính như trạng thái (active, expired, suspended, banned), thời hạn, số lượng máy tối đa được phép, và các chính sách liên quan.

* **Policies (Chính sách):** Định nghĩa các quy tắc và ràng buộc cho giấy phép. Các chính sách có thể bao gồm giới hạn máy, chiến lược hết hạn, yêu cầu xác thực, và các chiến lược xác thực khác. Chúng hoạt động như các khuôn mẫu được liên kết với sản phẩm và được kế thừa bởi các giấy phép được tạo ra.

* **Products (Sản phẩm):** Đại diện cho phần mềm hoặc dịch vụ được cấp phép. Mỗi sản phẩm có thể có nhiều giấy phép và chính sách liên quan.

* **Machines (Máy):** Đại diện cho các thiết bị mà phần mềm được cài đặt và sử dụng. Hệ thống theo dõi các máy được liên kết với giấy phép, bao gồm thông tin về dấu vân tay máy (machine fingerprint) để đảm bảo tính duy nhất và ngăn chặn việc sử dụng trái phép.

* **Activations (Kích hoạt):** Ghi lại các sự kiện kích hoạt giấy phép trên một máy cụ thể.

* **Check-ins (Kiểm tra định kỳ):** Cơ chế để giấy phép hoặc máy báo cáo trạng thái định kỳ về máy chủ, giúp theo dõi việc sử dụng và thực thi các chính sách như thời hạn sử dụng hoặc giới hạn máy.



### 2.2. Quản lý người dùng và tài khoản (User and Account Management)



Hệ thống quản lý người dùng và tài khoản cung cấp các chức năng để quản lý người dùng, tài khoản và các mối quan hệ của họ:



* **Accounts (Tài khoản):** Đại diện cho các tổ chức hoặc người dùng cấp cao sở hữu các sản phẩm và giấy phép. Mỗi tài khoản có thể có nhiều người dùng và sản phẩm.

* **Users (Người dùng):** Đại diện cho người dùng cuối hoặc quản trị viên trong một tài khoản. Người dùng có thể có các vai trò khác nhau với các quyền truy cập cụ thể.

* **Organizations (Tổ chức):** (Nếu có) Có thể là một thực thể để nhóm các tài khoản hoặc người dùng lại với nhau.



### 2.3. Hệ thống Token và Vai trò (Token and Role System)



Hệ thống này quản lý các token được sử dụng để xác thực API và các vai trò để kiểm soát quyền truy cập:



* **Tokens (Token):** Các mã thông báo được sử dụng để xác thực các yêu cầu API. Các token có thể được liên kết với các thực thể khác nhau (người dùng, sản phẩm, giấy phép, môi trường) và có thể có thời hạn sử dụng, quyền hạn và phạm vi môi trường cụ thể.

* **Roles (Vai trò):** Định nghĩa các cấp độ quyền truy cập khác nhau trong hệ thống (ví dụ: admin, developer, sales_agent, support_agent, read_only, user, license, product, environment). Mỗi vai trò có một tập hợp các quyền được xác định trước.

* **Permissions (Quyền hạn):** Các quyền cụ thể cho phép thực hiện các hành động nhất định trên các tài nguyên (ví dụ: `license.read`, `user.create`, `product.update`). Hệ thống quyền hạn có cấu trúc phân cấp và hỗ trợ các quyền wildcard.









## 3. Hệ thống API và Xử lý Request



Keygen-API cung cấp một bộ API RESTful toàn diện để tương tác với các mô hình domain của nó. Các API này tuân thủ các quy ước REST tiêu chuẩn và sử dụng định dạng JSON:API cho các yêu cầu và phản hồi.



### 3.1. Quy trình xử lý yêu cầu (Request Processing Pipeline)



Mỗi yêu cầu API đi qua một quy trình xử lý nghiêm ngặt để đảm bảo tính bảo mật, hợp lệ và hiệu quả. Quy trình này bao gồm các bước chính sau:



1. **Account Scoping:** Đảm bảo rằng các hoạt động được giới hạn trong phạm vi tài khoản hiện tại của người dùng.

2. **Subscription Check:** Xác thực trạng thái đăng ký hoạt động của tài khoản.

3. **Authentication:** Yêu cầu token xác thực hợp lệ. (Chi tiết hơn ở phần 4.1)

4. **Authorization:** Kiểm tra quyền hạn sử dụng ActionPolicy để đảm bảo người dùng có quyền thực hiện hành động được yêu cầu. (Chi tiết hơn ở phần 4.2)

5. **Parameter Validation:** Xác thực cấu trúc yêu cầu và kiểu dữ liệu của các tham số.

6. **Business Logic:** Xử lý yêu cầu và cập nhật dữ liệu theo logic nghiệp vụ.

7. **Event Broadcasting:** Kích hoạt các sự kiện webhook cho các thay đổi dữ liệu, cho phép các hệ thống bên ngoài phản ứng với các cập nhật. (Chi tiết hơn ở phần 6.1)



### 3.2. License and Machine APIs



Các API này cho phép quản lý giấy phép và máy móc:



* **Quản lý giấy phép:** Tạo, đọc, cập nhật, xóa (CRUD) giấy phép, bao gồm cả việc cấp phát và thu hồi giấy phép.

* **Xác thực giấy phép:** API để phần mềm kiểm tra tính hợp lệ của giấy phép.

* **Quản lý máy:** Đăng ký, cập nhật và xóa thông tin máy, liên kết máy với giấy phép.

* **Heartbeat:** Cơ chế để máy gửi tín hiệu sống định kỳ, giúp hệ thống theo dõi trạng thái hoạt động của máy và giấy phép.



### 3.3. User Management APIs



Các API này tập trung vào việc quản lý người dùng và tài khoản:



* **Quản lý người dùng:** CRUD người dùng, bao gồm tạo, cập nhật thông tin, và quản lý vai trò.

* **Quản lý tài khoản:** CRUD tài khoản, bao gồm thông tin tài khoản và các cài đặt liên quan.



### 3.4. Policy Management APIs



Các API này cho phép quản lý các chính sách cấp phép:



* **Quản lý chính sách:** CRUD các chính sách, cho phép định nghĩa các quy tắc và ràng buộc cho giấy phép.

* **Cấu hình chính sách:** Hỗ trợ nhiều tùy chọn cấu hình cho chính sách, bao gồm giới hạn máy, chiến lược hết hạn, chiến lược xác thực, v.v.









## 4. Xác thực và Phân quyền (Authentication and Authorization)



Hệ thống Keygen-API có một hệ thống bảo mật mạnh mẽ, bao gồm nhiều phương thức xác thực và một hệ thống phân quyền chi tiết dựa trên vai trò.



### 4.1. Các phương thức xác thực (Authentication Methods)



Keygen-API hỗ trợ nhiều phương thức xác thực để phù hợp với các trường hợp sử dụng khác nhau:



* **Xác thực bằng Token:** Phương thức chính cho truy cập API và tự động hóa. Token có thể được truyền qua header `Authorization: Bearer`, `Authorization: Basic` (base64(token:)), hoặc qua tham số truy vấn (`?token=` hoặc `?auth=`). Token có thể được liên kết với người dùng, sản phẩm, giấy phép hoặc môi trường và có thể cấu hình thời gian hết hạn và quyền hạn.

* **Xác thực bằng Khóa cấp phép (License Key):** Cho phép phần mềm của người dùng cuối xác thực trực tiếp bằng khóa cấp phép, hỗ trợ quy trình kích hoạt và xác thực phần mềm.

* **Xác thực bằng Mật khẩu:** Được sử dụng cho việc đăng nhập của người dùng và truy cập quản trị thông qua giao diện cổng thông tin (portal). Yêu cầu xác thực email/mật khẩu và hỗ trợ xác thực đa yếu tố (2FA).

* **Xác thực phiên (Session Authentication):** Sử dụng cookie HTTP được mã hóa cho giao diện web của cổng thông tin. Phiên có thể tự động gia hạn khi đang hoạt động.



### 4.2. Hệ thống phân quyền (Authorization System)



Hệ thống phân quyền của Keygen-API được xây dựng trên ActionPolicy, cung cấp kiểm soát truy cập toàn diện thông qua các quyền dựa trên vai trò, cách ly môi trường và ranh giới bảo mật đa người thuê.



* **Kiến trúc cốt lõi:** Dựa trên ActionPolicy, triển khai mô hình bảo mật đa lớp với cách ly tài khoản, kiểm soát truy cập dựa trên vai trò và phạm vi môi trường.

* **Phân cấp chính sách ủy quyền:** Các chính sách ủy quyền được tổ chức theo phân cấp, với `ApplicationPolicy` cung cấp các phương thức xác minh chung và các chính sách cụ thể cho từng loại tài nguyên (ví dụ: `LicensePolicy`, `UserPolicy`, `ProductPolicy`).

* **Kiểm soát truy cập dựa trên vai trò:** Hệ thống định nghĩa nhiều vai trò (admin, developer, sales_agent, support_agent, read_only, user, license, product, environment) với các bộ quyền và phạm vi truy cập cụ thể.

* **Hệ thống quyền hạn:** Cung cấp kiểm soát truy cập chi tiết thông qua các quyền dựa trên hành động, có thể được gán cho các vai trò và token. Quyền hạn có cấu trúc phân cấp (ví dụ: `license.read`, `user.create`) và hỗ trợ quyền wildcard (`*`).

* **Cách ly môi trường (Enterprise Edition):** Hỗ trợ cách ly đa người thuê trong các tài khoản, cho phép tài nguyên được giới hạn trong các môi trường cụ thể (global, shared, isolated) với các quy tắc truy cập khác nhau.



### 4.3. Quản lý Token (Token Management)



Hệ thống quản lý token bao gồm việc tạo, tái tạo, thu hồi và quản lý các token được sử dụng để xác thực API.



* **Các loại Bearer Token:** Hệ thống hỗ trợ bốn loại bearer chính: User (Admin/Regular), Product, License, và Environment, mỗi loại có `Token Kind` và `Default Expiry` riêng.

* **Vòng đời Token:**

* **Tạo Token:** Xảy ra thông qua hành động `generate` trong `TokensController` hoặc các hành động `create` trong các controller quan hệ. Quá trình này bao gồm xác thực, ủy quyền, gán bearer, tạo phiên (nếu có) và phát sóng sự kiện `token.generated`.

* **Tái tạo Token:** Thay thế giá trị token trong khi vẫn giữ nguyên cấu hình. Hữu ích khi token bị lộ hoặc cần làm mới.

* **Thu hồi Token:** Xóa vĩnh viễn token, phát sóng sự kiện `token.revoked` và đặt lại cookie phiên nếu token hiện tại bị thu hồi.

* **Phạm vi môi trường và quyền hạn:** Token có thể được giới hạn trong các môi trường cụ thể và có thể được gán các quyền tùy chỉnh (Enterprise Edition) hoặc kế thừa quyền từ vai trò của bearer.



## 5. Xử lý nền và Sự kiện (Background Processing and Events)



Hệ thống Keygen-API sử dụng một kiến trúc xử lý không đồng bộ mạnh mẽ để xử lý các tác vụ tốn thời gian và phát sóng các sự kiện trong toàn hệ thống.



### 5.1. Hệ thống Webhook và Sự kiện (Webhook and Event System)



Hệ thống webhook và sự kiện cung cấp thông báo thời gian thực về các thay đổi tài nguyên và các sự kiện trong toàn bộ nền tảng Keygen. Hệ thống này nắm bắt các sự kiện từ các hoạt động API, xác thực giấy phép và các quy trình nền, sau đó gửi chúng đến các điểm cuối webhook đã cấu hình với logic thử lại, xác minh chữ ký và ghi nhật ký toàn diện.



* **Tổng quan hệ thống:** Hoạt động thông qua một quy trình đa giai đoạn, nắm bắt các sự kiện, xử lý chúng không đồng bộ và gửi chúng đến các điểm cuối bên ngoài với đảm bảo độ tin cậy.

* **Các thành phần cốt lõi:**

* `BroadcastEventService`: Bộ điều phối trung tâm xử lý và phân phối sự kiện. Nó chấp nhận tên sự kiện, tài khoản, tài nguyên và siêu dữ liệu tùy chọn, sau đó kích hoạt xử lý song song cho các số liệu, ghi nhật ký, thông báo và phân phối webhook.

* `WebhookWorker`: Xử lý việc gửi HTTP thực tế của các sự kiện webhook với xử lý lỗi toàn diện, tạo chữ ký và logic thử lại.

* **Các loại và mẫu sự kiện:** Các sự kiện tuân theo một mẫu đặt tên nhất quán phản ánh tài nguyên và hành động (ví dụ: `{resource}.created`, `{resource}.updated`, `{resource}.deleted`, `{resource}.validation.{result}`, `{resource}.{action}`). Các sự kiện có khối lượng lớn được phân loại đặc biệt để tối ưu hóa cơ sở dữ liệu.

* **Cấu hình và phân phối Webhook:** Các điểm cuối webhook được cấu hình cho mỗi tài khoản và có thể được giới hạn trong các sản phẩm hoặc môi trường cụ thể. Hệ thống triển khai logic thử lại phức tạp với xử lý đặc biệt cho các tình huống lỗi khác nhau (ví dụ: lỗi tạm thời, điểm cuối bị mất, lỗi mạng).



### 5.2. Xử lý công việc nền (Background Job Processing)



Keygen-API sử dụng Sidekiq làm công cụ xử lý công việc nền chính, với Redis làm backend hàng đợi công việc. Hệ thống này bao gồm một lớp xử lý hàng loạt tùy chỉnh có tên `PerformBulk` để tối ưu hóa các hoạt động thông lượng cao, nhiều hàng đợi ưu tiên và các tác vụ bảo trì định kỳ toàn diện.



* **Cấu hình Sidekiq:** Được cấu hình thông qua nhiều tệp xác định cài đặt kết nối, ưu tiên hàng đợi và middleware.

* **Hệ thống hàng đợi và ưu tiên:** Nền tảng sử dụng hệ thống hàng đợi có trọng số, trong đó trọng số cao hơn cho thấy việc xử lý thường xuyên hơn (ví dụ: `critical`, `webhooks`, `default`, `mailers`, `billing`, `metrics`, `logs`, `cron`).

* **Hệ thống PerformBulk:** Một lớp tối ưu hóa tùy chỉnh giúp xử lý hàng loạt các công việc riêng lẻ để thực thi hàng loạt, cải thiện đáng kể hiệu suất cho các hoạt động thông lượng cao như ghi nhật ký và thu thập số liệu.

* **Các công việc được lên lịch:** Nền tảng chạy nhiều tác vụ bảo trì định kỳ theo các khoảng thời gian khác nhau (ví dụ: kiểm tra hết hạn giấy phép, kiểm tra định kỳ quá hạn, dọn dẹp máy chết, v.v.).

* **Mẫu triển khai Worker:** Hầu hết các worker kế thừa từ `BaseWorker` và tuân theo các mẫu Sidekiq tiêu chuẩn, bao gồm chính sách thử lại, gán hàng đợi và xử lý lỗi. Các mẫu phổ biến bao gồm phát sóng sự kiện và giám sát heartbeat.









## 6. Hạ tầng và Middleware (Infrastructure and Middleware)



Hạ tầng và middleware của Keygen-API là nền tảng cho hiệu suất, bảo mật và khả năng quan sát của hệ thống. Nó xử lý các mối quan tâm xuyên suốt như bảo mật, xử lý lỗi, xử lý nội dung và khả năng quan sát trên tất cả các yêu cầu API.



### 6.1. Quy trình Middleware (Middleware Pipeline)



Keygen-API sử dụng một ngăn xếp Rack middleware được sắp xếp cẩn thận để xử lý tiền xử lý yêu cầu, xử lý lỗi và các vấn đề bảo mật trước khi các yêu cầu đến các bộ điều khiển ứng dụng. Các thành phần middleware cốt lõi bao gồm:



* `IgnoreForwardedHost`: Loại bỏ các tiêu đề `X-Forwarded-Host` có vấn đề từ các yêu cầu được proxy.

* `RewriteAcceptAll`: Chuẩn hóa các tiêu đề `Accept` không hợp lệ.

* `PartitionedCookies`: Thêm hỗ trợ cookie được phân vùng cho cookie `SameSite=None`.

* `RequestErrorWrapper`: Xử lý lỗi toàn diện cho các lỗi phân tích cú pháp yêu cầu khác nhau (JSON, mã hóa, tham số truy vấn, định tuyến, thời gian chờ).

* `DefaultContentType`: Đặt `application/json` làm loại nội dung mặc định cho các yêu cầu API và xử lý các trường hợp đặc biệt về loại nội dung.

* `Rack::Attack`: Cung cấp giới hạn tốc độ và bảo vệ bảo mật.



### 6.2. Giới hạn tốc độ và Bảo mật (Rate Limiting and Security)



Nền tảng triển khai giới hạn tốc độ tinh vi bằng cách sử dụng Rack::Attack với nhiều lớp bảo vệ, bao gồm danh sách trắng IP, phát hiện bot và điều tiết theo điểm cuối.



* **Kiến trúc giới hạn tốc độ:** Bao gồm kiểm tra danh sách an toàn, danh sách đen, phát hiện bot Fail2Ban và điều tiết cho các loại yêu cầu khác nhau (tổng quát, tạo token, đặt lại mật khẩu, v.v.).

* **Cấu hình giới hạn tốc độ:** Hỗ trợ nhiều chiến lược giới hạn tốc độ với các giới hạn có thể cấu hình (ví dụ: RPS/RPM chung, tạo token, đặt lại mật khẩu, đột biến mật khẩu, hoạt động MFA).

* **Phát hiện bot và quản lý IP:** Giám sát các mẫu tấn công phổ biến, triển khai cấm lũy tiến và hỗ trợ danh sách trắng/đen IP.



### 6.3. Ghi nhật ký và Giám sát (Logging and Monitoring)



Nền tảng triển khai ghi nhật ký yêu cầu và giám sát toàn diện bằng cách sử dụng Lograge với trích xuất tải trọng tùy chỉnh và tích hợp giới hạn tốc độ.



* **Kiến trúc ghi nhật ký:** Sử dụng hệ thống thông báo để nắm bắt dữ liệu yêu cầu và tạo các mục nhật ký có cấu trúc JSON.

* **Tải trọng ghi nhật ký yêu cầu:** Nắm bắt ngữ cảnh yêu cầu toàn diện, bao gồm siêu dữ liệu yêu cầu, ngữ cảnh API, xác thực, ngữ cảnh tài khoản, giới hạn tốc độ và ngữ cảnh lỗi.

* **Tiêu đề phản hồi mặc định:** Thêm các tiêu đề mặc định vào tất cả các phản hồi để giám sát và gỡ lỗi (ví dụ: tiêu đề bảo mật, tiêu đề giới hạn tốc độ, tiêu đề kiểm soát bộ nhớ cache, tiêu đề nhận dạng).



### 6.4. Hạ tầng xử lý yêu cầu (Request Processing Infrastructure)



Nền tảng bao gồm một số thành phần hạ tầng xử lý yêu cầu, lập phiên bản API và cấu hình hệ thống.



* **Lập phiên bản API và di chuyển yêu cầu:** Triển khai hệ thống di chuyển yêu cầu tinh vi cho phép tương thích ngược giữa các phiên bản API. Hệ thống xác định phiên bản yêu cầu và áp dụng các di chuyển cần thiết cho dữ liệu yêu cầu/phản hồi.

* **Cấu hình và hằng số hệ thống:** Bao gồm các cấu hình và hằng số toàn hệ thống khác nhau (ví dụ: biểu thức chính quy để xác thực UUID, phân tích cú pháp email).

