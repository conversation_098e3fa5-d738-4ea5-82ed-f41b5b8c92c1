﻿
  📋 Final Design: Resource-Based Permissions + Organization Membership

  1. Two Separate Concerns

  -- 1. Organization Membership (ai thuộc org nào)
  CREATE TABLE users_organizations (
      user_id         UUID,
      organization_id UUID,
      joined_at       TIMESTAMP DEFAULT NOW(),
      invited_by      <PERSON>UI<PERSON>,
      PRIMARY KEY (user_id, organization_id)
  );

  -- 2. Permissions (ai được làm gì)
  CREATE TABLE permissions (
      id              UUID PRIMARY KEY,
      user_id         UUID NOT NULL,
      scope           VARCHAR(255) NOT NULL,  -- "system", "org:xxx", "resource:xxx", "owner"
      resource_type   VARCHAR(50) NOT NULL,   -- "product", "license", "*"
      actions         TEXT[] NOT NULL,        -- ["create", "read", "update", "delete"]
      granted_by      <PERSON><PERSON><PERSON>,
      granted_at      TIMESTAMP DEFAULT NOW(),
      expires_at      TIMESTAMP,              -- Optional expiration

      UNIQUE(user_id, scope, resource_type)  -- Prevent duplicates
  );

  2. Clear Separation

  // Organization membership chỉ để track user thuộc org nào
  func GetUserOrganizations(userID string) []string {
      // SELECT organization_id FROM users_organizations WHERE user_id = ?
      return orgIDs
  }

  // Permissions hoàn toàn độc lập
  func GetUserPermissions(userID string) []Permission {
      // SELECT * FROM permissions WHERE user_id = ?
      return permissions
  }

  3. Authorization Flow

  func CanAccess(userID, resourceType, resourceID, action string) bool {
      // 1. Get permissions (không liên quan đến org membership)
      permissions := GetUserPermissions(userID)

      // 2. Check each permission
      for _, perm := range permissions {
          if permissionAllows(perm, resourceType, resourceID, action) {
              return true
          }
      }

      return false
  }

  // Helper: Check if resource belongs to org (khi cần)
  func resourceBelongsToOrg(resourceType, resourceID, orgID string) bool {
      switch resourceType {
      case "product":
          product := getProduct(resourceID)
          return product.OrganizationID == orgID
      case "user":
          // Special case: listing users in org
          if resourceID == orgID {
              return true // LIST action on org's users
          }
          // Check if specific user belongs to org
          return isUserInOrg(resourceID, orgID)
      default:
          // Generic check
          return getResourceOrg(resourceType, resourceID) == orgID
      }
  }

  4. Practical Examples

  -- 1. John joins Org-123
  INSERT INTO users_organizations (user_id, organization_id)
  VALUES ('john-id', 'org-123');

  -- 2. John được cấp quyền create products trong org
  INSERT INTO permissions (user_id, scope, resource_type, actions)
  VALUES ('john-id', 'org:org-123', 'product', '{"create", "read"}');

  -- 3. Jane là admin của Org-123
  INSERT INTO users_organizations (user_id, organization_id)
  VALUES ('jane-id', 'org-123');

  INSERT INTO permissions (user_id, scope, resource_type, actions)
  VALUES ('jane-id', 'org:org-123', '*', '{"*"}');

  -- 4. Mike không thuộc org nào nhưng có permission xem product cụ thể
  -- (Không cần INSERT vào users_organizations)
  INSERT INTO permissions (user_id, scope, resource_type, actions)
  VALUES ('mike-id', 'resource:product:prod-abc', 'product', '{"read"}');

  5. Common Queries

  -- Q1: List users in organization (for admin)
  SELECT u.* FROM users u
  JOIN users_organizations uo ON u.id = uo.user_id
  WHERE uo.organization_id = 'org-123';

  -- Q2: Check if user can list users in org
  SELECT 1 FROM permissions
  WHERE user_id = 'admin-id'
    AND (
      (scope = 'system' AND resource_type = '*') OR
      (scope = 'org:org-123' AND resource_type IN ('*', 'user'))
    )
    AND ('*' = ANY(actions) OR 'read' = ANY(actions));

  -- Q3: Get all permissions for a user
  SELECT * FROM permissions
  WHERE user_id = 'user-id'
    AND (expires_at IS NULL OR expires_at > NOW());

  -- Q4: Find all users with access to a specific product
  SELECT DISTINCT user_id FROM permissions
  WHERE (
      -- System admin
      (scope = 'system' AND resource_type = '*') OR
      -- Org admin
      (scope = 'org:org-123' AND resource_type IN ('*', 'product')) OR
      -- Specific product access
      (scope = 'resource:product:prod-123' AND resource_type = 'product')
  );

  6. API Implementation Examples

  // GET /organizations/:org_id/users
  func ListOrganizationUsers(c *gin.Context) {
      userID := c.GetString("user_id")
      orgID := c.Param("org_id")

      // Check permission to read users in this org
      if !CanAccess(userID, "user", orgID, "read") {
          c.JSON(403, gin.H{"error": "permission denied"})
          return
      }

      // List users
      users := getUsersInOrg(orgID)
      c.JSON(200, users)
  }

  // POST /products
  func CreateProduct(c *gin.Context) {
      userID := c.GetString("user_id")
      orgID := c.GetString("organization_id") // from JWT

      // Check permission to create product in org
      if !CanAccess(userID, "product", orgID, "create") {
          c.JSON(403, gin.H{"error": "permission denied"})
          return
      }

      // Create product
      product := createProduct(c.Request.Body, orgID)
      c.JSON(201, product)
  }

  7. Benefits của thiết kế này

  1. Separation of Concerns:
    - users_organizations: WHO is in WHICH org
    - permissions: WHO can do WHAT
  2. Flexibility:
    - User có thể có permissions mà không cần thuộc org
    - User có thể thuộc nhiều orgs
    - Permissions không phụ thuộc vào membership
  3. Simple Queries:
    - List org users: Simple JOIN
    - Check permissions: Simple SELECT
    - No complex logic
  4. Easy to Reason:
    - "John thuộc Org-123" → users_organizations
    - "John có thể create products" → permissions

  8. Migration from old system

  -- 1. Migrate organization membership
  INSERT INTO users_organizations (user_id, organization_id)
  SELECT DISTINCT user_id, organization_id
  FROM user_organizations
  WHERE organization_id IS NOT NULL;

  -- 2. Migrate permissions (như đã design)
  INSERT INTO permissions (user_id, scope, resource_type, actions)
  SELECT
      user_id,
      CASE
          WHEN organization_id IS NULL THEN 'owner'
          WHEN resource_scope = 'global' THEN CONCAT('org:', organization_id)
          WHEN resource_scope = 'specific' AND resource_id IS NOT NULL
              THEN CONCAT('resource:', resource_type, ':', resource_id)
          ELSE CONCAT('org:', organization_id)
      END as scope,
      COALESCE(resource_type, '*'),
      allowed_actions
  FROM user_organizations;