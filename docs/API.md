# GoKeys License Management API Documentation

## Overview

GoKeys is an enterprise-grade license management platform that provides comprehensive license validation, machine tracking, policy management, and webhook integration capabilities. The API offers Ruby Keygen compatibility alongside modern Go-style endpoints.

## Base URL

- **Development**: `http://localhost:8080/api/v1`
- **Production**: `https://api.gokeys.com/v1`

## Authentication

The GoKeys API supports multiple authentication methods:

### 1. Bearer Token Authentication
Used for account management and protected administrative endpoints.

```bash
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  https://api.gokeys.com/v1/licenses
```

### 2. API Key Authentication
Used for administrative operations and account-scoped access.

```bash
curl -H "X-API-Key: YOUR_API_KEY" \
  https://api.gokeys.com/v1/accounts
```

### 3. License Key Authentication
Used for license validation and machine operations.

```bash
curl -H "X-License-Key: LIC-12345-ABCDE-67890-FGHIJ" \
  https://api.gokeys.com/v1/licenses/validate
```

## Rate Limiting

All endpoints are subject to rate limiting:
- **Standard endpoints**: 100 requests per minute per account
- **Validation endpoints**: 1000 requests per minute per license
- **Administrative endpoints**: 50 requests per minute per API key

Rate limit headers are included in all responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Unix timestamp when the rate limit resets

## API Documentation Formats

The API documentation is available in multiple formats:

### OpenAPI 3.0 Specification
- **YAML**: [`/docs/openapi.yaml`](/docs/openapi.yaml) - Complete API specification
- **JSON**: [`/docs/swagger.json`](/docs/swagger.json) - JSON format for tools
- **Simplified YAML**: [`/docs/swagger.yaml`](/docs/swagger.yaml) - Core endpoints

### Interactive Documentation
- **Swagger UI**: Available at `/swagger` endpoint when server is running
- **Redoc**: Alternative documentation interface

## Quick Start Examples

### 1. License Validation (Most Common Use Case)

```bash
# Quick validation (minimal response)
curl "https://api.gokeys.com/v1/licenses/quick-validate?license_key=LIC-12345-ABCDE-67890-FGHIJ"

# Full validation with machine tracking
curl -X POST https://api.gokeys.com/v1/licenses/validate \
  -H "Content-Type: application/json" \
  -d '{
    "license_key": "LIC-12345-ABCDE-67890-FGHIJ",
    "machine_fingerprint": "fp-mac-********",
    "environment": "production",
    "machine_info": {
      "hostname": "server-01",
      "os": "linux",
      "cpu": "Intel i7"
    }
  }'
```

### 2. Account Management

```bash
# Create an account (public endpoint)
curl -X POST https://api.gokeys.com/v1/accounts \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Acme Corporation",
    "slug": "acme-corp",
    "email": "<EMAIL>",
    "metadata": {
      "plan": "enterprise",
      "industry": "technology"
    }
  }'

# Get current account info
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  https://api.gokeys.com/v1/account
```

### 3. License Management

```bash
# List licenses
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "https://api.gokeys.com/v1/licenses?page=1&per_page=20&status=ACTIVE"

# Create a license
curl -X POST https://api.gokeys.com/v1/licenses \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Enterprise License",
    "policy_id": "550e8400-e29b-41d4-a716-************",
    "expires_at": "2025-12-31T23:59:59Z",
    "metadata": {
      "plan": "enterprise",
      "features": ["api_access", "premium_support"]
    }
  }'
```

### 4. Machine Tracking

```bash
# List machines
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  "https://api.gokeys.com/v1/machines?license_id=550e8400-e29b-41d4-a716-************"

# Send heartbeat
curl -X POST https://api.gokeys.com/v1/machines/fp-mac-********/actions/heartbeats/ping \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "timestamp": "2025-07-15T10:30:00Z",
    "metadata": {
      "cpu_usage": 45.2,
      "memory_usage": 67.8
    }
  }'
```

## Response Formats

### Success Response
```json
{
  "valid": true,
  "license": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "Enterprise License",
    "key": "LIC-12345-ABCDE-67890-FGHIJ",
    "status": "ACTIVE"
  },
  "machines_used": 2,
  "machines_allowed": 5,
  "validation_time": "2025-07-15T10:30:00Z"
}
```

### Error Response
```json
{
  "error": "invalid_request",
  "message": "The license key is invalid",
  "details": {
    "field": "license_key",
    "reason": "not_found"
  },
  "timestamp": "2025-07-15T10:30:00Z"
}
```

### Pagination Response
```json
{
  "licenses": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Enterprise License",
      "key": "LIC-12345-ABCDE-67890-FGHIJ"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total": 150,
    "total_pages": 8
  }
}
```

## Error Codes

| Code | Description |
|------|-------------|
| `invalid_request` | The request is malformed or missing required fields |
| `unauthorized` | Authentication credentials are missing or invalid |
| `forbidden` | Access denied - insufficient permissions |
| `not_found` | The requested resource was not found |
| `conflict` | Resource already exists or would cause a conflict |
| `rate_limited` | Too many requests - rate limit exceeded |
| `internal_error` | An internal server error occurred |
| `validation_failed` | License validation failed |
| `license_expired` | License has expired |
| `machine_limit_exceeded` | Maximum machine limit reached |

## HTTP Status Codes

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **204 No Content**: Request successful, no content returned
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Access denied
- **404 Not Found**: Resource not found
- **409 Conflict**: Resource conflict
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

## SDKs and Client Libraries

### Official SDKs
- **Go**: [gokeys-go](https://github.com/gokeys/gokeys-go)
- **JavaScript/Node.js**: [gokeys-js](https://github.com/gokeys/gokeys-js)
- **Python**: [gokeys-python](https://github.com/gokeys/gokeys-python)
- **Ruby**: [gokeys-ruby](https://github.com/gokeys/gokeys-ruby)

### Community SDKs
- **PHP**: Available in community packages
- **Java**: Available in community packages
- **.NET**: Available in community packages

## Webhooks

GoKeys supports webhook notifications for real-time updates:

### Supported Events
- `license.created`
- `license.updated` 
- `license.deleted`
- `license.validation.succeeded`
- `license.validation.failed`
- `machine.created`
- `machine.heartbeat.ping`
- `machine.heartbeat.missed`
- `policy.violation`
- `account.updated`

### Webhook Endpoint Management
```bash
# Create webhook endpoint
curl -X POST https://api.gokeys.com/v1/webhook-endpoints \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "data": {
      "type": "webhook-endpoints",
      "attributes": {
        "url": "https://yourapp.com/webhooks/gokeys",
        "events": ["license.validation.succeeded", "machine.heartbeat.ping"]
      }
    }
  }'
```

## Health and Monitoring

### Health Check Endpoints
```bash
# Overall system health
curl https://api.gokeys.com/v1/health

# Database health
curl https://api.gokeys.com/v1/health/database

# Cache health  
curl https://api.gokeys.com/v1/health/cache
```

### Metrics
System metrics are available at `/metrics` endpoint in Prometheus format.

## Ruby Keygen Compatibility

GoKeys maintains compatibility with Ruby Keygen API for easy migration:

### Compatible Endpoints
- License validation endpoints
- Machine fingerprinting
- Webhook event formats
- Authentication schemes

### Migration Guide
1. Update base URL from Keygen to GoKeys
2. Update authentication headers if needed
3. Test license validation flows
4. Update webhook endpoints
5. Verify machine tracking functionality

## Support and Resources

- **Documentation**: [docs.gokeys.com](https://docs.gokeys.com)
- **API Status**: [status.gokeys.com](https://status.gokeys.com)  
- **Support**: [<EMAIL>](mailto:<EMAIL>)
- **GitHub**: [github.com/gokeys/gokeys](https://github.com/gokeys/gokeys)
- **Community**: [community.gokeys.com](https://community.gokeys.com)

## Changelog

### v1.0.0 (Current)
- Initial release with full API coverage
- Ruby Keygen compatibility layer
- Enterprise license management features
- Machine tracking and heartbeats
- Webhook integration
- Multi-tenant support

---

For the complete API specification, see the [OpenAPI documentation](openapi.yaml).