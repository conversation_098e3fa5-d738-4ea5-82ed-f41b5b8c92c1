# GoKeys License Management System - Database Schema Documentation

## Tổng quan hệ thống

GoKeys là một hệ thống quản lý license phần mềm với khả năng hỗ trợ mã hóa mạnh mẽ và tích hợp với Open Policy Agent (OPA). Hệ thống được thiết kế để phục vụ các vendor phần mềm, reseller và enterprise customer với kiến trúc linh hoạt cho phép người dùng độc lập hoặc thuộc về nhiều tổ chức.

## S<PERSON> đồ kiến trúc cơ sở dữ liệu

```mermaid
erDiagram
    ORGANIZATIONS ||--o{ PRODUCTS : "có nhiều"
    ORGANIZATIONS ||--o{ USERS : "có nhiều"
    USERS }|--o{ USER_ORGANIZATIONS : "có thể thuộc nhiề<PERSON>"
    ORGANIZATIONS }|--o{ USER_ORGANIZATIONS : "có nhiều thành viên"
    PRODUCTS ||--o{ POLICIES : "có nhiều"
    POLICIES ||--o{ LICENSES : "điều chỉnh"
    LICENSES ||--o{ MACHINES : "quản lý"
    LICENSES }|--|| USERS : "thuộc về"
    LICENSES }|--|| ORGANIZATIONS : "thuộc về"
    USERS ||--o{ SESSIONS : "có nhiều"
    USERS ||--o{ API_TOKENS : "có nhiều"
    
    ORGANIZATIONS {
        uuid id PK
        string name
        string slug
        string email
        string status
    }
    
    USERS {
        uuid id PK
        string email
        string first_name
        string last_name
        string status
    }
    
    USER_ORGANIZATIONS {
        uuid id PK
        uuid user_id FK
        uuid organization_id FK
        string role
    }
    
    PRODUCTS {
        uuid id PK
        uuid organization_id FK
        string name
        string code
    }
    
    POLICIES {
        uuid id PK
        uuid product_id FK
        string name
        boolean strict
    }
    
    LICENSES {
        uuid id PK
        uuid policy_id FK
        uuid owner_id
        string owner_type
        string status
    }
    
    MACHINES {
        uuid id PK
        uuid license_id FK
        string fingerprint
        string status
    }
    
    SESSIONS {
        uuid id PK
        uuid user_id FK
        string token_hash
    }
    
    API_TOKENS {
        uuid id PK
        uuid user_id FK
        string[] permissions
    }
```

## Nguyên tắc thiết kế

| Nguyên tắc | Mô tả |
|------------|-------|
| **Flexibility First** | Người dùng có thể tồn tại độc lập hoặc thuộc nhiều tổ chức |
| **Cryptographic Ready** | Hỗ trợ đầy đủ các thuật toán mã hóa hiện đại |
| **OPA Compatible** | Thiết kế để tích hợp với Open Policy Agent |
| **Performance Optimized** | Cached counters và indexing cho hiệu suất cao |

---

## 1. ORGANIZATIONS - Quản lý tổ chức

**Mục đích**: Lưu trữ thông tin các vendor, reseller và enterprise customer.

### Thông tin cơ bản

| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| `id` | UUID | PRIMARY KEY | UUID tự động generate |
| `name` | VARCHAR(255) | NOT NULL | Tên hiển thị của tổ chức |
| `slug` | VARCHAR(255) | UNIQUE, NOT NULL | URL-friendly identifier |
| `email` | VARCHAR(255) | UNIQUE, NOT NULL | Email liên hệ chính |

### Quản lý trạng thái

| Field | Type | Default | Possible Values | Mô tả |
|-------|------|---------|----------------|-------|
| `status` | VARCHAR(50) | 'active' | active, suspended, canceled | Trạng thái tổ chức |
| `protected` | BOOLEAN | FALSE | true, false | Bảo vệ khỏi xóa/thay đổi |

### Hạ tầng mã hóa (FULL SUPPORT)

| Field | Type | Mô tả |
|-------|------|-------|
| `public_key` | TEXT | RSA public key cho encryption/verification |
| `private_key` | TEXT | RSA private key (được mã hóa khi lưu trữ) |
| `secret_key` | TEXT | 128-character hex secret cho symmetric encryption |
| `ed25519_private_key` | TEXT | Ed25519 private key (encrypted at rest) |
| `ed25519_public_key` | TEXT | Ed25519 public key cho digital signature |

### Giới hạn tài nguyên

| Field | Type | Mô tả |
|-------|------|-------|
| `max_users` | INTEGER | Giới hạn số lượng user tối đa |
| `max_licenses` | INTEGER | Giới hạn số lượng license tối đa |
| `max_machines` | INTEGER | Giới hạn số lượng máy tối đa |

### Cấu hình và metadata

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `settings` | JSONB | '{}' | Cấu hình tùy chỉnh |
| `metadata` | JSONB | '{}' | Metadata bổ sung |

---

## 2. USERS - Quản lý người dùng độc lập

**Mục đích**: Lưu trữ thông tin người dùng với khả năng hoạt động độc lập (không bắt buộc thuộc tổ chức).

### Thông tin xác thực cơ bản

| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| `id` | UUID | PRIMARY KEY | UUID tự động generate |
| `email` | VARCHAR(255) | UNIQUE, NOT NULL | Email đăng nhập |
| `password` | VARCHAR(255) | NOT NULL | Mật khẩu đã hash |
| `first_name` | VARCHAR(255) | | Tên |
| `last_name` | VARCHAR(255) | | Họ |

### Trạng thái và bảo mật

| Field | Type | Default | Possible Values | Mô tả |
|-------|------|---------|----------------|-------|
| `status` | VARCHAR(50) | 'active' | active, inactive, suspended | Trạng thái user |
| `totp_secret` | TEXT | | | Secret key cho 2FA TOTP |
| `totp_enabled` | BOOLEAN | FALSE | | Bật/tắt 2FA |
| `banned_at` | TIMESTAMP WITH TIME ZONE | | | Timestamp khi user bị ban |

### Khôi phục mật khẩu

| Field | Type | Mô tả |
|-------|------|-------|
| `password_reset_token` | VARCHAR(255) | Token để reset password |
| `password_reset_expires_at` | TIMESTAMP WITH TIME ZONE | Thời hạn của reset token |

### Theo dõi hoạt động

| Field | Type | Mô tả |
|-------|------|-------|
| `last_login` | TIMESTAMP WITH TIME ZONE | Timestamp đăng nhập cuối cùng |

---

## 3. USER_ORGANIZATIONS - Quan hệ User-Organization

**Mục đích**: Thiết lập mối quan hệ many-to-many giữa users và organizations với role-based access control.

### Định nghĩa quan hệ

| Field | Type | Constraints | Possible Values | Mô tả |
|-------|------|-------------|----------------|-------|
| `user_id` | UUID | FK → users.id | | Reference đến user |
| `organization_id` | UUID | FK → organizations.id | | Reference đến organization |
| `role` | VARCHAR(50) | NOT NULL | admin, sales_agent, member, viewer | Vai trò trong tổ chức |

### Phân quyền theo resource (OPA Integration)

| Field | Type | Mô tả | Ví dụ |
|-------|------|-------|-------|
| `resource_type` | VARCHAR(50) | Loại resource được phép truy cập | 'product', null = all |
| `resource_id` | UUID | ID cụ thể của resource | product_uuid, null = all |

### Kiểm soát truy cập

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `active` | BOOLEAN | TRUE | Trạng thái active của relationship |
| `expires_at` | TIMESTAMP WITH TIME ZONE | | Thời hạn của quyền truy cập |
| `granted_by` | UUID | FK → users.id | User nào đã cấp quyền này |

### Ràng buộc duy nhất
```sql
UNIQUE (user_id, organization_id, resource_type, resource_id)
```

---

## 4. PRODUCTS - Quản lý sản phẩm phần mềm

**Mục đích**: Lưu trữ thông tin các sản phẩm phần mềm của vendor.

### Nhận dạng sản phẩm

| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| `id` | UUID | PRIMARY KEY | UUID tự động generate |
| `organization_id` | UUID | FK → organizations.id | Vendor sở hữu product |
| `name` | VARCHAR(255) | NOT NULL | Tên hiển thị của product |
| `code` | VARCHAR(255) | NOT NULL | Mã code nội bộ |
| `key` | VARCHAR(255) | UNIQUE, NOT NULL | Legacy identifier, unique global |
| `description` | TEXT | | Mô tả sản phẩm |
| `url` | VARCHAR(512) | | URL sản phẩm |

### Platform và phân phối

| Field | Type | Default | Mô tả | Ví dụ |
|-------|------|---------|-------|-------|
| `platforms` | JSONB | '{"supported": [], "metadata": {}}' | Thông tin platforms được hỗ trợ | `{"supported": ["windows", "linux"], "metadata": {"min_version": "10.0"}}` |
| `distribution_strategy` | VARCHAR(255) | | Chiến lược phân phối | |

### Ràng buộc duy nhất
```sql
UNIQUE (organization_id, code) -- Code unique trong organization
```

---

## 5. POLICIES - Cấu hình hành vi License (FULL FEATURED)

**Mục đích**: Định nghĩa chi tiết cách thức hoạt động của license với đầy đủ tính năng.

### Cấu hình cơ bản

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `id` | UUID | PRIMARY KEY | UUID tự động generate |
| `organization_id` | UUID | FK → organizations.id | Tổ chức sở hữu |
| `product_id` | UUID | FK → products.id | Sản phẩm áp dụng |
| `name` | VARCHAR(255) | NOT NULL | Tên policy |
| `description` | TEXT | | Mô tả chi tiết |
| `strict` | BOOLEAN | FALSE | Chế độ strict enforcement |
| `protected` | BOOLEAN | FALSE | Bảo vệ khỏi modification |
| `duration` | INTEGER | | Thời hạn license (giây) |

### Hành vi License

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `floating` | BOOLEAN | FALSE | License có thể chuyển đổi giữa các máy |
| `use_pool` | BOOLEAN | FALSE | Sử dụng license pooling |
| `encrypted` | BOOLEAN | FALSE | License được mã hóa |
| `concurrent` | BOOLEAN | TRUE | Cho phép sử dụng đồng thời |

### Sơ đồ mã hóa

| Field | Type | Default | Possible Values | Mô tả |
|-------|------|---------|----------------|-------|
| `scheme` | VARCHAR(50) | 'ED25519_SIGN' | LEGACY_ENCRYPT, RSA_2048_PKCS1_ENCRYPT, RSA_2048_PKCS1_SIGN, RSA_2048_PKCS1_PSS_SIGN, RSA_2048_JWT_RS256, ED25519_SIGN, ED25519_JWT_ES256 | Thuật toán mã hóa sử dụng |

### Giới hạn tài nguyên

| Field | Type | Mô tả |
|-------|------|-------|
| `max_machines` | INTEGER | Số máy tối đa |
| `max_uses` | INTEGER | Số lần sử dụng tối đa |
| `max_cores` | INTEGER | Số cores CPU tối đa |
| `max_users` | INTEGER | Số users tối đa |
| `max_processes` | INTEGER | Số processes tối đa |
| `max_activations` | INTEGER | Số lần activate tối đa |
| `max_deactivations` | INTEGER | Số lần deactivate tối đa |

### Cấu hình Heartbeat

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `require_heartbeat` | BOOLEAN | FALSE | Bắt buộc heartbeat check |
| `heartbeat_duration` | INTEGER | | Interval heartbeat (giây) |
| `heartbeat_basis` | VARCHAR(255) | | Cơ sở tính toán heartbeat |
| `heartbeat_cull_strategy` | VARCHAR(255) | | Chiến lược xử lý khi mất heartbeat |
| `heartbeat_resurrection_strategy` | VARCHAR(255) | | Chiến lược phục hồi |

### Cấu hình Check-in

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `require_check_in` | BOOLEAN | FALSE | Bắt buộc check-in định kỳ |
| `check_in_interval` | VARCHAR(255) | | Khoảng thời gian check-in |
| `check_in_interval_count` | INTEGER | | Số lần check-in |

### Chiến lược nâng cao

| Field | Type | Mô tả |
|-------|------|-------|
| `fingerprint_uniqueness_strategy` | VARCHAR(255) | Cách xác định tính duy nhất của fingerprint |
| `fingerprint_matching_strategy` | VARCHAR(255) | Cách match fingerprint |
| `machine_uniqueness_strategy` | VARCHAR(255) | Cách xác định tính duy nhất của machine |
| `machine_matching_strategy` | VARCHAR(255) | Cách match machine |
| `machine_leasing_strategy` | VARCHAR(255) | Chiến lược lease machine |
| `component_uniqueness_strategy` | VARCHAR(255) | Cách xác định tính duy nhất của component |
| `component_matching_strategy` | VARCHAR(255) | Cách match component |
| `components_strategy` | VARCHAR(255) | Chiến lược xử lý components |
| `components_fingerprint` | VARCHAR(255) | Cách tạo component fingerprint |
| `process_leasing_strategy` | VARCHAR(255) | Chiến lược lease process |
| `expiration_strategy` | VARCHAR(255) | Chiến lược xử lý khi hết hạn |
| `expiration_basis` | VARCHAR(255) | Cơ sở tính toán expiration |
| `renewal_basis` | VARCHAR(255) | Cơ sở tính toán renewal |
| `authentication_strategy` | VARCHAR(255) | Chiến lược xác thực |
| `transfer_strategy` | VARCHAR(255) | Chiến lược chuyển đổi license |
| `leasing_strategy` | VARCHAR(255) | Chiến lược leasing tổng quát |
| `overage_strategy` | VARCHAR(255) | Xử lý khi vượt quá giới hạn |

### Yêu cầu Scope

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `require_product_scope` | BOOLEAN | FALSE | Bắt buộc thông tin product |
| `require_policy_scope` | BOOLEAN | FALSE | Bắt buộc thông tin policy |
| `require_machine_scope` | BOOLEAN | FALSE | Bắt buộc thông tin machine |
| `require_fingerprint_scope` | BOOLEAN | FALSE | Bắt buộc fingerprint |
| `require_user_scope` | BOOLEAN | FALSE | Bắt buộc thông tin user |
| `require_environment_scope` | BOOLEAN | FALSE | Bắt buộc thông tin môi trường |
| `require_checksum_scope` | BOOLEAN | FALSE | Bắt buộc checksum |
| `require_version_scope` | BOOLEAN | FALSE | Bắt buộc version |
| `require_components_scope` | BOOLEAN | FALSE | Bắt buộc thông tin components |

---

## 6. LICENSES - Tracking License (FULL FEATURED)

**Mục đích**: Theo dõi chi tiết từng license với đầy đủ tính năng và performance optimization.

### Nhận dạng License

| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| `id` | UUID | PRIMARY KEY | UUID tự động generate |
| `organization_id` | UUID | FK → organizations.id | Tổ chức sở hữu |
| `product_id` | UUID | FK → products.id | Sản phẩm áp dụng |
| `policy_id` | UUID | FK → policies.id | Policy áp dụng |
| `key` | VARCHAR(255) | NOT NULL | License key, unique trong organization |
| `name` | VARCHAR(255) | | Tên mô tả license |

### Chủ sở hữu Polymorphic

| Field | Type | Possible Values | Mô tả |
|-------|------|----------------|-------|
| `owner_type` | VARCHAR(50) | user, organization | Loại chủ sở hữu |
| `owner_id` | UUID | | ID của chủ sở hữu tương ứng |

### Trạng thái License

| Field | Type | Default | Possible Values | Mô tả |
|-------|------|---------|----------------|-------|
| `status` | VARCHAR(50) | 'active' | active, expired, suspended, banned | Trạng thái license |
| `suspended` | BOOLEAN | FALSE | | Trạng thái tạm ngưng |
| `protected` | BOOLEAN | FALSE | | Bảo vệ khỏi modification |

### Theo dõi sử dụng

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `uses` | INTEGER | 0 | Số lần đã sử dụng |
| `expires_at` | TIMESTAMP WITH TIME ZONE | | Thời điểm hết hạn |
| `last_used` | TIMESTAMP WITH TIME ZONE | | Lần sử dụng cuối cùng |

### Policy Overrides

| Field | Type | Mô tả |
|-------|------|-------|
| `max_uses_override` | INTEGER | Override max uses từ policy |
| `max_machines_override` | INTEGER | Override max machines từ policy |
| `max_cores_override` | INTEGER | Override max cores từ policy |
| `max_users_override` | INTEGER | Override max users từ policy |
| `max_processes_override` | INTEGER | Override max processes từ policy |

### Performance Cached Counters

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `machines_count` | INTEGER | 0 | Số máy hiện tại (cached) |
| `machines_core_count` | INTEGER | 0 | Tổng số cores (cached) |
| `license_users_count` | INTEGER | 0 | Số users (cached) |

### Validation & Check-in Tracking

| Field | Type | Mô tả |
|-------|------|-------|
| `last_check_in_at` | TIMESTAMP WITH TIME ZONE | Lần check-in cuối |
| `last_validated_at` | TIMESTAMP WITH TIME ZONE | Lần validate cuối |
| `last_validated_checksum` | VARCHAR(255) | Checksum của lần validate cuối |
| `last_validated_version` | VARCHAR(255) | Version của lần validate cuối |
| `last_check_out_at` | TIMESTAMP WITH TIME ZONE | Lần check-out cuối |

### Event Notification Tracking

| Field | Type | Mô tả |
|-------|------|-------|
| `last_expiration_event_sent_at` | TIMESTAMP WITH TIME ZONE | Event hết hạn cuối cùng |
| `last_check_in_event_sent_at` | TIMESTAMP WITH TIME ZONE | Event check-in cuối cùng |
| `last_expiring_soon_event_sent_at` | TIMESTAMP WITH TIME ZONE | Event sắp hết hạn cuối cùng |
| `last_check_in_soon_event_sent_at` | TIMESTAMP WITH TIME ZONE | Event sắp cần check-in cuối cùng |

### Ràng buộc duy nhất
```sql
UNIQUE (organization_id, key) -- Key unique trong organization
```

---

## 7. MACHINES - Tracking máy tính (FULL FEATURED)

**Mục đích**: Theo dõi chi tiết các máy sử dụng license với heartbeat và component tracking.

### Nhận dạng Machine

| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| `id` | UUID | PRIMARY KEY | UUID tự động generate |
| `license_id` | UUID | FK → licenses.id | License đang sử dụng |
| `policy_id` | UUID | FK → policies.id | Policy áp dụng |
| `owner_id` | UUID | FK → users.id | User sở hữu máy này |
| `fingerprint` | VARCHAR(255) | NOT NULL | Unique fingerprint của máy |
| `name` | VARCHAR(255) | | Tên máy do user đặt |
| `hostname` | VARCHAR(255) | | System hostname |

### Thông tin hệ thống

| Field | Type | Mô tả |
|-------|------|-------|
| `platform` | VARCHAR(255) | Hệ điều hành |
| `ip` | VARCHAR(45) | Địa chỉ IP (IPv4/IPv6) |
| `cores` | INTEGER | Số cores CPU |

### Trạng thái Machine

| Field | Type | Default | Possible Values | Mô tả |
|-------|------|---------|----------------|-------|
| `status` | VARCHAR(50) | 'active' | active, inactive | Trạng thái machine |
| `activated_at` | TIMESTAMP WITH TIME ZONE | | | Thời điểm activate |
| `deactivated_at` | TIMESTAMP WITH TIME ZONE | | | Thời điểm deactivate |
| `last_seen` | TIMESTAMP WITH TIME ZONE | | | Lần liên lạc cuối |

### Component Fingerprinting

| Field | Type | Default | Mô tả | Ví dụ |
|-------|------|---------|-------|-------|
| `components` | JSONB | '{}' | Thông tin chi tiết phần cứng | `{"cpu": "Intel i7-9700K", "ram": "16GB", "disk_serial": "ABC123", "mac_addresses": ["00:11:22:33:44:55"]}` |

### Heartbeat System

| Field | Type | Mô tả |
|-------|------|-------|
| `last_heartbeat_at` | TIMESTAMP WITH TIME ZONE | Heartbeat cuối cùng |
| `next_heartbeat_at` | TIMESTAMP WITH TIME ZONE | Heartbeat tiếp theo dự kiến |
| `last_death_event_sent_at` | TIMESTAMP WITH TIME ZONE | Event "máy chết" cuối cùng |
| `heartbeat_jid` | VARCHAR(255) | Job ID cho background processing |

### Floating License Support

| Field | Type | Mô tả |
|-------|------|-------|
| `last_check_out_at` | TIMESTAMP WITH TIME ZONE | Lần check-out cuối (cho floating license) |

### Ràng buộc duy nhất
```sql
UNIQUE (license_id, fingerprint) -- Một fingerprint duy nhất per license
```

---

## 8. SESSIONS - Quản lý phiên đăng nhập

**Mục đích**: Theo dõi các session đăng nhập của user.

| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| `id` | UUID | PRIMARY KEY | UUID tự động generate |
| `user_id` | UUID | FK → users.id | User sở hữu session |
| `token_hash` | VARCHAR(255) | UNIQUE, NOT NULL | Hash của session token (bảo mật) |
| `ip` | VARCHAR(45) | | Địa chỉ IP đăng nhập |
| `user_agent` | TEXT | | Browser/client information |
| `last_used_at` | TIMESTAMP WITH TIME ZONE | | Lần sử dụng cuối |
| `expires_at` | TIMESTAMP WITH TIME ZONE | NOT NULL | Thời điểm hết hạn session |

---

## 9. API_TOKENS - Quản lý API access tokens

**Mục đích**: Cung cấp API access với permission-based authorization.

### Token Ownership

| Field | Type | Constraints | Mô tả |
|-------|------|-------------|-------|
| `id` | UUID | PRIMARY KEY | UUID tự động generate |
| `user_id` | UUID | FK → users.id | User sở hữu token (optional) |
| `organization_id` | UUID | FK → organizations.id | Organization sở hữu token (optional) |
| `name` | VARCHAR(255) | NOT NULL | Tên mô tả token |

### Security & Permissions

| Field | Type | Constraints | Mô tả | Ví dụ |
|-------|------|-------------|-------|-------|
| `token_hash` | VARCHAR(255) | UNIQUE, NOT NULL | Hash của token (bảo mật) | |
| `permissions` | TEXT[] | | Array các permissions cho OPA | `['licenses:read', 'machines:write', 'products:admin']` |

### Token Management

| Field | Type | Default | Mô tả |
|-------|------|---------|-------|
| `active` | BOOLEAN | TRUE | Trạng thái active |
| `expires_at` | TIMESTAMP WITH TIME ZONE | | Thời điểm hết hạn |
| `last_used_at` | TIMESTAMP WITH TIME ZONE | | Lần sử dụng cuối |

---

## Relationships và Dependencies

### Core Entity Flow
```
Organizations → Products → Policies → Licenses → Machines
     ↓
   Users ←→ User_Organizations (many-to-many)
     ↓
  Sessions, API_Tokens
```

### Key Relationships

| Relationship | Type | Mô tả |
|--------------|------|-------|
| Organizations → Products | 1:N | Một organization có nhiều products |
| Products → Policies | 1:N | Một product có nhiều policies |
| Policies → Licenses | 1:N | Một policy định nghĩa cho nhiều licenses |
| Licenses → Machines | 1:N | Một license sử dụng trên nhiều machines |
| Users ↔ Organizations | M:N | Users có thể thuộc nhiều organizations với role khác nhau |
| Licenses → Owner | Polymorphic | License có thể thuộc về User hoặc Organization |

---

## Performance Optimizations

| Feature | Mô tả | Benefit |
|---------|-------|---------|
| Cached Counters | `machines_count`, `license_users_count` trong licenses table | Tránh COUNT queries phức tạp |
| Indexed Relationships | Foreign keys và unique constraints | Fast lookups và joins |
| JSONB Storage | Metadata, settings, components | Flexible schema với indexing support |
| UUID Primary Keys | Tất cả tables sử dụng UUID | Distributed systems friendly |

---

## Security Features

| Feature | Implementation | Mô tả |
|---------|----------------|-------|
| Password Security | Bcrypt hashing | Passwords được hash an toàn |
| Token Security | SHA-256 hashing | Session và API tokens được hash |
| 2FA Support | TOTP implementation | Two-factor authentication |
| Key Encryption | Encrypted private keys | Private keys encrypted at rest |
| API Permissions | Permission arrays | Fine-grained API access control |
| Soft Deletes | `deleted_at` fields | Data recovery và audit trail |

---

## Extension Points

### JSONB Fields cho flexibility

| Field | Purpose | Example Usage |
|-------|---------|---------------|
| `organizations.settings` | Cấu hình tùy chỉnh | Theme, notification preferences |
| `organizations.metadata` | Metadata mở rộng | Integration configs, custom fields |
| `products.platforms` | Platform information | Supported OS, minimum requirements |
| `machines.components` | Hardware fingerprinting | CPU, RAM, disk info cho security |
| `licenses.metadata` | License-specific data | Custom attributes, integration data |

### OPA Integration Ready

| Feature | Implementation | Use Case |
|---------|----------------|----------|
| Resource-level permissions | `user_organizations.resource_type/resource_id` | User chỉ có quyền trên specific products |
| API permissions | `api_tokens.permissions` | API key với specific scopes |
| Role-based access | `user_organizations.role` | Admin, sales_agent, member, viewer roles |

Hệ thống này cung cấp một nền tảng mạnh mẽ và linh hoạt cho việc quản lý license phần mềm với khả năng mở rộng cao và bảo mật tốt.