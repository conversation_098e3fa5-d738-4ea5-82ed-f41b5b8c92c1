# Authentication & Authorization System

This document describes the comprehensive authentication and authorization system implemented in GoKeys, which is compatible with the Ruby Keygen API.

## Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Authentication Methods](#authentication-methods)
- [Permission System](#permission-system)
- [Role System](#role-system)
- [Token Management](#token-management)
- [Middleware Usage](#middleware-usage)
- [API Examples](#api-examples)
- [Security Considerations](#security-considerations)

## Overview

The GoKeys authentication and authorization system provides:

- **Multiple Authentication Methods**: Bearer tokens, Basic auth, License keys, Query parameters
- **OpenPolicyAgent Integration**: Dynamic policy evaluation with OPA support
- **Fine-grained Permissions**: 216+ specific permissions for different operations
- **Role-based Access Control**: User, Admin, Environment, Product, License roles
- **Token Security**: HMAC-based token generation and verification
- **Ruby API Compatibility**: Full compatibility with existing Keygen API clients

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   HTTP Request  │───▶│  Authentication  │───▶│  Authorization  │
│                 │    │   Middleware     │    │   Middleware    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │ Token Validation│    │ OPA Policy      │
                       │ & Context Setup │    │ Evaluation      │
                       └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │ Permission Check│
                                               │ & Role Validation│
                                               └─────────────────┘
```

### Core Components

1. **OPA Service** (`internal/domain/services/policy/opa_service.go`)
2. **OPA Manager** (`internal/domain/services/policy/opa_manager.go`)
3. **Token Entity** (`internal/domain/entities/token.go`)
4. **Authentication Middleware** (`internal/adapters/http/middleware/authentication.go`)
5. **Authorization Middleware** (`internal/adapters/http/middleware/authorization.go`)
6. **Rego Policies** (`policies/auth/main.rego`)

## Authentication Methods

### 1. Bearer Token Authentication

**Header Format:**
```http
Authorization: Bearer <token>
```

**Example:**
```bash
curl -H "Authorization: Bearer user-abc123def456...v3" \
     https://api.example.com/licenses
```

### 2. Basic Authentication

**Token-based Basic Auth:**
```http
Authorization: Basic <base64(token:)>
```

**License Key Basic Auth:**
```http
Authorization: Basic <base64(license:<key>)>
```

**Examples:**
```bash
# Token-based
curl -u "token:user-abc123def456...v3" \
     https://api.example.com/licenses

# License key-based
curl -u "license:LIC-12345-ABCDE-67890" \
     https://api.example.com/licenses
```

### 3. License Header Authentication

**Header Format:**
```http
Authorization: License <license_key>
```

**Example:**
```bash
curl -H "Authorization: License LIC-12345-ABCDE-67890" \
     https://api.example.com/licenses
```

### 4. Query Parameter Authentication

**Token Parameter:**
```
?token=<token>
```

**Auth Parameter with Type:**
```
?auth=<type>:<value>
```

**Examples:**
```bash
# Direct token
curl "https://api.example.com/licenses?token=user-abc123def456...v3"

# Typed authentication
curl "https://api.example.com/licenses?auth=license:LIC-12345-ABCDE"
```

## Permission System

### Permission Categories

The system includes 216+ granular permissions organized by resource, managed through OpenPolicyAgent (OPA):

#### Account Permissions
- `account.read`, `account.update`
- `account.billing.read`, `account.billing.update`
- `account.plan.read`, `account.plan.update`

#### License Permissions
- `license.read`, `license.create`, `license.update`, `license.delete`
- `license.validate`, `license.check-in`, `license.check-out`
- `license.suspend`, `license.reinstate`, `license.revoke`
- `license.usage.increment`, `license.usage.decrement`, `license.usage.reset`

#### Machine Permissions
- `machine.read`, `machine.create`, `machine.update`, `machine.delete`
- `machine.heartbeat.ping`, `machine.heartbeat.reset`
- `machine.check-out`, `machine.proofs.generate`

#### User Permissions
- `user.read`, `user.create`, `user.update`, `user.delete`
- `user.invite`, `user.ban`, `user.unban`
- `user.password.reset`, `user.password.update`

#### Product & Policy Permissions
- `product.read`, `product.create`, `product.update`, `product.delete`
- `policy.read`, `policy.create`, `policy.update`, `policy.delete`

#### Token Permissions
- `token.read`, `token.generate`, `token.regenerate`, `token.revoke`

### Permission Sets

Different token types have different default permission sets:

#### Environment Permissions
Environment tokens have broad access for automation:
```go
var EnvironmentPermissions = []string{
    "account.read",
    "license.create", "license.read", "license.update", "license.validate",
    "machine.create", "machine.read", "machine.update", "machine.heartbeat.ping",
    "user.create", "user.read", "user.update",
    // ... and many more
}
```

#### User Permissions
User tokens have limited access for end-user operations:
```go
var UserPermissions = []string{
    "account.read",
    "license.read", "license.validate", "license.check-out",
    "machine.read", "machine.create", "machine.heartbeat.ping",
    "user.read", "user.update", "user.password.update",
    // ... focused on user operations
}
```

#### License Permissions
License tokens have minimal access for activation scenarios:
```go
var LicensePermissions = []string{
    "account.read",
    "license.read", "license.validate", "license.check-out",
    "machine.create", "machine.read", "machine.heartbeat.ping",
    "product.read", "policy.read",
    // ... minimal set for license operations
}
```

### Wildcard Permission

The special `*` permission grants access to all operations:
```go
const WildcardPermission = "*"
```

Admin and Developer tokens typically have wildcard permission.

## Role System

### Bearer Types

The system uses bearer types to categorize tokens:

```go
type TokenBearerType string

const (
    TokenBearerTypeUser        TokenBearerType = "user"
    TokenBearerTypeProduct     TokenBearerType = "product"
    TokenBearerTypeLicense     TokenBearerType = "license"
    TokenBearerTypeEnvironment TokenBearerType = "environment"
)
```

### Token Prefixes

Each bearer type has a unique prefix for easy identification:

- **User tokens**: `user-abc123...v3`
- **Environment tokens**: `env-abc123...v3`
- **Product tokens**: `prod-abc123...v3`
- **License tokens**: `activ-abc123...v3`

## Token Management

### Token Structure

```go
type Token struct {
    ID            string          `json:"id"`
    AccountID     string          `json:"account_id"`
    BearerID      string          `json:"bearer_id"`
    BearerType    TokenBearerType `json:"bearer_type"`
    
    // Security
    Digest        string          `json:"-"`        // HMAC digest
    Token         string          `json:"token,omitempty"` // Raw token (memory only)
    
    // Permissions
    CustomPermissions []string    `json:"custom_permissions"`
    
    // Lifecycle
    ExpiresAt     *time.Time      `json:"expires_at,omitempty"`
    RevokedAt     *time.Time      `json:"revoked_at,omitempty"`
    LastUsed      *time.Time      `json:"last_used,omitempty"`
}
```

### Token Generation

Tokens are generated using HMAC-SHA256:

```go
func (t *Token) GenerateToken(secretKey string) error {
    // Generate 32 random bytes
    tokenBytes := make([]byte, 32)
    rand.Read(tokenBytes)
    
    // Create token with version
    rawToken := hex.EncodeToString(tokenBytes) + "v3"
    
    // Add prefix based on bearer type
    prefix := t.GetPrefix()
    if prefix != "" {
        rawToken = prefix + "-" + rawToken
    }
    
    // Create HMAC digest
    h := hmac.New(sha256.New, []byte(secretKey))
    h.Write([]byte(rawToken))
    digest := hex.EncodeToString(h.Sum(nil))
    
    t.Token = rawToken
    t.Digest = digest
    return nil
}
```

### Token Verification

```go
func (t *Token) VerifyToken(rawToken, secretKey string) bool {
    h := hmac.New(sha256.New, []byte(secretKey))
    h.Write([]byte(rawToken))
    expectedDigest := hex.EncodeToString(h.Sum(nil))
    
    // Constant-time comparison
    return hmac.Equal([]byte(t.Digest), []byte(expectedDigest))
}
```

## Middleware Usage

### Setup

```go
package main

import (
    "github.com/gin-gonic/gin"
    "github.com/gokeys/gokeys/internal/adapters/http/middleware"
    "github.com/gokeys/gokeys/internal/domain/services"
)

func main() {
    router := gin.Default()

    // Initialize service coordinator
    serviceCoordinator := services.NewServiceCoordinator(db)

    // Setup authentication middleware
    authMiddleware := middleware.NewAuthenticationMiddleware(
        serviceCoordinator,
        "your-secret-key",
    )

    // Setup authorization middleware with OPA support
    authzMiddleware := middleware.NewAuthorizationMiddleware(serviceCoordinator)

    // Apply middlewares
    setupRoutes(router, authMiddleware, authzMiddleware)
}
```

### Route Protection

#### Basic Authentication Required
```go
// Require any valid authentication
router.Use(authMiddleware.RequireAuthentication())
```

#### Permission-based Protection
```go
// Require specific permissions using OPA
router.GET("/licenses",
    authMiddleware.RequireAuthentication(),
    authzMiddleware.CreatePermissionChecker("license.read"),
    handlers.ListLicenses,
)

// Complex authorization with resource ownership
router.GET("/licenses/:id",
    authMiddleware.RequireAuthentication(),
    authzMiddleware.RequireOwnershipOrPermission("license", "id", "license.read"),
    handlers.GetLicense,
)
```

#### Role-based Protection
```go
// Require admin role (wildcard permission)
router.DELETE("/users/:id",
    authMiddleware.RequireAuthentication(),
    authzMiddleware.CreatePermissionChecker("*"),
    handlers.DeleteUser,
)

// Require organization access
router.GET("/organization",
    authMiddleware.RequireAuthentication(),
    authzMiddleware.RequireOrganizationAccess(),
    handlers.GetCurrentOrganization,
)
```

#### Resource-specific Protection
```go
// License operations with OPA-based authorization
licenses := protected.Group("/licenses")
{
    licenses.GET("",
        authzMiddleware.CreatePermissionChecker("license.read"),
        handlers.ListLicenses)
    licenses.POST("",
        authzMiddleware.CreatePermissionChecker("license.create"),
        handlers.CreateLicense)
    licenses.GET("/:id",
        authzMiddleware.RequireOwnershipOrPermission("license", "id", "license.read"),
        handlers.GetLicense)
}

// Machine heartbeat operations
router.POST("/machines/:id/actions/ping",
    authMiddleware.RequireAuthentication(),
    authzMiddleware.CreatePermissionChecker("machine.heartbeat.ping"),
    handlers.PingMachine,
)
```

### Optional Authentication

For endpoints that work with both authenticated and unauthenticated requests:

```go
router.GET("/public-info",
    authMiddleware.OptionalAuthentication(),
    handlers.GetPublicInfo,
)
```

### Accessing Authentication Info in Handlers

```go
func MyHandler(c *gin.Context) {
    // Get authenticated token
    token, exists := middleware.GetToken(c)
    if !exists {
        c.JSON(401, gin.H{"error": "authentication_required"})
        return
    }

    // Get account and bearer IDs
    accountID, err := middleware.GetAccountID(c)
    if err != nil {
        c.JSON(500, gin.H{"error": "invalid_account_id"})
        return
    }

    bearerID, err := middleware.GetBearerID(c)
    if err != nil {
        c.JSON(500, gin.H{"error": "invalid_bearer_id"})
        return
    }

    // Check specific permissions
    if !token.HasPermission("license.create") {
        c.JSON(403, gin.H{"error": "insufficient_permissions"})
        return
    }

    // Check bearer type
    if token.BearerType != entities.TokenBearerTypeEnvironment {
        c.JSON(403, gin.H{"error": "environment_token_required"})
        return
    }

    // Check token expiry
    if token.IsExpired() {
        c.JSON(401, gin.H{"error": "token_expired"})
        return
    }

    // Your business logic here
    c.JSON(200, gin.H{"message": "success"})
}
```

## API Examples

### License Validation

```bash
# Using bearer token
curl -X POST \
  -H "Authorization: Bearer env-abc123def456...v3" \
  -H "Content-Type: application/json" \
  -d '{"meta": {"machine": "fp-12345"}}' \
  https://api.example.com/licenses/550e8400-e29b-41d4-a716-************/actions/validate

# Using license key authentication
curl -X POST \
  -H "Authorization: License LIC-12345-ABCDE-67890" \
  -H "Content-Type: application/json" \
  -d '{"meta": {"machine": "fp-12345"}}' \
  https://api.example.com/licenses/actions/validate-key

# Using basic auth with license key
curl -X POST \
  -u "license:LIC-12345-ABCDE-67890" \
  -H "Content-Type: application/json" \
  -d '{"meta": {"machine": "fp-12345"}}' \
  https://api.example.com/licenses/actions/validate-key
```

### Machine Heartbeat

```bash
# Environment token required
curl -X POST \
  -H "Authorization: Bearer env-abc123def456...v3" \
  https://api.example.com/machines/550e8400-e29b-41d4-a716-************/actions/ping

# License token can also ping
curl -X POST \
  -H "Authorization: License LIC-12345-ABCDE-67890" \
  https://api.example.com/machines/550e8400-e29b-41d4-a716-************/actions/ping
```

### Account-scoped Operations

```bash
# List licenses for specific account
curl -H "Authorization: Bearer user-abc123def456...v3" \
  https://api.example.com/accounts/550e8400-e29b-41d4-a716-************/licenses

# Validate license in account scope
curl -X POST \
  -H "Authorization: Bearer env-abc123def456...v3" \
  https://api.example.com/accounts/550e8400-e29b-41d4-a716-************/licenses/550e8400-e29b-41d4-a716-************/actions/validate
```

## Security Considerations

### Token Security

1. **HMAC-based Verification**: All tokens use HMAC-SHA256 for integrity
2. **Constant-time Comparison**: Prevents timing attacks
3. **Secure Random Generation**: Uses crypto/rand for token generation
4. **No Plain Text Storage**: Only HMAC digests are stored in database

### Permission Validation

1. **Principle of Least Privilege**: Each token type has minimal required permissions
2. **Explicit Permission Checks**: No implicit permissions granted
3. **Wildcard Permission Control**: Only admin/developer tokens get wildcard access
4. **Token Expiry Enforcement**: Expired tokens are rejected

### Best Practices

1. **Use Environment Variables**: Store secret keys in environment variables
2. **Rotate Secret Keys**: Implement key rotation for production
3. **Monitor Token Usage**: Track last_used timestamps
4. **Implement Rate Limiting**: Prevent abuse of authentication endpoints
5. **Use HTTPS**: Always use TLS in production
6. **Validate Input**: Sanitize all authentication inputs

### Error Handling

The system provides specific error codes for different authentication failures:

```json
{
  "error": "authentication_required",
  "message": "Authentication required to access this resource"
}

{
  "error": "token_expired",
  "message": "Token has expired"
}

{
  "error": "insufficient_permissions",
  "message": "Insufficient permissions to access this resource",
  "required_permissions": ["license.validate"]
}

{
  "error": "role_not_allowed",
  "message": "Token role is not allowed to access this resource",
  "bearer_type": "license",
  "allowed_roles": ["user", "environment"]
}
```

## Migration from Ruby API

The GoKeys authentication system is fully compatible with existing Ruby Keygen API clients:

1. **Same Token Formats**: Identical token prefixes and structure
2. **Same Authentication Methods**: All Ruby auth methods supported
3. **Same Permission Names**: Exact permission string matches
4. **Same API Routes**: Both direct and account-scoped routes
5. **Same Error Responses**: Compatible error format and codes

Existing clients can switch to GoKeys without any code changes.

---

For more information, see:
- [API Documentation](./api-documentation.md)
- [Swagger Documentation](http://localhost:8080/swagger/index.html)
- [Ruby Keygen API Reference](https://keygen.sh/docs/api/)
```
