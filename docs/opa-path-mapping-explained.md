# OPA Path Mapping & Định Danh - Giải Thích Chi Tiết

## 🤔 Câu Hỏi Gốc
**"Sao nó định danh được cái nào cho cái nào và định danh path kiểu gì?"**

Đ<PERSON>y là câu hỏi cốt lõi để hiểu OPA! Hãy phân tích từng bước.

---

## 1. OPA Package & Path Mapping

### Package Declaration trong Rego
```rego
# File: policies/auth/main.rego
package gokeys.authz

default allow = false
allow if { "*" in input.subject.permissions }
```

### Golang Query tương ứng
```go
// Golang code
result := opa.Evaluate("data.gokeys.authz.allow", input)
//                     ^^^^^^^^^^^^^^^^^^^^^^^^
//                     Path này mapping với package
```

### 🔗 **Mapping Rule:**
```
Package:  gokeys.authz       (trong file .rego)
    ↓
Path:     data.gokeys.authz  (trong Golang query)
    ↓  
Rule:     allow              (rule name trong .rego)
    ↓
Full:     data.gokeys.authz.allow (complete query path)
```

---

## 2. Cách OPA Xây Dựng Namespace

### A. File Structure → Namespace
```
policies/
├── auth/
│   ├── main.rego          # package gokeys.authz
│   └── admin.rego         # package gokeys.admin  
├── license/
│   └── validation.rego    # package gokeys.license
└── data/
    └── permissions.json
```

### B. Package → Data Path
```rego
# File: policies/auth/main.rego
package gokeys.authz
#       ^^^^^^ ^^^^
#       |      |
#       |      └─ Submodule
#       └─ Root namespace

# Tương đương với:
data.gokeys.authz
#    ^^^^^^ ^^^^
#    |      |  
#    |      └─ Submodule path
#    └─ Root data namespace
```

---

## 3. HTTP Request → OPA Input Mapping

### Step 1: HTTP Request Arrives
```http
GET /api/v1/licenses/license-123 HTTP/1.1
Authorization: Bearer user-abc123...
```

### Step 2: Route Definition
```go
// File: internal/adapters/http/routes/api.go
router.GET("/licenses/:id",
    authz.CreatePermissionChecker("license.read"), // <-- Permission string
    handlers.GetLicense)
```

### Step 3: Authorization Middleware 
```go
// File: internal/adapters/http/middleware/authorization.go
func (am *AuthorizationMiddleware) CreatePermissionChecker(requiredPermissions ...string) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 🔍 BUILD OPA INPUT TỪ CONTEXT
        input := am.buildOPAInput(c)
        
        // 🎯 CALL OPA VỚI SPECIFIC QUERY PATH
        allowed, err := am.serviceCoordinator.GetOPAManager().IsAllowed(ctx, input)
    }
}
```

### Step 4: Build OPA Input
```go
func (am *AuthorizationMiddleware) buildOPAInput(c *gin.Context) policy.AuthorizationInput {
    // Lấy thông tin từ JWT token (đã được auth middleware set)
    userID, _ := GetUserID(c)           // "user-123"
    orgID, _ := GetOrganizationID(c)    // "org-456" 
    permissions, _ := GetPermissions(c)  // ["license.read", "machine.create"]
    
    // Lấy thông tin từ URL path
    resourceID := c.Param("id")         // "license-123" từ /licenses/:id
    
    // Determine resource type từ URL path
    resourceType := ""
    if strings.Contains(c.Request.URL.Path, "/licenses") {
        resourceType = "license"
    } else if strings.Contains(c.Request.URL.Path, "/machines") {
        resourceType = "machine"
    }
    
    // Determine action từ HTTP method
    action := ""
    switch c.Request.Method {
    case "GET":    action = "read"
    case "POST":   action = "create"  
    case "PUT":    action = "update"
    case "DELETE": action = "delete"
    }
    
    return policy.AuthorizationInput{
        Subject: policy.Subject{
            ID:             userID,
            OrganizationID: orgID,
            Permissions:    permissions,
        },
        Resource: policy.Resource{
            Type: resourceType,
            ID:   resourceID,
        },
        Action: action,
    }
}
```

---

## 4. Query Path Resolution

### OPA Manager
```go
// File: internal/domain/services/policy/opa_manager.go
func (m *OPAManager) IsAllowed(ctx context.Context, input AuthorizationInput) (bool, error) {
    inputMap := map[string]interface{}{
        "subject":  input.Subject,
        "resource": input.Resource, 
        "action":   input.Action,
    }
    
    // 🎯 FIXED QUERY PATH - luôn gọi rule "allow" trong package "gokeys.authz"
    return m.service.Evaluate(ctx, "data.gokeys.authz.allow", inputMap)
    //                              ^^^^^^^^^^^^^^^^^^^^^^^^
    //                              Hard-coded query path
}
```

### SimpleOPAService
```go
// File: internal/domain/services/policy/opa_service.go
func (s *SimpleOPAService) Evaluate(ctx context.Context, query string, input map[string]interface{}) (bool, error) {
    // query = "data.gokeys.authz.allow"
    // input = {"subject": {...}, "resource": {...}, "action": "read"}
    
    r := rego.New(
        rego.Query(query),      // "data.gokeys.authz.allow" 
        rego.Store(s.store),    // Data store
        rego.Input(input),      // Input object
        rego.Module("main", s.policies["main"]), // Load main.rego policy
    )
    
    rs, err := r.Eval(ctx)
    return rs[0].Expressions[0].Value.(bool), nil
}
```

---

## 5. Policy Loading & Module Mapping

### Policy Loading Process
```go
// File: internal/domain/services/policy/opa_manager.go
func (m *OPAManager) loadPolicies(ctx context.Context) error {
    err := filepath.Walk(m.config.PolicyPath, func(path string, info os.FileInfo, err error) error {
        if filepath.Ext(path) != ".rego" {
            return nil
        }
        
        // 🔍 TẠO MODULE NAME TỪ FILE PATH
        relPath, _ := filepath.Rel(m.config.PolicyPath, path)
        // policies/auth/main.rego -> auth/main.rego
        
        moduleName := filepath.ToSlash(relPath)
        moduleName = moduleName[:len(moduleName)-5] // Remove .rego
        moduleName = filepath.Base(moduleName)      // Get filename only
        // auth/main.rego -> main
        
        // 🎯 LOAD POLICY VỚI MODULE NAME
        return m.service.LoadPolicyFromFile(ctx, moduleName, path)
        //                                       ^^^^^^^^^^
        //                                       Module identifier
    })
}
```

### Module Registration
```go
// File: internal/domain/services/policy/opa_service.go
func (s *SimpleOPAService) LoadPolicy(ctx context.Context, name string, policy string) error {
    // name = "main" (từ filename main.rego)
    // policy = nội dung file .rego
    
    s.policies[name] = policy  // Store policy với key "main"
    s.clearCache()             // Clear cache khi policy thay đổi
    return nil
}
```

### Policy Execution
```go
func (s *SimpleOPAService) EvaluateWithResult(ctx context.Context, query string, input map[string]interface{}) (interface{}, error) {
    regoOptions := []func(*rego.Rego){
        rego.Query(query),     // "data.gokeys.authz.allow"
        rego.Store(s.store),   // Data store
        rego.Input(input),     // Input data
    }
    
    // 🎯 ADD TẤT CẢ POLICIES ĐÃ LOAD
    for name, policy := range s.policies {
        regoOptions = append(regoOptions, rego.Module(name, policy))
        // rego.Module("main", "package gokeys.authz\ndefault allow = false\n...")
    }
    
    r := rego.New(regoOptions...)
    return r.Eval(ctx)
}
```

---

## 6. Ví Dụ Cụ Thể: Complete Flow

### HTTP Request
```http
GET /api/v1/licenses/license-123 HTTP/1.1
Authorization: Bearer user-abc123...
```

### Step 1: Route Matching
```go
// Route definition
router.GET("/licenses/:id", 
    authz.CreatePermissionChecker("license.read"),
    handlers.GetLicense)

// Gin matches: /licenses/license-123
// Sets c.Param("id") = "license-123"
```

### Step 2: Build OPA Input
```go
input := AuthorizationInput{
    Subject: Subject{
        ID: "user-456",
        Permissions: ["license.read", "license.validate"],
    },
    Resource: Resource{
        Type: "license",    // ← Từ URL path "/licenses/*"
        ID: "license-123",  // ← Từ c.Param("id")
    },
    Action: "read",         // ← Từ HTTP method GET
}
```

### Step 3: OPA Query
```go
// Fixed query path
query = "data.gokeys.authz.allow"
//       ^^^^ ^^^^^^ ^^^^ ^^^^
//       |    |      |    |
//       |    |      |    └─ Rule name trong .rego
//       |    |      └─ Submodule từ package declaration
//       |    └─ Root namespace từ package declaration  
//       └─ OPA data prefix (luôn luôn là "data")

input = {
    "subject": {"id": "user-456", "permissions": ["license.read"]},
    "resource": {"type": "license", "id": "license-123"},
    "action": "read"
}
```

### Step 4: Rego Evaluation
```rego
# File: policies/auth/main.rego
package gokeys.authz  # ← Tạo namespace data.gokeys.authz

# Input tự động available as 'input' variable:
# input.subject = {"id": "user-456", "permissions": ["license.read"]}
# input.resource = {"type": "license", "id": "license-123"}  
# input.action = "read"

default allow = false

# Rule evaluation
allow if {
    required_permission in input.subject.permissions
}

# Computed value
required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
    # sprintf("%s.%s", ["license", "read"]) = "license.read"
}

# Final check:
# "license.read" in ["license.read", "license.validate"] => TRUE
# => allow = true
```

---

## 7. Data Store & Permission Loading

### Permission Data Loading
```go
// File: internal/domain/services/policy/opa_manager.go
func (m *OPAManager) loadPermissionData(ctx context.Context) error {
    // Load từ policies/data/permissions.json
    dataFile := filepath.Join(m.config.DataPath, "permissions.json")
    
    data, err := os.ReadFile(dataFile)
    var permissions map[string]interface{}
    json.Unmarshal(data, &permissions)
    
    // 🎯 UPDATE DATA STORE TẠI PATH "/permissions"
    return m.service.UpdateData(ctx, "/permissions", permissions)
    //                              ^^^^^^^^^^^^^^
    //                              Data path trong OPA store
}
```

### Permissions.json Structure
```json
{
  "license": {
    "read": "license.read",
    "create": "license.create", 
    "update": "license.update",
    "delete": "license.delete"
  },
  "machine": {
    "read": "machine.read",
    "create": "machine.create"
  }
}
```

### Access trong Rego
```rego
# File: policies/auth/main.rego
package gokeys.authz

# Access data store
available_permissions := data.permissions
# data.permissions ← mapping với UpdateData(ctx, "/permissions", permissions)

# Kiểm tra permission tồn tại
valid_permission if {
    data.permissions[input.resource.type][input.action]
}
```

---

## 8. Debug: Làm Sao Biết Path Mapping?

### A. Log OPA Queries
```go
// File: internal/domain/services/policy/opa_service.go
func (s *SimpleOPAService) Evaluate(ctx context.Context, query string, input map[string]interface{}) (bool, error) {
    // 🐛 ADD LOGGING
    log.Printf("🔍 OPA Query: %s", query)
    log.Printf("📥 OPA Input: %+v", input)
    
    result, err := s.EvaluateWithResult(ctx, query, input)
    
    log.Printf("📤 OPA Result: %+v", result)
    log.Printf("❌ OPA Error: %v", err)
    
    return result.(bool), err
}
```

### B. Rego Debugging
```rego
# File: policies/auth/main.rego
package gokeys.authz

default allow = false

# Debug rule - in ra console
debug_info := {
    "computed_permission": required_permission,
    "user_permissions": input.subject.permissions,
    "resource_info": input.resource,
    "action": input.action
}

allow if {
    required_permission in input.subject.permissions
}

required_permission = permission if {
    permission := sprintf("%s.%s", [input.resource.type, input.action])
}
```

### C. Test OPA Queries
```bash
# Test trực tiếp với OPA CLI
echo '{"subject": {"permissions": ["license.read"]}, "resource": {"type": "license"}, "action": "read"}' | \
opa eval -d policies/ "data.gokeys.authz.allow"

# Test với debug info
echo '{"subject": {"permissions": ["license.read"]}, "resource": {"type": "license"}, "action": "read"}' | \
opa eval -d policies/ "data.gokeys.authz.debug_info"
```

---

## 9. Multiple Policies & Path Resolution

### Multiple Policy Files
```
policies/
├── auth/
│   ├── main.rego         # package gokeys.authz
│   ├── admin.rego        # package gokeys.admin
│   └── ownership.rego    # package gokeys.ownership
└── license/
    └── validation.rego   # package gokeys.license.validation
```

### Different Query Paths
```go
// Different queries cho different policies
adminCheck := opa.Evaluate("data.gokeys.admin.is_super_user", input)
ownerCheck := opa.Evaluate("data.gokeys.ownership.is_owner", input) 
licenseValid := opa.Evaluate("data.gokeys.license.validation.is_valid", input)

// Main authorization vẫn dùng chung
authCheck := opa.Evaluate("data.gokeys.authz.allow", input)
```

### Policy Composition
```rego
# File: policies/auth/main.rego
package gokeys.authz

import data.gokeys.admin
import data.gokeys.ownership

allow if { admin.is_super_user }
allow if { ownership.is_owner }  
allow if { required_permission in input.subject.permissions }
```

---

## 10. Kết Luận: Mapping Summary

### 🎯 **Core Mapping Rules:**

1. **Package → Namespace:**
   ```
   package gokeys.authz → data.gokeys.authz
   ```

2. **File → Module:**
   ```
   policies/auth/main.rego → module "main"
   ```

3. **HTTP → Resource Type:**
   ```
   /licenses/:id → resource.type = "license"
   ```

4. **Method → Action:**
   ```
   GET → "read", POST → "create", PUT → "update", DELETE → "delete"
   ```

5. **Permission String → Policy Input:**
   ```
   "license.read" → required_permission = "license.read"
   ```

6. **Data Path → Store Location:**
   ```
   UpdateData("/permissions", data) → data.permissions trong Rego
   ```

### 🔧 **Fixed vs Dynamic Parts:**

**Fixed (Hard-coded):**
- Query path: `"data.gokeys.authz.allow"` 
- Package name: `gokeys.authz`
- Rule name: `allow`

**Dynamic (Runtime):**
- Resource type: từ URL path
- Action: từ HTTP method  
- Subject: từ JWT token
- Resource ID: từ URL params

Đây chính là cách OPA "định danh được cái nào cho cái nào"! 🎉