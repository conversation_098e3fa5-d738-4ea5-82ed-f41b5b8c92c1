# Tài liệu Chi tiết Lược đồ Cơ sở Dữ liệu – <PERSON><PERSON> thống Quản lý License GoKeys

## Tổng quan

Tài liệu này cung cấp mô tả chi tiết về lược đồ cơ sở dữ liệu (schema) của hệ thống quản lý license GoKeys. Lược đồ được thiết kế để hỗ trợ nền tảng quản lý license toàn diện với kiến trúc đa tenant, bảo mật mật mã và khả năng cấu hình linh hoạt thông qua các chính sách (policy).

## S<PERSON> đồ Quan hệ (ER Diagram)

```mermaid
erDiagram
    organizations
    users
    products
    policies
    licenses
    machines
    sessions
    api_tokens
    users_organizations
    permissions

    organizations ||--o{ products : "has"
    organizations ||--o{ policies : "has"
    organizations ||--o{ licenses : "has"
    organizations ||--o{ api_tokens : "has"
    organizations ||--|{ users_organizations : "links"

    users ||--o{ sessions : "has"
    users ||--o{ api_tokens : "has"
    users ||--o{ licenses : "owns"
    users ||--o{ machines : "owns"
    users ||--|{ users_organizations : "links"
    users ||--o{ permissions : "granted"

    products ||--o{ policies : "has"
    products ||--o{ licenses : "has"

    policies ||--o{ licenses : "governs"
    policies ||--o{ machines : "governs"

    licenses ||--o{ machines : "activates"
```

## Tiện ích Cơ sở Dữ liệu

```sql
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
```

- **uuid-ossp**: Kích hoạt các hàm sinh UUID để sử dụng làm khóa chính.

## Các Bảng Cốt lõi

### 1. Bảng Organizations

Bảng `organizations` Phục vụ cho việc phân cấp tổ chức trong hệ thống.

```sql
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) DEFAULT 'vendor' CHECK (type IN ('vendor', 'reseller', 'customer')),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'canceled')),
    protected BOOLEAN DEFAULT FALSE,
    public_key TEXT,
    private_key TEXT,
    secret_key TEXT,
    ed25519_private_key TEXT,
    ed25519_public_key TEXT,
    max_users INTEGER,
    max_licenses INTEGER,
    max_machines INTEGER,
    settings JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh duy nhất của tổ chức | Tự sinh |
| **name** | VARCHAR(255) | Tên hiển thị của tổ chức | Bắt buộc |
| **slug** | VARCHAR(255) | Chuỗi định danh có thể là url | Độc nhất, bắt buộc |
| **email** | VARCHAR(255) | Email liên hệ chính | Độc nhất, bắt buộc |
| **type** | VARCHAR(50) | Phân loại tổ chức | `vendor`, `reseller`, `customer` |
| **status** | VARCHAR(50) | Trạng thái hoạt động | `active`, `suspended`, `canceled` |
| **protected** | BOOLEAN | Cờ hệ thống để tránh xóa nhầm | Mặc định: FALSE |
| **public_key** | TEXT | Khóa công khai RSA | Có thể null |
| **private_key** | TEXT | Khóa riêng RSA | Có thể null |
| **secret_key** | TEXT | Khóa bí mật 128 ký tự hex | Có thể null |
| **ed25519_private_key** | TEXT | Khóa riêng Ed25519 | Có thể null |
| **ed25519_public_key** | TEXT | Khóa công khai Ed25519 | Có thể null |
| **max_users** | INTEGER | Giới hạn người dùng | Null = không giới hạn |
| **max_licenses** | INTEGER | Giới hạn số license có thể tạo | Null = không giới hạn |
| **max_machines** | INTEGER | Giới hạn máy có thể kích hoạt | Null = không giới hạn |
| **settings** | JSONB | Cấu hình JSON riêng của tổ chức | Mặc định `'{}'` |
| **metadata** | JSONB | Trường mở rộng linh hoạt | Mặc định `'{}'` |
| **created_at** | TIMESTAMP | Ngày tạo | Tự sinh |
| **updated_at** | TIMESTAMP | Ngày cập nhật cuối | Tự cập nhật |
| **deleted_at** | TIMESTAMP | Xóa mềm | Có thể null |

### 2. Bảng Users

Bảng `users` lưu thông tin tài khoản người dùng, có thể (hoặc không) thuộc về tổ chứ nào.

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    totp_secret TEXT,
    totp_enabled BOOLEAN DEFAULT FALSE,
    password_reset_token VARCHAR(255),
    password_reset_expires_at TIMESTAMP WITH TIME ZONE,
    banned_at TIMESTAMP WITH TIME ZONE,
    last_login TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh người dùng | Tự sinh |
| **email** | VARCHAR(255) | Email | Độc nhất, bắt buộc |
| **password** | VARCHAR(255) | Mật khẩu đã băm | Bắt buộc |
| **first_name** | VARCHAR(255) | Tên | Tùy chọn |
| **last_name** | VARCHAR(255) | Họ | Tùy chọn |
| **status** | VARCHAR(50) | Trạng thái tài khoản | `active`, `inactive`, `suspended` |
| **totp_secret** | TEXT | Khóa TOTP cho 2FA | Có thể null |
| **totp_enabled** | BOOLEAN | Đã kích hoạt 2FA | Mặc định FALSE |
| **password_reset_token** | VARCHAR(255) | Token khôi phục mật khẩu | Có thể null |
| **password_reset_expires_at** | TIMESTAMP | Hạn token khôi phục | Có thể null |
| **banned_at** | TIMESTAMP | Thời gian bị cấm | Có thể null |
| **last_login** | TIMESTAMP | Lần đăng nhập cuối | Có thể null |
| **metadata** | JSONB | Dữ liệu mở rộng | Mặc định `'{}'` |
| **created_at** | TIMESTAMP | Ngày tạo | Tự sinh |
| **updated_at** | TIMESTAMP | Ngày cập nhật cuối | Tự cập nhật |
| **deleted_at** | TIMESTAMP | Xóa mềm | Có thể null |

### 3. Bảng Users Organizations

`users_organizations` phản ánh mối quan hệ thành viên giữa người dùng và tổ chức (không còn lưu role; quyền nay tách sang bảng `permissions`). Giúp cho việc quản lí user của các tổ chức.

```sql
CREATE TABLE users_organizations (
    user_id UUID NOT NULL,
    organization_id UUID NOT NULL,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    invited_by UUID,
    PRIMARY KEY (user_id, organization_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **user_id** | UUID | Người dùng | FK users |
| **organization_id** | UUID | Tổ chức | FK organizations |
| **joined_at** | TIMESTAMP | Thời gian tham gia | Tự sinh |
| **invited_by** | UUID | Người mời | FK users |

### 4. Bảng Permissions

Bảng `permissions` lưu các bản ghi phân quyền theo tài nguyên, giúp cho việc quản lí quyền của người dùng trong hệ thống.

```sql
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    scope VARCHAR(255) NOT NULL, -- "system", "org:<id>", "resource:type:id", "owner"
    resource_type VARCHAR(50) NOT NULL, -- "product", "license", "user", "*"
    actions TEXT[] NOT NULL, -- ["create", "read", "update", "delete"] hoặc ["*"]
    granted_by UUID,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    UNIQUE (user_id, scope, resource_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh phân quyền | |
| **user_id** | UUID | Người dùng sở hữu quyền | FK users |
| **scope** | VARCHAR(255) | Phạm vi áp dụng | Bắt buộc |
| **resource_type** | VARCHAR(50) | Loại tài nguyên | Bắt buộc |
| **actions** | TEXT[] | Các hành động cho phép | Bắt buộc |
| **granted_by** | UUID | Người cấp quyền | FK users |
| **granted_at** | TIMESTAMP | Thời gian cấp | |
| **expires_at** | TIMESTAMP | Hết hạn (nếu có) | |

### 5. Bảng Products

Bảng `products` lưu thông tin sản phẩm phần mềm.

```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(255) NOT NULL,
    description TEXT,
    url VARCHAR(255),
    distribution_strategy VARCHAR(255),
    platforms TEXT[],
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    UNIQUE (organization_id, code)
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh sản phẩm | Tự sinh |
| **organization_id** | UUID | Tổ chức sở hữu | FK organizations |
| **name** | VARCHAR(255) | Tên sản phẩm | Bắt buộc |
| **code** | VARCHAR(255) | Mã sản phẩm duy nhất trong tổ chức | Bắt buộc |
| **description** | TEXT | Mô tả chi tiết | Tùy chọn |
| **url** | VARCHAR(255) | URL website/tài liệu | Tùy chọn |
| **distribution_strategy** | VARCHAR(255) | Chiến lược phân phối (download/cloud/hybrid) | Tùy chọn |
| **platforms** | TEXT[] | Danh sách nền tảng hỗ trợ | Tùy chọn |
| **metadata** | JSONB | Cấu hình bổ sung | Mặc định '{}' |
| **created_at** | TIMESTAMP | Ngày tạo | Tự sinh |
| **updated_at** | TIMESTAMP | Ngày cập nhật cuối | Tự cập nhật |
| **deleted_at** | TIMESTAMP | Xóa mềm | Có thể null |

### 6. Bảng Policies

Bảng `policies` định nghĩa các quy tắc hành vi và xác thực license toàn diện.

```sql
CREATE TABLE policies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    product_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    
    -- Basic configuration
    strict BOOLEAN DEFAULT FALSE,
    protected BOOLEAN DEFAULT FALSE,
    duration INTEGER, -- in seconds
    lock_version INTEGER DEFAULT 0,
    
    -- License behavior
    floating BOOLEAN DEFAULT FALSE, -- License transferability
    use_pool BOOLEAN DEFAULT FALSE, -- License pooling
    encrypted BOOLEAN DEFAULT FALSE, -- License encryption
    
    -- Cryptographic scheme
    scheme VARCHAR(50) DEFAULT 'ED25519_SIGN' CHECK (scheme IN (
        'LEGACY_ENCRYPT', 'RSA_2048_PKCS1_ENCRYPT', 'RSA_2048_PKCS1_SIGN',
        'RSA_2048_PKCS1_PSS_SIGN', 'RSA_2048_JWT_RS256', 'ED25519_SIGN', 'ED25519_JWT_ES256'
    )),
    
    -- Limits and constraints
    max_machines INTEGER,
    max_uses INTEGER,
    max_cores INTEGER,
    max_users INTEGER,
    max_processes INTEGER,
    
    -- Heartbeat configuration
    require_heartbeat BOOLEAN DEFAULT FALSE,
    heartbeat_duration INTEGER, -- in seconds
    heartbeat_basis VARCHAR(255),
    heartbeat_cull_strategy VARCHAR(255),
    heartbeat_resurrection_strategy VARCHAR(255),
    
    -- Check-in configuration
    require_check_in BOOLEAN DEFAULT FALSE,
    check_in_interval VARCHAR(255),
    check_in_interval_count INTEGER,
    
    -- Strategy configurations
    machine_uniqueness_strategy VARCHAR(255),
    machine_matching_strategy VARCHAR(255),
    machine_leasing_strategy VARCHAR(255),
    component_uniqueness_strategy VARCHAR(255),
    component_matching_strategy VARCHAR(255),
    process_leasing_strategy VARCHAR(255),
    expiration_strategy VARCHAR(255),
    expiration_basis VARCHAR(255),
    renewal_basis VARCHAR(255),
    authentication_strategy VARCHAR(255),
    transfer_strategy VARCHAR(255),
    overage_strategy VARCHAR(255),
    
    -- Scope requirements
    require_product_scope BOOLEAN DEFAULT FALSE,
    require_policy_scope BOOLEAN DEFAULT FALSE,
    require_machine_scope BOOLEAN DEFAULT FALSE,
    require_fingerprint_scope BOOLEAN DEFAULT FALSE,
    require_user_scope BOOLEAN DEFAULT FALSE,
    require_checksum_scope BOOLEAN DEFAULT FALSE,
    require_version_scope BOOLEAN DEFAULT FALSE,
    require_components_scope BOOLEAN DEFAULT FALSE,
    
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh chính sách | Tự sinh |
| **organization_id** | UUID | Tổ chức sở hữu | FK organizations |
| **product_id** | UUID | Sản phẩm áp dụng | FK products |
| **name** | VARCHAR(255) | Tên chính sách | Bắt buộc |
| **description** | TEXT | Mô tả chi tiết | Tùy chọn |
| **strict** | BOOLEAN | Chế độ xác thực nghiêm ngặt | Mặc định FALSE |
| **scheme** | VARCHAR(255) | Sơ đồ mã hóa license | LEGACY_ENCRYPT/RSA_2048/ED25519_SIGN |
| **duration** | INTEGER | Thời hạn license (giây) | Null = vĩnh viễn |
| **lock_version** | INTEGER | Phiên bản khóa optimistic locking | Mặc định 0 |
| **floating** | BOOLEAN | License có thể chuyển nhượng | Mặc định FALSE |
| **use_pool** | BOOLEAN | Sử dụng key pool | Mặc định FALSE |
| **encrypted** | BOOLEAN | License được mã hóa | Mặc định FALSE |
| **max_machines** | INTEGER | Số máy tối đa | Null = không giới hạn |
| **max_uses** | INTEGER | Số lần sử dụng tối đa | Null = không giới hạn |
| **max_cores** | INTEGER | Số lõi CPU tối đa | Null = không giới hạn |
| **max_users** | INTEGER | Số người dùng tối đa | Null = không giới hạn |
| **max_processes** | INTEGER | Số process tối đa | Null = không giới hạn |
| **protected** | BOOLEAN | Cờ bảo vệ khỏi xóa nhầm | Mặc định FALSE |
| **require_heartbeat** | BOOLEAN | Yêu cầu máy gửi heartbeat | Mặc định FALSE |
| **heartbeat_duration** | INTEGER | Chu kỳ heartbeat (giây) | Bắt buộc nếu require_heartbeat=true |
| **heartbeat_basis** | VARCHAR(255) | Cơ sở tính heartbeat | Tùy chọn |
| **heartbeat_cull_strategy** | VARCHAR(255) | Chiến lược xử lý máy chết | Tùy chọn |
| **heartbeat_resurrection_strategy** | VARCHAR(255) | Chiến lược hồi sinh máy | Tùy chọn |
| **require_check_in** | BOOLEAN | Yêu cầu check-in | Mặc định FALSE |
| **check_in_interval** | VARCHAR(255) | Khoảng thời gian check-in | Tùy chọn |
| **check_in_interval_count** | INTEGER | Số lượng interval | Tùy chọn |
| **machine_uniqueness_strategy** | VARCHAR(255) | Chiến lược duy nhất máy | Tùy chọn |
| **machine_matching_strategy** | VARCHAR(255) | Chiến lược khớp máy | Tùy chọn |
| **component_uniqueness_strategy** | VARCHAR(255) | Chiến lược duy nhất component | Tùy chọn |
| **component_matching_strategy** | VARCHAR(255) | Chiến lược khớp component | Tùy chọn |
| **machine_leasing_strategy** | VARCHAR(255) | Chiến lược thuê máy | Tùy chọn |
| **process_leasing_strategy** | VARCHAR(255) | Chiến lược thuê process | Tùy chọn |
| **overage_strategy** | VARCHAR(255) | Chiến lược vượt giới hạn | Tùy chọn |
| **expiration_strategy** | VARCHAR(255) | Chiến lược hết hạn | Tùy chọn |
| **expiration_basis** | VARCHAR(255) | Cơ sở tính hết hạn | Tùy chọn |
| **renewal_basis** | VARCHAR(255) | Cơ sở gia hạn | Tùy chọn |
| **transfer_strategy** | VARCHAR(255) | Chiến lược chuyển nhượng | Tùy chọn |
| **authentication_strategy** | VARCHAR(255) | Chiến lược xác thực | Tùy chọn |
| **require_product_scope** | BOOLEAN | Yêu cầu scope sản phẩm | Mặc định FALSE |
| **require_policy_scope** | BOOLEAN | Yêu cầu scope chính sách | Mặc định FALSE |
| **require_machine_scope** | BOOLEAN | Yêu cầu scope máy | Mặc định FALSE |
| **require_fingerprint_scope** | BOOLEAN | Yêu cầu scope fingerprint | Mặc định FALSE |
| **require_user_scope** | BOOLEAN | Yêu cầu scope người dùng | Mặc định FALSE |
| **require_checksum_scope** | BOOLEAN | Yêu cầu scope checksum | Mặc định FALSE |
| **require_version_scope** | BOOLEAN | Yêu cầu scope phiên bản | Mặc định FALSE |
| **require_components_scope** | BOOLEAN | Yêu cầu scope component | Mặc định FALSE |
| **metadata** | JSONB | Dữ liệu mở rộng | Mặc định '{}' |
| **created_at** | TIMESTAMP | Ngày tạo | Tự sinh |
| **updated_at** | TIMESTAMP | Ngày cập nhật cuối | Tự cập nhật |
| **deleted_at** | TIMESTAMP | Xóa mềm | Có thể null |

### 7. Bảng Licenses

Bảng `licenses` lưu các phiên bản license cụ thể với trạng thái và thông tin theo dõi.

```sql
CREATE TABLE licenses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL,
    product_id UUID NOT NULL,
    policy_id UUID NOT NULL,
    key VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'expired', 'suspended')),
    uses INTEGER DEFAULT 0,
    protected BOOLEAN DEFAULT FALSE,
    version VARCHAR(255),
    user_id UUID,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_validated_at TIMESTAMP WITH TIME ZONE,
    last_validated_checksum VARCHAR(255),
    last_validated_version VARCHAR(255),
    last_check_in_at TIMESTAMP WITH TIME ZONE,
    last_check_out_at TIMESTAMP WITH TIME ZONE,
    last_expiration_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_expiring_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    last_check_in_soon_event_sent_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    UNIQUE (organization_id, key)
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh license | Tự sinh |
| **organization_id** | UUID | Tổ chức sở hữu | FK organizations |
| **product_id** | UUID | Sản phẩm | FK products |
| **policy_id** | UUID | Chính sách áp dụng | FK policies |
| **key** | VARCHAR(255) | Khóa license duy nhất | Bắt buộc, unique per org |
| **name** | VARCHAR(255) | Tên hiển thị | Tùy chọn |
| **status** | VARCHAR(50) | Trạng thái license | active/inactive/expired/suspended |
| **uses** | INTEGER | Số lần đã sử dụng | Mặc định 0 |
| **protected** | BOOLEAN | Cờ bảo vệ | Mặc định FALSE |
| **version** | VARCHAR(255) | Phiên bản phần mềm | Tùy chọn |
| **user_id** | UUID | Người sở hữu | FK users |
| **expires_at** | TIMESTAMP | Thời gian hết hạn | Null = không hết hạn |
| **last_validated_at** | TIMESTAMP | Lần xác thực cuối | Null |
| **last_check_in_at** | TIMESTAMP | Lần check-in cuối | Null |
| **last_check_out_at** | TIMESTAMP | Lần check-out cuối | Null |

### 8. Bảng Machines

Bảng `machines` theo dõi các máy đã kích hoạt license.

```sql
CREATE TABLE machines (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    license_id UUID NOT NULL,
    policy_id UUID NOT NULL,
    owner_id UUID,
    fingerprint VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    hostname VARCHAR(255),
    platform VARCHAR(255),
    ip VARCHAR(45),
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    activated_at TIMESTAMP WITH TIME ZONE,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    last_seen TIMESTAMP WITH TIME ZONE,
    cores INTEGER DEFAULT 0,
    components JSONB DEFAULT '{}',
    last_heartbeat_at TIMESTAMP WITH TIME ZONE,
    next_heartbeat_at TIMESTAMP WITH TIME ZONE,
    last_death_event_sent_at TIMESTAMP WITH TIME ZONE,
    heartbeat_jid VARCHAR(255),
    last_check_out_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    FOREIGN KEY (policy_id) REFERENCES policies(id) ON DELETE CASCADE,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE (license_id, fingerprint)
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh máy | Tự sinh |
| **license_id** | UUID | License đang sử dụng | FK licenses |
| **policy_id** | UUID | Chính sách áp dụng | FK policies |
| **owner_id** | UUID | Chủ sở hữu máy | FK users |
| **fingerprint** | VARCHAR(255) | Dấu vân tay máy | Unique per license |
| **name** | VARCHAR(255) | Tên hiển thị | Tùy chọn |
| **hostname** | VARCHAR(255) | Tên máy trên mạng | Tùy chọn |
| **platform** | VARCHAR(255) | Hệ điều hành | VD: windows/linux/macos |
| **ip** | VARCHAR(45) | Địa chỉ IP | IPv4/IPv6 |
| **status** | VARCHAR(50) | Trạng thái máy | active/inactive |
| **activated_at** | TIMESTAMP | Thời gian kích hoạt | Null |
| **last_seen** | TIMESTAMP | Lần liên lạc cuối | Null |
| **cores** | INTEGER | Số lõi CPU | Mặc định 0 |
| **components** | JSONB | Thông tin phần cứng | JSON object |

### 9. Bảng Sessions

Bảng `sessions` quản lý phiên đăng nhập của người dùng.

```sql
CREATE TABLE sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    ip VARCHAR(45),
    user_agent TEXT,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh phiên | Tự sinh |
| **user_id** | UUID | Người dùng sở hữu | FK users |
| **token_hash** | VARCHAR(255) | Token đã băm | Unique, bắt buộc |
| **ip** | VARCHAR(45) | Địa chỉ IP tạo phiên | IPv4/IPv6 |
| **user_agent** | TEXT | Chuỗi user agent | Tùy chọn |
| **last_used_at** | TIMESTAMP | Hoạt động cuối | Null |
| **expires_at** | TIMESTAMP | Thời gian hết hạn | Bắt buộc |
| **created_at** | TIMESTAMP | Thời gian tạo | Tự sinh |

### 10. Bảng API Tokens

Bảng `api_tokens` quản lý token truy cập API cho ứng dụng.

```sql
CREATE TABLE api_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID,
    organization_id UUID,
    name VARCHAR(255) NOT NULL,
    token_hash VARCHAR(255) UNIQUE NOT NULL,
    permissions TEXT[],
    active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
);
```

#### Mô tả Trường

| Trường | Kiểu | Mô tả | Ghi chú |
|--------|------|-------|---------|
| **id** | UUID | Định danh API token | Tự sinh |
| **user_id** | UUID | Người dùng sở hữu | FK users |
| **organization_id** | UUID | Tổ chức sở hữu | FK organizations |
| **name** | VARCHAR(255) | Tên hiển thị token | Bắt buộc |
| **token_hash** | VARCHAR(255) | Token đã băm | Unique, bắt buộc |
| **permissions** | TEXT[] | Mảng quyền cho OPA | Null |
| **active** | BOOLEAN | Token có hoạt động | Mặc định TRUE |
| **expires_at** | TIMESTAMP | Thời gian hết hạn | Null = không hết hạn |
| **last_used_at** | TIMESTAMP | Lần sử dụng cuối | Null |
| **created_at** | TIMESTAMP | Thời gian tạo | Tự sinh |
| **updated_at** | TIMESTAMP | Thời gian cập nhật | Tự cập nhật |

## Quan hệ & Ràng buộc Chính

### Quan hệ chính
- Một tổ chức có thể có nhiều users, products, policies, licenses
- Người dùng có thể thuộc nhiều tổ chức thông qua `users_organizations`
- Sản phẩm thuộc tổ chức và có nhiều policies
- Policies định nghĩa luật cho licenses và machines
- License là phiên bản của product với policy cụ thể
- Machine được kích hoạt bởi license
- Sessions và API tokens hỗ trợ xác thực

### Ràng buộc khoá chính & phụ
- Tất cả bảng dùng UUID làm khóa chính
- Xóa mềm qua trường `deleted_at`
- Ràng buộc duy nhất tránh trùng khóa, slug
- Khoá ngoại đảm bảo toàn vẹn tham chiếu
- CHECK đảm bảo giá trị enum hợp lệ
- Timestamps luôn kèm múi giờ để phù hợp môi trường toàn cầu

## Bảo mật
1. **Khoá mật mã**: Khóa riêng được mã hóa khi lưu trữ.
2. **Mật khẩu**: Password được băm.
3. **Token**: Session/API token lưu dạng băm.
4. **Xóa mềm**: Dữ liệu nhạy cảm được xóa mềm để giữ audit.
5. **UUID**: Ngăn dò index.
6. **Phân quyền chi tiết**: `permissions` hỗ trợ tích hợp OPA.

## Hiệu năng
1. **Chỉ mục**: Ràng buộc UNIQUE tạo index ngầm.
2. **JSONB**: Cho phép truy vấn JSON hiệu quả.
3. **Timestamps**: Lưu kèm timezone.
4. **Khoá ngoại**: Quy tắc CASCADE tránh mồ côi.
5. **Mảng**: PostgreSQL array cho trường đa giá trị.

Lược đồ này cung cấp nền tảng vững chắc cho hệ thống quản lý license có độ bảo mật, linh hoạt và khả năng mở rộng cao.
