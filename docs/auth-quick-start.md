# Authentication & Authorization Quick Start

This guide helps you quickly get started with the GoKeys authentication and authorization system.

## Quick Setup

### 1. Initialize Middleware

```go
package main

import (
    "github.com/gin-gonic/gin"
    "github.com/gokeys/gokeys/internal/adapters/http/middleware"
    "github.com/gokeys/gokeys/internal/domain/services"
)

func main() {
    // Setup router
    router := gin.Default()
    
    // Initialize service coordinator (assumes you have a database connection)
    serviceCoordinator := services.NewServiceCoordinator(db)
    
    // Create authentication middleware
    authMiddleware := middleware.NewAuthenticationMiddleware(
        serviceCoordinator,
        "your-secret-key", // Use environment variable in production
    )
    
    // Create authorization middleware with OPA support
    authzMiddleware := middleware.NewAuthorizationMiddleware(serviceCoordinator)
    
    // Setup routes
    setupRoutes(router, authMiddleware, authzMiddleware)
    
    router.Run(":8080")
}
```

### 2. Protect Routes

```go
func setupRoutes(
    router *gin.Engine, 
    auth *middleware.AuthenticationMiddleware,
    authz *middleware.AuthorizationMiddleware,
) {
    api := router.Group("/api/v1")
    
    // Public routes (no authentication required)
    api.GET("/health", handlers.HealthCheck)
    
    // Protected routes
    protected := api.Group("")
    protected.Use(auth.RequireAuthentication())
    {
        // Basic protected route
        protected.GET("/account", handlers.GetAccount)
        
        // License operations with OPA-based authorization
        licenses := protected.Group("/licenses")
        {
            licenses.GET("", 
                authz.CreatePermissionChecker("license.read"),
                handlers.ListLicenses)
            licenses.POST("", 
                authz.CreatePermissionChecker("license.create"),
                handlers.CreateLicense)
            licenses.GET("/:id", 
                authz.RequireOwnershipOrPermission("license", "id"),
                handlers.GetLicense)
        }
        
        // License validation (special permissions)
        licenses.POST("/:id/actions/validate", 
            authz.CreatePermissionChecker("license.validate"),
            handlers.ValidateLicense,
        )
        
        // Machine operations with OPA-based authorization
        machines := protected.Group("/machines")
        {
            machines.GET("", handlers.ListMachines)
            machines.POST("", handlers.CreateMachine)
        }
        
        // Machine heartbeat (specific permission)
        machines.POST("/:id/actions/ping",
            authz.CreatePermissionChecker("machine.heartbeat.ping"),
            handlers.PingMachine,
        )
        
        // Admin-only operations
        admin := protected.Group("/admin")
        admin.Use(authz.CreatePermissionChecker("*"))
        {
            admin.GET("/users", handlers.ListAllUsers)
            admin.DELETE("/users/:id", handlers.DeleteUser)
        }
    }
}
```

### 3. Handle Authentication in Handlers

```go
package handlers

import (
    "net/http"
    "github.com/gin-gonic/gin"
    "github.com/gokeys/gokeys/internal/adapters/http/middleware"
    "github.com/gokeys/gokeys/internal/domain/entities"
)

func ValidateLicense(c *gin.Context) {
    // Get authenticated token
    token, exists := middleware.GetToken(c)
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{
            "error": "authentication_required",
        })
        return
    }
    
    // Get account ID from context
    accountID, err := middleware.GetAccountID(c)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            "error": "invalid_account_context",
        })
        return
    }
    
    // Get license ID from URL
    licenseID := c.Param("id")
    
    // Check if token can validate this specific license
    if token.BearerType == entities.TokenBearerTypeLicense {
        // License tokens can only validate themselves
        if token.BearerID != licenseID {
            c.JSON(http.StatusForbidden, gin.H{
                "error": "license_token_mismatch",
                "message": "License tokens can only validate themselves",
            })
            return
        }
    }
    
    // Your validation logic here
    result := performLicenseValidation(licenseID, accountID)
    
    c.JSON(http.StatusOK, result)
}

func CreateLicense(c *gin.Context) {
    // Get token info
    token, _ := middleware.GetToken(c)
    accountID, _ := middleware.GetAccountID(c)
    
    // Only environment and admin tokens can create licenses
    allowedTypes := []entities.TokenBearerType{
        entities.TokenBearerTypeEnvironment,
    }
    
    allowed := false
    for _, allowedType := range allowedTypes {
        if token.BearerType == allowedType {
            allowed = true
            break
        }
    }
    
    // Admin tokens have wildcard permission
    if token.HasPermission("*") {
        allowed = true
    }
    
    if !allowed {
        c.JSON(http.StatusForbidden, gin.H{
            "error": "insufficient_permissions",
            "message": "Only environment tokens can create licenses",
        })
        return
    }
    
    // Your license creation logic here
    license := createLicense(accountID, c)
    
    c.JSON(http.StatusCreated, license)
}
```

## Authentication Methods

### 1. Bearer Token (Recommended)

```bash
curl -H "Authorization: Bearer env-abc123def456...v3" \
     https://api.example.com/licenses
```

### 2. Basic Authentication

```bash
# With token
curl -u "token:env-abc123def456...v3" \
     https://api.example.com/licenses

# With license key
curl -u "license:LIC-12345-ABCDE" \
     https://api.example.com/licenses
```

### 3. License Header

```bash
curl -H "Authorization: License LIC-12345-ABCDE" \
     https://api.example.com/licenses
```

### 4. Query Parameters

```bash
# Token parameter
curl "https://api.example.com/licenses?token=env-abc123def456...v3"

# Auth parameter
curl "https://api.example.com/licenses?auth=license:LIC-12345-ABCDE"
```

## Common Permission Patterns

### License Operations

```go
// Read licenses
authz.CreatePermissionChecker("license.read")

// Validate licenses  
authz.CreatePermissionChecker("license.validate")

// Create licenses
authz.CreatePermissionChecker("license.create")

// Modify licenses
authz.CreatePermissionChecker("license.update")

// Delete licenses
authz.CreatePermissionChecker("license.delete")
```

### Machine Operations

```go
// Read machines
authz.CreatePermissionChecker("machine.read")

// Create machines
authz.CreatePermissionChecker("machine.create")

// Heartbeat operations
authz.CreatePermissionChecker("machine.heartbeat.ping")

// Machine checkout
authz.CreatePermissionChecker("machine.check-out")
```

### User Management

```go
// Read users
authz.CreatePermissionChecker("user.read")

// Create users
authz.CreatePermissionChecker("user.create")

// Admin operations
authz.CreatePermissionChecker("*") // Wildcard permission required
```

## Token Types & Use Cases

### Environment Tokens (`env-...`)
- **Use Case**: Automation, CI/CD, server-to-server
- **Permissions**: Broad access for license/machine management
- **Example**: Creating licenses, managing machines, user operations

### User Tokens (`user-...`)
- **Use Case**: End-user applications, customer portals
- **Permissions**: Limited to user-specific operations
- **Example**: Viewing own licenses, updating profile

### License Tokens (`activ-...`)
- **Use Case**: License activation, machine binding
- **Permissions**: Minimal set for activation scenarios
- **Example**: License validation, machine creation, heartbeat

### Product Tokens (`prod-...`)
- **Use Case**: Product-scoped operations
- **Permissions**: Product-specific management
- **Example**: Managing licenses for a specific product

## Error Handling

### Common Error Responses

```json
// No authentication provided
{
  "error": "authentication_required",
  "message": "Authentication required to access this resource"
}

// Invalid or expired token
{
  "error": "token_expired",
  "message": "Token has expired"
}

// Insufficient permissions
{
  "error": "insufficient_permissions", 
  "message": "Insufficient permissions to access this resource",
  "required_permissions": ["license.validate"]
}

// Wrong token type
{
  "error": "role_not_allowed",
  "message": "Token role is not allowed to access this resource",
  "bearer_type": "license",
  "allowed_roles": ["user", "environment"]
}
```

### Error Handling in Code

```go
func MyHandler(c *gin.Context) {
    token, exists := middleware.GetToken(c)
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{
            "error": "authentication_required",
            "message": "This endpoint requires authentication",
        })
        return
    }
    
    if token.IsExpired() {
        c.JSON(http.StatusUnauthorized, gin.H{
            "error": "token_expired", 
            "message": "Your token has expired, please obtain a new one",
        })
        return
    }
    
    if !token.HasPermission("license.read") {
        c.JSON(http.StatusForbidden, gin.H{
            "error": "insufficient_permissions",
            "message": "You don't have permission to read licenses",
            "required_permissions": []string{"license.read"},
        })
        return
    }
    
    // Success path
    c.JSON(http.StatusOK, gin.H{"message": "success"})
}
```

## Testing Authentication

### Unit Tests

```go
func TestAuthenticatedEndpoint(t *testing.T) {
    // Setup test router with middleware
    router := gin.New()
    authMiddleware := middleware.NewAuthenticationMiddleware(mockService, "test-key")
    router.Use(authMiddleware.RequireAuthentication())
    router.GET("/test", func(c *gin.Context) {
        c.JSON(200, gin.H{"message": "success"})
    })
    
    // Test without authentication
    w := httptest.NewRecorder()
    req, _ := http.NewRequest("GET", "/test", nil)
    router.ServeHTTP(w, req)
    assert.Equal(t, 401, w.Code)
    
    // Test with valid token
    w = httptest.NewRecorder()
    req, _ = http.NewRequest("GET", "/test", nil)
    req.Header.Set("Authorization", "Bearer valid-token")
    router.ServeHTTP(w, req)
    assert.Equal(t, 200, w.Code)
}
```

### Integration Tests

```bash
# Test authentication methods
curl -i -H "Authorization: Bearer env-test123...v3" http://localhost:8080/api/v1/licenses
curl -i -u "token:env-test123...v3" http://localhost:8080/api/v1/licenses  
curl -i -H "Authorization: License LIC-TEST-12345" http://localhost:8080/api/v1/licenses

# Test permission errors
curl -i -H "Authorization: Bearer license-token...v3" http://localhost:8080/api/v1/admin/users
```

## Production Considerations

### Security

1. **Use Environment Variables**:
   ```bash
   export GOKEYS_SECRET_KEY="your-production-secret-key"
   ```

2. **Enable HTTPS**: Always use TLS in production

3. **Implement Rate Limiting**: Prevent brute force attacks

4. **Monitor Token Usage**: Track suspicious activity

### Performance

1. **Token Caching**: Cache frequently used tokens
2. **Database Indexing**: Index token digest and bearer fields
3. **Connection Pooling**: Use database connection pools

### Monitoring

1. **Authentication Metrics**: Track success/failure rates
2. **Permission Violations**: Monitor unauthorized access attempts
3. **Token Lifecycle**: Track token creation, usage, and expiry

---

For complete documentation, see [Authentication & Authorization System](./authentication-authorization.md)
