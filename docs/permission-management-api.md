# Permission Management API

This document provides comprehensive documentation for the GoKeys permission management API, including endpoints, request/response formats, and usage examples.

## Overview

The permission management system uses a **flattened permission key architecture** for high-performance authorization:

- **Permission Key Format**: `{scope}:{resource_type}:{action}`
- **Hierarchy**: System (S) → Organization (O:uuid) → Resource (R)
- **Performance**: O(1) database lookups with optimized indexes

## Base URL

All permission management endpoints are under the admin namespace:

```
BASE_URL: /api/v1/admin/permissions
```

**Authentication Required**: All endpoints require system administrator permissions.

---

## Permission Scopes

### System Scope (S)
- **Format**: `S`
- **Description**: System-wide permissions (highest priority)
- **Example**: `S:license:read` - Read all licenses across all organizations

### Organization Scope (O:uuid)
- **Format**: `O:{organization_id}`
- **Description**: Organization-specific permissions
- **Example**: `O:550e8400-e29b-41d4-a716-************:license:read` - Read licenses only in specific organization

### Resource Scope (R)
- **Format**: `R`
- **Description**: Resource-level permissions with optional resource ID constraints
- **Example**: `R:license:read` - Read specific licenses (constrained by `resource_ids`)

---

## Resource Types

| Resource Type | Description |
|---------------|-------------|
| `*` | All resources (wildcard) |
| `organization` | Organization management |
| `product` | Product management |
| `policy` | Policy management |
| `license` | License management |
| `machine` | Machine management |
| `user` | User management |
| `api_token` | API token management |
| `session` | Session management |

---

## Actions

| Action | Description |
|--------|-------------|
| `*` | All actions (wildcard) |
| `create` | Create new resources |
| `read` | View/list resources |
| `update` | Modify existing resources |
| `delete` | Remove resources |
| `validate` | Validate licenses |
| `checkout` | Checkout licenses/machines |

---

## API Endpoints

### 1. Grant Permissions

#### Grant Single Permission Set
```http
POST /api/v1/admin/permissions/grant
```

**Request Body:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "scope": "O:550e8400-e29b-41d4-a716-************",
  "resource_type": "license",
  "actions": ["read", "update"],
  "resource_ids": ["license-123", "license-456"],
  "expires_at": "2024-12-31T23:59:59Z",
  "reason": "Granting license management permissions for Q4 2024"
}
```

**Response (201 Created):**
```json
{
  "message": "Permissions granted successfully",
  "granted_count": 2,
  "permissions": [
    {
      "id": "perm-123",
      "user_id": "550e8400-e29b-41d4-a716-446655440000",
      "permission_key": "O:550e8400-e29b-41d4-a716-************:license:read",
      "scope": "O:550e8400-e29b-41d4-a716-************",
      "resource_type": "license",
      "action": "read",
      "resource_ids": ["license-123", "license-456"],
      "granted_by": "550e8400-e29b-41d4-a716-446655440002",
      "granted_at": "2024-01-15T10:30:00Z",
      "expires_at": "2024-12-31T23:59:59Z",
      "is_active": true,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

#### Batch Grant Permissions
```http
POST /api/v1/admin/permissions/batch-grant
```

**Request Body:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "reason": "Initial role setup for new team member",
  "grants": [
    {
      "scope": "O:550e8400-e29b-41d4-a716-************",
      "resource_type": "license",
      "actions": ["read", "update"],
      "resource_ids": [],
      "expires_at": "2024-12-31T23:59:59Z"
    },
    {
      "scope": "O:550e8400-e29b-41d4-a716-************",
      "resource_type": "product",
      "actions": ["read"],
      "resource_ids": ["product-abc", "product-xyz"]
    }
  ]
}
```

### 2. Revoke Permissions

#### Revoke Permissions by Scope/Resource
```http
DELETE /api/v1/admin/permissions/revoke
```

**Request Body:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "scope": "O:550e8400-e29b-41d4-a716-************",
  "resource_type": "license",
  "reason": "User left the organization"
}
```

#### Revoke Specific Permission
```http
DELETE /api/v1/admin/permissions/revoke-specific
```

**Request Body:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "permission_key": "O:550e8400-e29b-41d4-a716-************:license:read",
  "reason": "Removing specific read access"
}
```

#### Revoke All User Permissions
```http
DELETE /api/v1/admin/permissions/users/{user_id}/revoke-all
```

**Response (200 OK):**
```json
{
  "message": "Permissions revoked successfully",
  "revoked_count": 5
}
```

### 3. Check Permissions

#### Check Specific Permission
```http
POST /api/v1/admin/permissions/check
```

**Request Body:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "scope": "O:550e8400-e29b-41d4-a716-************",
  "resource_type": "license",
  "action": "read",
  "resource_id": "license-123"
}
```

**Response (200 OK):**
```json
{
  "has_permission": true,
  "matched_permission": {
    "id": "perm-123",
    "user_id": "550e8400-e29b-41d4-a716-446655440000",
    "permission_key": "O:550e8400-e29b-41d4-a716-************:license:read",
    "scope": "O:550e8400-e29b-41d4-a716-************",
    "resource_type": "license",
    "action": "read",
    "is_active": true
  },
  "checked_scopes": ["S", "O:550e8400-e29b-41d4-a716-************", "R"],
  "checked_keys": [
    "O:550e8400-e29b-41d4-a716-************:license:read",
    "O:550e8400-e29b-41d4-a716-************:license:*",
    "S:license:read",
    "S:license:*",
    "S:*:*"
  ]
}
```

### 4. List Permissions

#### List with Filters
```http
GET /api/v1/admin/permissions?user_id={user_id}&scope_pattern={pattern}&resource_type={type}&action={action}&include_expired={bool}&page={n}&page_size={n}
```

**Query Parameters:**
- `user_id` (optional): Filter by specific user
- `scope_pattern` (optional): Filter by scope pattern (e.g., "O:*" for all organization permissions)
- `resource_type` (optional): Filter by resource type
- `action` (optional): Filter by action
- `include_expired` (optional): Include expired permissions (default: false)
- `page` (optional): Page number (default: 1)
- `page_size` (optional): Page size (default: 20, max: 100)

**Response (200 OK):**
```json
{
  "permissions": [
    {
      "id": "perm-123",
      "user_id": "550e8400-e29b-41d4-a716-446655440000",
      "permission_key": "O:550e8400-e29b-41d4-a716-************:license:read",
      "scope": "O:550e8400-e29b-41d4-a716-************",
      "resource_type": "license",
      "action": "read",
      "resource_ids": ["license-123"],
      "granted_by": "550e8400-e29b-41d4-a716-446655440002",
      "granted_at": "2024-01-15T10:30:00Z",
      "expires_at": "2024-12-31T23:59:59Z",
      "is_active": true,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "page_size": 20,
    "total": 150,
    "total_pages": 8
  }
}
```

### 5. Get User Permissions

#### Get User Permission Summary
```http
GET /api/v1/admin/permissions/users/{user_id}
```

**Response (200 OK):**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440000",
  "total_permissions": 15,
  "system_permissions": 2,
  "org_permissions": 10,
  "resource_permissions": 3,
  "expiring_permissions": 1,
  "organizations": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-446655440002"
  ],
  "effective_scopes": [
    "S",
    "O:550e8400-e29b-41d4-a716-************",
    "R"
  ],
  "permissions": [
    {
      "id": "perm-123",
      "permission_key": "S:*:*",
      "scope": "S",
      "resource_type": "*",
      "action": "*",
      "is_active": true
    }
  ]
}
```

### 6. Get Permission Statistics

#### Get System Permission Stats
```http
GET /api/v1/admin/permissions/stats
```

**Response (200 OK):**
```json
{
  "total_permissions": 1250,
  "active_permissions": 1180,
  "expired_permissions": 70,
  "users_with_permissions": 45,
  "scope_breakdown": {
    "system": 5,
    "organization": 1000,
    "resource": 175
  },
  "resource_breakdown": {
    "license": 800,
    "product": 200,
    "user": 150,
    "machine": 100
  },
  "action_breakdown": {
    "read": 600,
    "update": 400,
    "delete": 180,
    "create": 70
  },
  "expiring_in_30_days": 25
}
```

---

## Usage Examples

### Example 1: Grant Organization Admin Permissions

Grant comprehensive organization admin permissions to a user:

```bash
curl -X POST "https://api.gokeys.com/api/v1/admin/permissions/batch-grant" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user-123",
    "reason": "Promoting user to organization administrator",
    "grants": [
      {
        "scope": "O:org-456",
        "resource_type": "*",
        "actions": ["*"]
      }
    ]
  }'
```

### Example 2: Grant Limited License Management

Grant read-only access to specific licenses:

```bash
curl -X POST "https://api.gokeys.com/api/v1/admin/permissions/grant" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user-789",
    "scope": "O:org-456",
    "resource_type": "license",
    "actions": ["read"],
    "resource_ids": ["license-abc", "license-xyz"],
    "expires_at": "2024-06-30T23:59:59Z",
    "reason": "Temporary access for audit purposes"
  }'
```

### Example 3: Check User Permission

Check if a user can update a specific license:

```bash
curl -X POST "https://api.gokeys.com/api/v1/admin/permissions/check" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user-123",
    "scope": "O:org-456", 
    "resource_type": "license",
    "action": "update",
    "resource_id": "license-abc"
  }'
```

### Example 4: List Organization Permissions

List all permissions for a specific organization:

```bash
curl -X GET "https://api.gokeys.com/api/v1/admin/permissions?scope_pattern=O:org-456&page=1&page_size=50" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### Example 5: Revoke User Access

Remove all organization access when user leaves:

```bash
curl -X DELETE "https://api.gokeys.com/api/v1/admin/permissions/revoke" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "user-123",
    "scope": "O:org-456",
    "resource_type": "*",
    "reason": "User left organization"
  }'
```

---

## Permission Hierarchy Logic

The system automatically checks permissions in hierarchical order:

1. **System Permissions** (`S:*:*`) - Highest priority
2. **Organization Permissions** (`O:uuid:*:*`) - Organization admin
3. **Specific Permissions** (`O:uuid:resource:action`) - Granular access
4. **Resource Permissions** (`R:resource:action`) - Resource-level access

### Hierarchy Examples

For permission check: `O:org-123:license:read`

**Check Order:**
1. `S:license:read` - System license read
2. `S:license:*` - System license admin  
3. `S:*:read` - System read-only
4. `S:*:*` - System admin
5. `O:org-123:license:read` - Exact match
6. `O:org-123:license:*` - Org license admin
7. `O:org-123:*:read` - Org read-only
8. `O:org-123:*:*` - Org admin
9. `R:license:read` - Resource-level access

**First Match Wins** - Stop checking when first permission found.

---

## Error Responses

### Standard Error Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request format",
    "details": "Field 'user_id' is required",
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "req_123456789"
  }
}
```

### Common Error Codes

| Status | Code | Description |
|--------|------|-------------|
| 400 | `VALIDATION_ERROR` | Request validation failed |
| 400 | `INVALID_USER_ID` | Invalid user ID format |
| 400 | `INVALID_SCOPE` | Invalid scope format |
| 400 | `INVALID_RESOURCE_TYPE` | Invalid resource type |
| 400 | `INVALID_ACTION` | Invalid action |
| 400 | `USER_NOT_FOUND` | User does not exist |
| 401 | `AUTHENTICATION_REQUIRED` | Authentication required |
| 403 | `INSUFFICIENT_PERMISSIONS` | Insufficient permissions |
| 500 | `INTERNAL_ERROR` | Internal server error |

---

## Performance Considerations

### Optimization Features

1. **Flattened Permission Keys**: O(1) database lookups
2. **Batch Operations**: Reduce database round trips  
3. **Intelligent Caching**: 5-minute TTL with automatic cleanup
4. **Index Optimization**: Optimized database indexes for fast queries
5. **Early Exit**: Stop hierarchy checking at first match

### Recommended Practices

1. **Use Batch Operations**: When granting multiple permissions
2. **Leverage Wildcards**: Use `*` for broader permissions instead of many specific ones
3. **Monitor Expiration**: Regularly clean expired permissions
4. **Cache Warming**: Use permission preloading for frequently accessed users
5. **Pagination**: Use appropriate page sizes for large permission lists

### Database Indexes

Ensure these indexes exist for optimal performance:

```sql
-- Primary performance indexes  
CREATE INDEX idx_permissions_user_key ON permissions(user_id, permission_key);
CREATE INDEX idx_permissions_key_expires ON permissions(permission_key, expires_at);
CREATE INDEX idx_permissions_scope_pattern ON permissions(permission_key varchar_pattern_ops);
```

---

## Integration Examples

### Frontend Permission Check

```javascript
async function checkUserPermission(userId, scope, resourceType, action, resourceId = null) {
  const response = await fetch('/api/v1/admin/permissions/check', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: userId,
      scope: scope,
      resource_type: resourceType, 
      action: action,
      resource_id: resourceId
    })
  });
  
  const result = await response.json();
  return result.has_permission;
}

// Usage
const canEdit = await checkUserPermission(
  'user-123',
  'O:org-456', 
  'license',
  'update',
  'license-789'
);

if (canEdit) {
  showEditButton();
}
```

### Background Job Permission Setup

```javascript
async function setupNewUserPermissions(userId, organizationId, role) {
  const grants = [];
  
  // Define role-based permissions
  if (role === 'admin') {
    grants.push({
      scope: `O:${organizationId}`,
      resource_type: '*',
      actions: ['*']
    });
  } else if (role === 'manager') {
    grants.push({
      scope: `O:${organizationId}`,
      resource_type: 'license',
      actions: ['read', 'update']
    }, {
      scope: `O:${organizationId}`,
      resource_type: 'product',
      actions: ['read']
    });
  } else if (role === 'viewer') {
    grants.push({
      scope: `O:${organizationId}`,
      resource_type: '*',
      actions: ['read']
    });
  }
  
  // Batch grant permissions
  await fetch('/api/v1/admin/permissions/batch-grant', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user_id: userId,
      reason: `Initial ${role} role setup`,
      grants: grants
    })
  });
}
```

This comprehensive API enables fine-grained permission management with enterprise-grade performance and flexibility.