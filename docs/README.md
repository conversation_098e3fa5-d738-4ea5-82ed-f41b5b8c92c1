# GoKeys API Documentation

This directory contains comprehensive API documentation for the GoKeys License Management Platform.

## Documentation Files

### API Specifications
- **[`openapi.yaml`](openapi.yaml)** - Complete OpenAPI 3.0 specification with all endpoints, schemas, and examples
- **[`swagger.yaml`](swagger.yaml)** - Simplified OpenAPI specification focusing on core license validation endpoints  
- **[`swagger.json`](swagger.json)** - JSON version of the API specification for tooling integration

### Guides and References
- **[`API.md`](API.md)** - Complete API documentation with examples, authentication, and best practices

### Authentication & Authorization
- **[`authentication-authorization.md`](authentication-authorization.md)** - Complete authentication system overview and architecture
- **[`auth-quick-start.md`](auth-quick-start.md)** - Quick start guide for implementing authentication
- **[`api-authentication-reference.md`](api-authentication-reference.md)** - Endpoint-by-endpoint authentication requirements

## Quick Start

### 1. View Interactive Documentation

When the GoKeys server is running, you can access interactive API documentation at:

- **Swagger UI**: `http://localhost:8080/swagger`
- **ReDoc**: `http://localhost:8080/redoc` (if enabled)

### 2. Import into API Tools

You can import the OpenAPI specifications into various API tools:

#### Postman
1. Open Postman
2. Click "Import" 
3. Select "Link" and enter: `http://localhost:8080/api/v1/swagger.json`
4. Or import the local `swagger.json` file

#### Insomnia
1. Open Insomnia
2. Click "Create" → "Import From"
3. Select "URL" and enter: `http://localhost:8080/api/v1/swagger.json`
4. Or import the local `openapi.yaml` file

#### VS Code REST Client
1. Install the "REST Client" extension
2. Create a `.http` file
3. Use the examples from `API.md`

### 3. Generate Client SDKs

You can generate client SDKs using the OpenAPI specification:

```bash
# Generate JavaScript client
npx @openapitools/openapi-generator-cli generate \
  -i docs/openapi.yaml \
  -g javascript \
  -o clients/javascript

# Generate Python client
npx @openapitools/openapi-generator-cli generate \
  -i docs/openapi.yaml \
  -g python \
  -o clients/python

# Generate Go client
npx @openapitools/openapi-generator-cli generate \
  -i docs/openapi.yaml \
  -g go \
  -o clients/go
```

## Documentation Structure

### Core API Endpoints

The GoKeys API is organized into these main areas:

1. **License Management** (`/licenses/*`)
   - License validation and verification
   - License lifecycle management (CRUD)
   - Machine registration and tracking
   - Cache management

2. **Account Management** (`/accounts/*`, `/account`)
   - Organization and tenant management
   - Account creation and configuration
   - User access control

3. **Product Management** (`/products/*`)
   - Software product configuration
   - Distribution strategy management
   - Platform and environment settings

4. **Policy Management** (`/policies/*`)
   - License enforcement rules
   - Machine limits and quotas
   - Cryptographic schemes
   - Heartbeat and check-in requirements

5. **Machine Management** (`/machines/*`)
   - Machine registration and deregistration
   - Heartbeat monitoring
   - Hardware fingerprinting
   - Status tracking

6. **Supporting Services**
   - Entitlements (`/entitlements/*`)
   - Groups (`/groups/*`) 
   - Plans (`/plans/*`)
   - Webhooks (`/webhook-endpoints/*`)
   - Health checks (`/health/*`)

### Authentication Methods

1. **Bearer Token** - JWT tokens for account access
2. **API Key** - Administrative access keys
3. **License Key** - License-based authentication for validation endpoints

### Response Formats

- **Go-style JSON** - Simple, flat object structures
- **JSON:API** - Structured format for webhook endpoints
- **Ruby-compatible** - Maintains compatibility with Ruby Keygen API

## Development Workflow

### 1. API-First Development

1. Update OpenAPI specification first
2. Generate/update client SDKs
3. Implement server endpoints
4. Write integration tests
5. Update documentation

### 2. Testing the API

```bash
# Run integration tests
go test ./internal/adapters/http/handlers/... -v

# Test specific endpoints
curl -X POST http://localhost:8080/api/v1/licenses/validate \
  -H "Content-Type: application/json" \
  -d '{"license_key": "TEST-LICENSE-KEY"}'

# Validate OpenAPI spec
npx swagger-jsdoc -v docs/openapi.yaml
```

### 3. Updating Documentation

When adding new endpoints:

1. Update `openapi.yaml` with new paths and schemas
2. Add examples to `API.md`
3. Regenerate `swagger.json` if needed
4. Test with interactive documentation
5. Update client SDK generation scripts

## API Versioning

- **Current Version**: v1
- **Base Path**: `/api/v1`
- **Versioning Strategy**: URL path versioning
- **Backward Compatibility**: Maintained within major versions

### Version Header Support

```bash
# Optional version header
curl -H "API-Version: v1" \
  https://api.gokeys.com/v1/licenses
```

## Enterprise Features

### Multi-Tenancy
- Account-scoped isolation
- Environment-based segmentation
- Tenant-specific configurations

### Security
- Rate limiting per account/endpoint
- Comprehensive audit logging
- Cryptographic license signing
- Machine fingerprinting

### Monitoring
- Prometheus metrics at `/metrics`
- Health checks for all services
- Event streaming and webhooks
- Performance monitoring

### Compliance
- SOC 2 Type II compliant architecture
- GDPR data protection features
- Audit trail for all operations
- Data retention policies

## Support and Contributing

### Getting Help
- Check the [API documentation](API.md) first
- Review [example requests](API.md#quick-start-examples)
- Open an issue on GitHub
- Contact <EMAIL>

### Contributing to Documentation
1. Fork the repository
2. Update the relevant documentation files
3. Test with the OpenAPI validator
4. Submit a pull request

### Documentation Standards
- Use OpenAPI 3.0.3 specification
- Include realistic examples
- Provide clear descriptions
- Test all examples before submitting
- Follow consistent naming conventions

---

For questions about the API or documentation, please reach out to the development team or check the main project README.