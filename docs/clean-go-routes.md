# Simple Go Routes Implementation with Gin

This document describes the simple and clean Go implementation of API routes using Gin's native features without over-engineering.

## 🚀 Simple Gin Implementation

### 1. **Constants for Consistency**

```go
// API route constants for better maintainability
const (
    // API versions
    APIVersionV1 = "/api/v1"
    PublicAPIV1  = "/api/v1/public"

    // Route groups
    AdminGroup         = "/admin"
    OrganizationsGroup = "/organizations"
    UsersGroup         = "/users"
    ProductsGroup      = "/products"
    LicensesGroup      = "/licenses"
    MachinesGroup      = "/machines"
    PoliciesGroup      = "/policies"
    AuthGroup          = "/auth"
    ActionsGroup       = "/actions"

    // Parameter names
    ParamOrganizationID = "organization_id"
    ParamProductID      = "product_id"
    ParamLicenseID      = "license_id"
    ParamUserID         = "user_id"
    ParamMachineID      = "machine_id"
    ParamPolicyID       = "policy_id"
    ParamKey            = "key"

    // System user ID for bypass authentication
    SystemAdminUserID = "eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee"
)

// HTTP methods for route definitions
const (
    GET    = http.MethodGet
    POST   = http.MethodPost
    PUT    = http.MethodPut
    DELETE = http.MethodDelete
    PATCH  = http.MethodPatch
)
```

**Benefits:**

- ✅ **Compile-time safety** - typos caught at build time
- ✅ **Consistency** - same route patterns across codebase
- ✅ **Maintainability** - single source of truth for route definitions
- ✅ **Refactoring safety** - IDE can find all usages

### 2. **Direct Gin Route Groups**

```go
// Simple and direct using Gin's native features
func (r *APIRoutes) setupSystemAdminRoutes(api *gin.RouterGroup) {
    admin := api.Group("/admin")

    // Organization management (system admin only)
    admin.GET("/organizations", r.orgHandler.ListAllOrganizations)
    admin.POST("/organizations", r.orgHandler.CreateOrganization)

    // User management across organizations
    admin.GET("/users", func(c *gin.Context) {
        c.JSON(200, gin.H{"message": "list all users (system admin)"})
    })

    // System-wide statistics and monitoring
    admin.GET("/stats", r.orgHandler.GetSystemStats)
}
```

**Benefits:**

- ✅ **Simple** - uses Gin's native Group() method
- ✅ **Direct** - no abstraction layers
- ✅ **Readable** - clear and straightforward
- ✅ **No Over-engineering** - leverages what Gin already provides

### 3. **Nested Route Groups**

```go
// setupOrganizationRoutes sets up organization-scoped routes
func (r *APIRoutes) setupOrganizationRoutes(api *gin.RouterGroup) {
    orgs := api.Group("/organizations/:organization_id")

    // Organization info and management
    orgs.GET("/", r.orgHandler.GetOrganization)
    orgs.PUT("/", r.orgHandler.UpdateOrganization)

    // Organization users
    r.setupOrganizationUserRoutes(orgs)

    // Organization products
    r.setupOrganizationProductRoutes(orgs)
}

// setupOrganizationUserRoutes sets up user management within organization
func (r *APIRoutes) setupOrganizationUserRoutes(orgs *gin.RouterGroup) {
    users := orgs.Group("/users")

    // List and add organization users
    users.GET("/", r.userHandler.ListOrganizationUsers)
    users.POST("/", r.userHandler.AddUserToOrganization)

    // Individual user management
    user := users.Group("/:user_id")
    user.GET("/", r.userHandler.GetOrganizationUser)
    user.DELETE("/", r.userHandler.RemoveUserFromOrganization)
}
```

**Benefits:**

- ✅ **Natural Nesting** - uses Gin's Group() method
- ✅ **Clear Hierarchy** - mirrors REST API structure
- ✅ **Middleware Inheritance** - parent middlewares apply to children
- ✅ **Simple Composition** - easy to understand and maintain

### 4. **License Action Routes (Keygen-API Compatible)**

```go
// setupLicenseActionRoutes sets up license action routes following keygen-api patterns
func (r *APIRoutes) setupLicenseActionRoutes(license *gin.RouterGroup) {
    actions := license.Group("/actions")

    // License validation actions
    actions.GET("/validate", r.licenseHandler.QuickValidateLicenseByID)
    actions.POST("/validate", r.licenseHandler.ValidateLicenseByKey)

    // License permit actions (following keygen-api permits_controller.rb)
    actions.POST("/suspend", r.licenseHandler.SuspendLicense)
    actions.POST("/reinstate", r.licenseHandler.ReinstateLicense)
    actions.DELETE("/revoke", r.licenseHandler.RevokeLicense)
    actions.POST("/renew", r.licenseHandler.RenewLicense)
    actions.POST("/check-in", r.licenseHandler.CheckInLicense)

    // License usage actions (following keygen-api uses_controller.rb)
    actions.POST("/increment-usage", r.licenseHandler.IncrementUsage)
    actions.POST("/decrement-usage", r.licenseHandler.DecrementUsage)
    actions.POST("/reset-usage", r.licenseHandler.ResetUsage)

    // License checkout actions
    actions.POST("/check-out", r.licenseHandler.CheckoutLicense)
    actions.GET("/check-out", r.licenseHandler.CheckoutLicense)
}
```

**Benefits:**

- ✅ **Keygen-API Compatible** - follows exact same patterns
- ✅ **Action-Based Grouping** - logical organization
- ✅ **RESTful Design** - proper HTTP methods
- ✅ **Clear Intent** - self-documenting route names

### 5. **Middleware Composition**

```go
// withAuth creates a middleware chain with authentication
func (r *APIRoutes) withAuth() []gin.HandlerFunc {
    return []gin.HandlerFunc{
        r.authMW.RequireAuth(),
        r.authzMW.SetOrganizationContext(),
    }
}

// withNoAuth creates a middleware chain without authentication (for testing)
func (r *APIRoutes) withNoAuth() []gin.HandlerFunc {
    return []gin.HandlerFunc{r.noAuthRequired()}
}
```

**Benefits:**

- ✅ **Reusability** - middleware chains can be reused
- ✅ **Composability** - easy to combine different middlewares
- ✅ **Testing Support** - easy to switch between auth/no-auth
- ✅ **Clean Code** - middleware logic separated from route definitions

### 6. **Recursive Route Application**

```go
// applyRouteGroup applies a route group to a router group recursively
func (r *APIRoutes) applyRouteGroup(parent gin.IRouter, routeGroup RouteGroup) {
    group := parent.Group(routeGroup.Prefix, routeGroup.Middlewares...)

    // Apply routes in this group
    r.applyRoutes(group, routeGroup.Routes)

    // Apply sub-groups recursively
    for _, subGroup := range routeGroup.SubGroups {
        r.applyRouteGroup(group, subGroup)
    }
}
```

**Benefits:**

- ✅ **Recursive Processing** - handles nested route groups automatically
- ✅ **Middleware Inheritance** - parent middlewares apply to all children
- ✅ **Clean Separation** - route definition separate from application
- ✅ **Flexibility** - can apply same route group to different routers

## 🎯 **Route Structure Overview**

The clean implementation creates a hierarchical route structure:

```
/api/v1/public (no auth)
├── /auth/login
├── /auth/register
└── /licenses/validate

/api/v1 (with auth)
├── /admin
│   ├── /organizations
│   ├── /users
│   └── /stats
├── /organizations/:organization_id
│   ├── GET    /
│   ├── PUT    /
│   ├── /users
│   │   ├── GET    /
│   │   ├── POST   /
│   │   └── /:user_id
│   │       ├── GET    /
│   │       ├── DELETE /
│   │       └── /permissions
│   └── /products
│       ├── GET    /
│       ├── POST   /
│       └── /:product_id
│           ├── GET    /
│           ├── PUT    /
│           ├── DELETE /
│           ├── /policies
│           │   ├── GET    /
│           │   ├── POST   /
│           │   └── /:policy_id
│           └── /licenses
│               ├── GET    /
│               ├── POST   /
│               └── /:license_id
│                   ├── GET    /
│                   ├── PUT    /
│                   ├── DELETE /
│                   ├── /actions
│                   │   ├── GET    /validate
│                   │   ├── POST   /validate
│                   │   ├── POST   /suspend
│                   │   ├── POST   /reinstate
│                   │   ├── DELETE /revoke
│                   │   ├── POST   /renew
│                   │   ├── POST   /check-in
│                   │   ├── POST   /increment-usage
│                   │   ├── POST   /decrement-usage
│                   │   ├── POST   /reset-usage
│                   │   └── POST   /check-out
│                   └── /machines
│                       ├── GET    /
│                       ├── POST   /
│                       └── /:machine_id
├── /users
│   ├── GET  /me
│   ├── PUT  /me
│   ├── GET  /me/organizations
│   ├── GET  /me/permissions
│   └── /auth
│       ├── POST /logout
│       └── POST /refresh
├── /licenses
│   ├── /actions
│   │   └── POST /validate-key
│   └── /:key
│       ├── POST /validate
│       └── POST /machines
└── /machines
    ├── POST /:machine_id/heartbeat
    └── /organizations/:organization_id/machines
        ├── GET  /
        ├── POST /
        └── /:machine_id
            ├── GET    /
            ├── PUT    /
            ├── DELETE /
            ├── POST   /heartbeat
            ├── GET    /processes
            └── GET    /components
```

## 🏆 **Simple Gin Best Practices**

### 1. **Use Gin's Native Features**

- ✅ Direct use of `router.Group()` method
- ✅ No custom abstractions over Gin
- ✅ Leverage what Gin already provides well

### 2. **Clear Route Organization**

- ✅ One setup method per domain (admin, org, user, etc.)
- ✅ Nested groups mirror REST API hierarchy
- ✅ Consistent naming patterns

### 3. **Constants for Consistency**

- ✅ API versions and route groups as constants
- ✅ Parameter names standardized
- ✅ HTTP methods as constants

### 4. **Simple Middleware**

- ✅ Direct middleware application with `Use()`
- ✅ No complex middleware composition
- ✅ Easy auth bypass for testing

### 5. **Maintainability**

- ✅ Easy to add new routes
- ✅ Clear separation by domain
- ✅ Self-documenting structure

## 🚀 **Results**

- **Simple & Direct** - Uses Gin's native features without over-engineering
- **Maintainable** - Easy to understand and modify
- **Consistent** - Same patterns across all routes
- **Readable** - Clear route hierarchy
- **No Abstractions** - Direct Gin usage as intended
- **Fast Development** - No custom frameworks to learn

**Key Insight:** Gin already provides excellent route grouping and middleware features. Don't reinvent the wheel - use what's already there!
