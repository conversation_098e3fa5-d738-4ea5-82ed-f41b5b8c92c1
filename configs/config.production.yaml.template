server:
  host: "0.0.0.0"
  port: 8080
  environment: "production"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

database:
  host: "${DATABASE_HOST}"
  port: ${DATABASE_PORT}
  user: "${DATABASE_USER}"
  password: "${DATABASE_PASSWORD}"
  dbname: "${DATABASE_NAME}"
  sslmode: "require"
  max_idle_conns: 25
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  address: "${REDIS_ADDRESS}"
  password: "${REDIS_PASSWORD}"
  db: 0
  pool_size: 50
  min_idle_conn: 10

security:
  # JWT Configuration - Use environment variables in production
  jwt_secret: "${JWT_SECRET}"
  jwt_public_key: "${JWT_PUBLIC_KEY}"
  jwt_private_key: "${JWT_PRIVATE_KEY}"
  jwt_expiration: 3600
  jwt_algorithm: "RS256"  # Use RSA in production
  
  # Token Configuration
  token_secret: "${TOKEN_SECRET}"
  token_prefix: "Bearer "
  
  # Production Mode
  skip_auth_verification: false
  
  # Password Configuration
  bcrypt_cost: 12
  
  # Rate Limiting
  rate_limit_enabled: true
  rate_limit_requests: 100
  rate_limit_window: 60
  
  # CORS
  cors_enabled: true
  cors_origins: ["https://yourapp.com", "https://api.yourapp.com"]

metrics:
  enabled: true
  port: 9090
  path: "/metrics"
  victoria_metrics:
    enabled: true
    endpoint: "${VICTORIA_METRICS_ENDPOINT}"
    username: "${VICTORIA_METRICS_USERNAME}"
    password: "${VICTORIA_METRICS_PASSWORD}"

nats:
  url: "${NATS_URL}"
  cluster_id: "${NATS_CLUSTER_ID}"
  client_id: "${NATS_CLIENT_ID}"
  max_reconnects: 10
  reconnect_delay: 2
  connection_name: "GoKeys-Prod"

logging:
  level: "info"
  format: "json"
  output: "stdout"
  max_size: 100
  max_backups: 10
  max_age: 30
  compress: true