server:
  host: "0.0.0.0"
  port: 8080
  environment: "development"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

database:
  host: "localhost"
  port: 5432
  user: "gokeys"
  password: "gokeys"
  dbname: "gokeys"
  sslmode: "disable"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  address: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conn: 5

security:
  # JWT Configuration
  jwt_secret: "dev-jwt-secret-key-change-in-production"
  jwt_public_key: ""  # For development, we use symmetric keys
  jwt_private_key: ""
  jwt_expiration: 3600
  jwt_algorithm: "HS256"
  
  # Token Configuration
  token_secret: "dev-token-secret-key-change-in-production"
  token_prefix: "Bearer "
  
  # Development Mode
  skip_auth_verification: false  # Even in dev, we should test auth
  
  # Password Configuration
  bcrypt_cost: 10  # Lower cost for development
  
  # Rate Limiting
  rate_limit_enabled: true
  rate_limit_requests: 1000  # Higher limit for development
  rate_limit_window: 60
  
  # CORS
  cors_enabled: true
  cors_origins: ["*"]

metrics:
  enabled: true
  port: 9090
  path: "/metrics"
  victoria_metrics:
    enabled: false
    endpoint: ""
    username: ""
    password: ""

nats:
  url: "nats://localhost:4222"
  cluster_id: "gokeys-dev-cluster"
  client_id: "gokeys-dev-client"
  max_reconnects: 10
  reconnect_delay: 2
  connection_name: "GoKeys-Dev"

logging:
  level: "debug"
  format: "json"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true