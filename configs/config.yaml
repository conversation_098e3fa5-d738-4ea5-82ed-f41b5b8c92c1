server:
  host: "0.0.0.0"
  port: 8080
  environment: "development"
  read_timeout: 30
  write_timeout: 30
  idle_timeout: 120

database:
  host: "localhost"
  port: 5432
  user: "gokeys"
  password: "gokeys"
  dbname: "gokeys"
  sslmode: "disable"
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600

redis:
  address: "localhost:6379"
  password: "gokeys"
  db: 0
  pool_size: 10
  min_idle_conn: 5

metrics:
  enabled: true
  port: 9090
  path: "/metrics"
  victoria_metrics:
    enabled: false
    endpoint: ""
    username: ""
    password: ""

nats:
  url: "nats://localhost:4222"
  cluster_id: "gokeys-cluster"
  client_id: "gokeys-client"
  max_reconnects: 10
  reconnect_delay: 2
  connection_name: "GoKeys"

security:
  jwt_secret: "development-secret-change-in-production"
  jwt_expiration: 3600
  jwt_algorithm: "RS256"
  jwt_public_key: |
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkhYRhQMEk0xOffJyE0SM
    v/qAYfwgkB2FCuDuqYFk1AI54cfaUpK4B+RfOct1aqucbWOtt8n5Gsx01fBvv9PZ
    EUc65rSRx23Q+QlVCXIUOc/KZ32qvHHPqcjh3Xs28V2aawWu7OKBJu6SI0GKn0vd
    Oz143UM4LNrWj1+f6nD05Zo6h9MMpxivLXWioOaHE2tj2z5+RwsJHru7Ka5oTTIr
    WQLynvoeUTciDb+9388vPvrbxZ7oanLuMOLj2torJK715jWKHm390+Cyl1ygnGWu
    Vgjzwpw/B0X4wrRul68RmdC09P+obhVluQ0kOWfPzy2ASLGtB5eFn9zGboJKhcyR
    YQIDAQAB
    -----END PUBLIC KEY-----
  jwt_private_key: |
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  bcrypt_cost: 12
  rate_limit_enabled: true
  rate_limit_requests: 100
  rate_limit_window: 60
  cors_enabled: true
  cors_origins:
    - "*"

opa:
  enabled: true
  policy_path: "policies"
  data_path: "policies/data"
  cache_enabled: true
  cache_ttl: 300

logging:
  level: "info"
  format: "json"
  output: "stdout"
  max_size: 100
  max_backups: 3
  max_age: 28
  compress: true