package main

import (
	"flag"
	"fmt"
	"log"

	"github.com/gokeys/gokeys/internal/adapters/database/postgres"
	"github.com/gokeys/gokeys/internal/config"
)

func main() {
	var (
		up            = flag.Bool("up", false, "Run migrations up")
		down          = flag.Bool("down", false, "Run migrations down")
		rollback      = flag.Int("rollback", 0, "Rollback N migrations")
		status        = flag.Bool("status", false, "Show migration status")
		extensions    = flag.Bool("extensions", false, "Create PostgreSQL extensions")
		migrationsDir = flag.String("migrations-dir", "./internal/adapters/database/migrations", "Migrations directory")
		_             = flag.String("config", "", "Configuration file path (optional, currently unused)")
	)
	flag.Parse()

	// Load configuration using the existing config system
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Show help if no flags provided
	if !*extensions && !*up && !*down && *rollback == 0 && !*status {
		showHelp(cfg)
		return
	}

	// Convert config.DatabaseConfig to postgres.Config
	dbConfig := postgres.Config{
		Host:            cfg.Database.Host,
		Port:            cfg.Database.Port,
		User:            cfg.Database.User,
		Password:        cfg.Database.Password,
		DBName:          cfg.Database.DBName,
		SSLMode:         cfg.Database.SSLMode,
		MaxIdleConns:    cfg.Database.MaxIdleConns,
		MaxOpenConns:    cfg.Database.MaxOpenConns,
		ConnMaxLifetime: cfg.Database.ConnMaxLifetime,
	}

	// Connect to database using the existing connection system
	db, err := postgres.NewConnection(dbConfig)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer func() {
		if err := postgres.Close(db); err != nil {
			log.Printf("Error closing database connection: %v", err)
		}
	}()

	// Create migrator
	migrator := postgres.NewMigrator(db, *migrationsDir)

	// Execute requested operation
	switch {
	case *extensions:
		if err := migrator.CreateExtensions(); err != nil {
			log.Fatalf("Failed to create extensions: %v", err)
		}
		fmt.Println("✅ PostgreSQL extensions created successfully")

	case *up:
		if err := migrator.RunMigrations(); err != nil {
			log.Fatalf("Failed to run migrations: %v", err)
		}
		fmt.Println("✅ Migrations completed successfully")

	case *down:
		if err := migrator.RollbackMigrations(1); err != nil {
			log.Fatalf("Failed to rollback migrations: %v", err)
		}
		fmt.Println("✅ Rollback completed successfully")

	case *rollback > 0:
		if err := migrator.RollbackMigrations(*rollback); err != nil {
			log.Fatalf("Failed to rollback migrations: %v", err)
		}
		fmt.Printf("✅ Rolled back %d migrations successfully\n", *rollback)

	case *status:
		version, dirty, err := migrator.GetMigrationStatus()
		if err != nil {
			log.Fatalf("Failed to get migration status: %v", err)
		}
		fmt.Printf("Current migration version: %d\n", version)
		fmt.Printf("Database is dirty: %t\n", dirty)
	}
}

func showHelp(cfg *config.Config) {
	fmt.Println("GoKeys Database Migration Tool")
	fmt.Println("==============================")
	fmt.Println()
	fmt.Println("Usage:")
	fmt.Println("  go run cmd/migrate/main.go [flags]")
	fmt.Println()
	fmt.Println("Flags:")
	fmt.Println("  -extensions              Create PostgreSQL extensions")
	fmt.Println("  -up                      Run all pending migrations")
	fmt.Println("  -down                    Rollback last migration")
	fmt.Println("  -rollback=N              Rollback N migrations")
	fmt.Println("  -status                  Show current migration status")
	fmt.Println("  -migrations-dir=DIR      Migrations directory (default: ./internal/adapters/database/migrations)")
	fmt.Println("  -config=FILE             Configuration file path (optional, currently unused)")
	fmt.Println()
	fmt.Println("Configuration:")
	fmt.Println("  The tool uses the same configuration system as the main application.")
	fmt.Println("  Configuration can be provided via:")
	fmt.Println("    1. YAML config file (config.yaml in current dir or ./configs/)")
	fmt.Println("    2. Environment variables (DATABASE_HOST, DATABASE_PORT, etc.)")
	fmt.Println("  Environment variables override YAML config values.")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  # Create extensions (run once)")
	fmt.Println("  go run cmd/migrate/main.go -extensions")
	fmt.Println()
	fmt.Println("  # Run all migrations")
	fmt.Println("  go run cmd/migrate/main.go -up")
	fmt.Println()
	fmt.Println("  # Check status")
	fmt.Println("  go run cmd/migrate/main.go -status")
	fmt.Println()
	fmt.Println("  # Rollback 3 migrations")
	fmt.Println("  go run cmd/migrate/main.go -rollback=3")
	fmt.Println()
	fmt.Printf("Current config: %s:%d/%s (user: %s)\n", 
		cfg.Database.Host, cfg.Database.Port, cfg.Database.DBName, cfg.Database.User)
}