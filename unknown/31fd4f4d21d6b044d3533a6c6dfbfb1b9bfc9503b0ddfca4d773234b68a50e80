package crypto

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWTService provides JWT token operations with RS256 support using jwt/v5
type JWTService struct{}

// NewJWTService creates a new JWT service instance
func NewJWTService() *JWTService {
	return &JWTService{}
}

// TokenOptions represents options for token creation
type TokenOptions struct {
	Algorithm    string
	KeyID        string
	ExpiresIn    time.Duration
	NotBefore    time.Time
	Issuer       string
	Subject      string
	Audience     []string
	JWTID        string
	CustomClaims map[string]interface{}
}

// CreateToken creates a new JWT token using the specified algorithm and key
func (js *JWTService) CreateToken(privateKeyPEM string, opts TokenOptions) (string, error) {
	// Parse private key
	privateKey, err := parseRSAPrivateKey(privateKeyPEM)
	if err != nil {
		return "", fmt.Errorf("failed to parse private key: %w", err)
	}

	// Create claims
	claims := jwt.MapClaims{
		"iss": opts.Issuer,
		"sub": opts.Subject,
		"iat": time.Now().Unix(),
	}

	// Set expiration
	if opts.ExpiresIn > 0 {
		claims["exp"] = time.Now().Add(opts.ExpiresIn).Unix()
	}

	// Set not before
	if !opts.NotBefore.IsZero() {
		claims["nbf"] = opts.NotBefore.Unix()
	}

	// Set audience
	if len(opts.Audience) > 0 {
		claims["aud"] = opts.Audience
	}

	// Set JWT ID
	if opts.JWTID != "" {
		claims["jti"] = opts.JWTID
	}

	// Add custom claims
	for k, v := range opts.CustomClaims {
		claims[k] = v
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodRS256, claims)

	// Set key ID in header if provided
	if opts.KeyID != "" {
		token.Header["kid"] = opts.KeyID
	}

	// Sign token
	tokenString, err := token.SignedString(privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// VerifyToken verifies a JWT token and returns the claims
func (js *JWTService) VerifyToken(tokenString, publicKeyPEM string) (*jwt.MapClaims, map[string]interface{}, error) {
	// Parse public key
	publicKey, err := parseRSAPublicKey(publicKeyPEM)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	// Parse and verify token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return publicKey, nil
	})

	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Validate token
	if !token.Valid {
		return nil, nil, fmt.Errorf("token is not valid")
	}

	// Extract claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, nil, fmt.Errorf("failed to extract claims")
	}

	// Convert to interface{} map for compatibility
	allClaims := make(map[string]interface{})
	for k, v := range claims {
		allClaims[k] = v
	}

	return &claims, allClaims, nil
}

// ParseTokenWithoutVerification parses a JWT token without verifying the signature
func (js *JWTService) ParseTokenWithoutVerification(tokenString string) (map[string]interface{}, map[string]interface{}, error) {
	// Parse token without verification
	token, _, err := new(jwt.Parser).ParseUnverified(tokenString, jwt.MapClaims{})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Extract header
	header := make(map[string]interface{})
	for k, v := range token.Header {
		header[k] = v
	}

	// Extract claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, nil, fmt.Errorf("failed to extract claims")
	}

	allClaims := make(map[string]interface{})
	for k, v := range claims {
		allClaims[k] = v
	}

	return header, allClaims, nil
}

// ValidateTokenStructure validates the basic structure of a JWT token
func (js *JWTService) ValidateTokenStructure(tokenString string) error {
	_, _, err := js.ParseTokenWithoutVerification(tokenString)
	return err
}

// GetTokenInfo returns information about a JWT token without verification
func (js *JWTService) GetTokenInfo(tokenString string) (map[string]interface{}, error) {
	header, claims, err := js.ParseTokenWithoutVerification(tokenString)
	if err != nil {
		return nil, err
	}

	info := map[string]interface{}{
		"algorithm":  header["alg"],
		"type":       header["typ"],
		"key_id":     header["kid"],
		"issued_at":  claims["iat"],
		"expires_at": claims["exp"],
		"subject":    claims["sub"],
		"issuer":     claims["iss"],
	}

	// Check if token is expired
	if exp, ok := claims["exp"].(float64); ok {
		if time.Now().Unix() > int64(exp) {
			info["expired"] = true
		} else {
			info["expired"] = false
		}
	}

	return info, nil
}

// RefreshToken creates a new token with updated expiration using the same claims
func (js *JWTService) RefreshToken(tokenString, privateKeyPEM string, newExpiresIn time.Duration) (string, error) {
	header, claims, err := js.ParseTokenWithoutVerification(tokenString)
	if err != nil {
		return "", err
	}

	// Extract standard claims
	opts := TokenOptions{
		ExpiresIn:    newExpiresIn,
		CustomClaims: make(map[string]interface{}),
	}

	if alg, ok := header["alg"].(string); ok {
		opts.Algorithm = alg
	}
	if kid, ok := header["kid"].(string); ok {
		opts.KeyID = kid
	}
	if iss, ok := claims["iss"].(string); ok {
		opts.Issuer = iss
	}
	if sub, ok := claims["sub"].(string); ok {
		opts.Subject = sub
	}
	if jti, ok := claims["jti"].(string); ok {
		opts.JWTID = jti
	}
	if aud, ok := claims["aud"].([]interface{}); ok {
		for _, a := range aud {
			if audStr, ok := a.(string); ok {
				opts.Audience = append(opts.Audience, audStr)
			}
		}
	}

	// Copy custom claims (excluding standard ones)
	standardClaims := map[string]bool{
		"iss": true, "sub": true, "aud": true, "exp": true,
		"nbf": true, "iat": true, "jti": true,
	}

	for k, v := range claims {
		if !standardClaims[k] {
			opts.CustomClaims[k] = v
		}
	}

	return js.CreateToken(privateKeyPEM, opts)
}

// CreateSimpleToken creates a simple JWT token with minimal claims
func (js *JWTService) CreateSimpleToken(privateKeyPEM, algorithm, subject string, expiresIn time.Duration) (string, error) {
	opts := TokenOptions{
		Algorithm: algorithm,
		Subject:   subject,
		ExpiresIn: expiresIn,
		Issuer:    "gokeys",
	}

	return js.CreateToken(privateKeyPEM, opts)
}

// VerifyAndExtractClaims verifies a token and extracts specific claims
func (js *JWTService) VerifyAndExtractClaims(tokenString, publicKeyPEM string, claimKeys []string) (map[string]interface{}, error) {
	_, allClaims, err := js.VerifyToken(tokenString, publicKeyPEM)
	if err != nil {
		return nil, err
	}

	result := make(map[string]interface{})
	for _, key := range claimKeys {
		if value, exists := allClaims[key]; exists {
			result[key] = value
		}
	}

	return result, nil
}

// Helper functions for key parsing

func parseRSAPrivateKey(privateKeyPEM string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	// Try PKCS#1 format first
	if privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes); err == nil {
		return privateKey, nil
	}

	// Try PKCS#8 format
	privateKeyInterface, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	privateKey, ok := privateKeyInterface.(*rsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA private key")
	}

	return privateKey, nil
}

func parseRSAPublicKey(publicKeyPEM string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block")
	}

	// Try PKIX format
	publicKeyInterface, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	publicKey, ok := publicKeyInterface.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}

	return publicKey, nil
}

// ExtractPublicKeyForJWT extracts public key from RSA private key for JWT
func (js *JWTService) ExtractPublicKeyForJWT(privateKeyPEM, algorithm string) (string, error) {
	if algorithm != "RS256" {
		return "", fmt.Errorf("unsupported algorithm: %s", algorithm)
	}

	privateKey, err := parseRSAPrivateKey(privateKeyPEM)
	if err != nil {
		return "", err
	}

	// Extract public key
	publicKey := &privateKey.PublicKey

	// Marshal to PKIX format
	publicKeyDER, err := x509.MarshalPKIXPublicKey(publicKey)
	if err != nil {
		return "", fmt.Errorf("failed to marshal public key: %w", err)
	}

	// Encode to PEM
	publicKeyPEM := pem.EncodeToMemory(&pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: publicKeyDER,
	})

	return string(publicKeyPEM), nil
}