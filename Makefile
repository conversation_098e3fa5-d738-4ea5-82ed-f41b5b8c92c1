# GoKeys License Management Platform - Makefile
# Development and deployment automation

.PHONY: help build run test clean docker-build docker-run docker-dev docker-stop deps lint fmt vet security migrate-up migrate-down

# Default target
.DEFAULT_GOAL := help

# Colors for output
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# Application variables
APP_NAME := gokeys
VERSION := $(shell git describe --tags --always --dirty)
BUILD_TIME := $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")
COMMIT := $(shell git rev-parse HEAD)
LDFLAGS := -X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.commit=$(COMMIT)

# Docker variables
DOCKER_IMAGE := $(APP_NAME)
DOCKER_TAG := $(VERSION)

help: ## Display this help message
	@echo "$(GREEN)GoKeys License Management Platform$(NC)"
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}'

# =================
# DEVELOPMENT
# =================

deps: ## Install dependencies
	@echo "$(GREEN)Installing dependencies...$(NC)"
	go mod download
	go mod verify
	go mod tidy

build: ## Build the application
	@echo "$(GREEN)Building $(APP_NAME)...$(NC)"
	CGO_ENABLED=1 go build -ldflags="$(LDFLAGS)" -o bin/$(APP_NAME) ./cmd/server/main.go

run: ## Run the application
	@echo "$(GREEN)Running $(APP_NAME)...$(NC)"
	go run ./cmd/server/main.go

run-dev: ## Run the application with hot reload
	@echo "$(GREEN)Running $(APP_NAME) with hot reload...$(NC)"
	air -c .air.toml

test: ## Run tests
	@echo "$(GREEN)Running tests...$(NC)"
	go test -v -race -coverprofile=coverage.out ./...

test-coverage: test ## Run tests with coverage report
	@echo "$(GREEN)Generating coverage report...$(NC)"
	go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

lint: ## Run linter
	@echo "$(GREEN)Running linter...$(NC)"
	golangci-lint run

fmt: ## Format code
	@echo "$(GREEN)Formatting code...$(NC)"
	go fmt ./...

vet: ## Run go vet
	@echo "$(GREEN)Running go vet...$(NC)"
	go vet ./...

security: ## Run security scan
	@echo "$(GREEN)Running security scan...$(NC)"
	gosec ./...

# =================
# DATABASE
# =================

migrate-up: ## Run database migrations
	@echo "$(GREEN)Running database migrations...$(NC)"
	migrate -database "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_dev?sslmode=disable" -path internal/adapters/database/migrations up

migrate-down: ## Rollback database migrations
	@echo "$(YELLOW)Rolling back database migrations...$(NC)"
	migrate -database "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_dev?sslmode=disable" -path internal/adapters/database/migrations down

migrate-create: ## Create new migration file (usage: make migrate-create NAME=migration_name)
	@echo "$(GREEN)Creating migration: $(NAME)$(NC)"
	migrate create -ext sql -dir internal/adapters/database/migrations -seq $(NAME)

db-reset: ## Reset database (drop and recreate)
	@echo "$(YELLOW)Resetting database...$(NC)"
	dropdb --if-exists gokeys_dev
	createdb gokeys_dev
	$(MAKE) migrate-up

# =================
# DOCKER
# =================

docker-build: ## Build Docker image
	@echo "$(GREEN)Building Docker image...$(NC)"
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .
	docker tag $(DOCKER_IMAGE):$(DOCKER_TAG) $(DOCKER_IMAGE):latest

docker-build-dev: ## Build Docker development image
	@echo "$(GREEN)Building Docker development image...$(NC)"
	docker build -f Dockerfile.dev -t $(DOCKER_IMAGE):dev .

docker-run: ## Run Docker container
	@echo "$(GREEN)Running Docker container...$(NC)"
	docker run -d --name $(APP_NAME) -p 8080:8080 -p 9090:9090 $(DOCKER_IMAGE):$(DOCKER_TAG)

docker-dev: ## Run development environment with Docker Compose
	@echo "$(GREEN)Starting development environment...$(NC)"
	docker-compose -f docker-compose.dev.yml up -d

docker-prod: ## Run production environment with Docker Compose
	@echo "$(GREEN)Starting production environment...$(NC)"
	docker-compose up -d

docker-stop: ## Stop Docker containers
	@echo "$(YELLOW)Stopping Docker containers...$(NC)"
	docker-compose down
	docker-compose -f docker-compose.dev.yml down

docker-logs: ## View Docker logs
	@echo "$(GREEN)Viewing Docker logs...$(NC)"
	docker-compose logs -f

docker-clean: ## Clean Docker resources
	@echo "$(YELLOW)Cleaning Docker resources...$(NC)"
	docker system prune -f
	docker volume prune -f

# =================
# UTILITIES
# =================

clean: ## Clean build artifacts
	@echo "$(GREEN)Cleaning build artifacts...$(NC)"
	rm -rf bin/
	rm -rf tmp/
	rm -f coverage.out
	rm -f coverage.html

gen-docs: ## Generate API documentation
	@echo "$(GREEN)Generating API documentation...$(NC)"
	@if ! command -v swag >/dev/null 2>&1; then \
		echo "$(YELLOW)swag not found in PATH, using GOPATH...$(NC)"; \
		$(shell go env GOPATH)/bin/swag init -g cmd/server/main.go -o docs --parseDependency --parseInternal; \
	else \
		swag init -g cmd/server/main.go -o docs --parseDependency --parseInternal; \
	fi

gen-mocks: ## Generate mocks for testing
	@echo "$(GREEN)Generating mocks...$(NC)"
	mockery --all --output=mocks --case=underscore

install-tools: ## Install development tools
	@echo "$(GREEN)Installing development tools...$(NC)"
	go install github.com/air-verse/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/swaggo/swag/cmd/swag@latest
	go install github.com/vektra/mockery/v2@latest
	go install github.com/securecodewarrior/sast-scan@latest

# =================
# PRODUCTION
# =================

deploy-staging: ## Deploy to staging environment
	@echo "$(GREEN)Deploying to staging...$(NC)"
	# Add your staging deployment commands here
	@echo "$(YELLOW)Staging deployment not implemented yet$(NC)"

deploy-prod: ## Deploy to production environment
	@echo "$(GREEN)Deploying to production...$(NC)"
	# Add your production deployment commands here
	@echo "$(YELLOW)Production deployment not implemented yet$(NC)"

backup-db: ## Backup database
	@echo "$(GREEN)Backing up database...$(NC)"
	pg_dump -h localhost -U gokeys -d gokeys_dev > backup_$(shell date +%Y%m%d_%H%M%S).sql

restore-db: ## Restore database from backup (usage: make restore-db FILE=backup.sql)
	@echo "$(GREEN)Restoring database from $(FILE)...$(NC)"
	psql -h localhost -U gokeys -d gokeys_dev < $(FILE)

# =================
# MONITORING
# =================

health-check: ## Check application health
	@echo "$(GREEN)Checking application health...$(NC)"
	curl -f http://localhost:8080/health || echo "$(RED)Health check failed$(NC)"

metrics: ## View application metrics
	@echo "$(GREEN)Viewing application metrics...$(NC)"
	curl -s http://localhost:9090/metrics | head -20

# =================
# PERFORMANCE
# =================

benchmark: ## Run benchmarks
	@echo "$(GREEN)Running benchmarks...$(NC)"
	go test -bench=. -benchmem ./...

profile-cpu: ## Profile CPU usage
	@echo "$(GREEN)Profiling CPU usage...$(NC)"
	go test -cpuprofile=cpu.prof -bench=. ./...
	go tool pprof cpu.prof

profile-mem: ## Profile memory usage
	@echo "$(GREEN)Profiling memory usage...$(NC)"
	go test -memprofile=mem.prof -bench=. ./...
	go tool pprof mem.prof

# =================
# RELEASE
# =================

release: ## Create a new release
	@echo "$(GREEN)Creating release...$(NC)"
	@echo "Current version: $(VERSION)"
	@echo "$(YELLOW)Manual release process - tag and push to trigger CI/CD$(NC)"

tag: ## Create a new git tag (usage: make tag VERSION=v1.0.0)
	@echo "$(GREEN)Creating tag $(VERSION)...$(NC)"
	git tag -a $(VERSION) -m "Release $(VERSION)"
	git push origin $(VERSION)

# =================
# HELP
# =================

info: ## Display project information
	@echo "$(GREEN)GoKeys License Management Platform$(NC)"
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Commit: $(COMMIT)"
	@echo "Go Version: $(shell go version)"
	@echo "Docker Version: $(shell docker --version)"

check-deps: ## Check if required dependencies are installed
	@echo "$(GREEN)Checking dependencies...$(NC)"
	@command -v go >/dev/null 2>&1 || { echo "$(RED)Go is not installed$(NC)"; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Docker is not installed$(NC)"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "$(RED)Docker Compose is not installed$(NC)"; exit 1; }
	@command -v psql >/dev/null 2>&1 || { echo "$(YELLOW)PostgreSQL client is not installed$(NC)"; }
	@echo "$(GREEN)All dependencies are available$(NC)"