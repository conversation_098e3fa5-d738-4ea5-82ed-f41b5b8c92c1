package integration

// NOTE: This file is disabled - use sqlite_test.go instead
/*

import (
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/google/uuid"
)

// === POLICY BUSINESS LOGIC TESTS ===
// Test comprehensive business logic cho Policy entity
// Bao gồm: validation, strategies, defaults, business rules

// TestPolicyStrategies test all policy strategies
func (suite *SimpleAPITestSuite) TestPolicyStrategies() {
	// Test policy with all strategies set
	policy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Full Strategy Policy",
		Floating:       true,
		MaxMachines:    &[]int{10}[0],
		Duration:       &[]int{86400 * 30}[0], // 30 days
	}

	// Set all strategies manually
	machineStrategy := "UNIQUE_PER_ACCOUNT"
	componentStrategy := "UNIQUE_PER_PRODUCT"
	machineMatching := "MATCH_ALL"
	componentMatching := "MATCH_MOST"
	expirationStrategy := "RESTRICT_ACCESS"
	expirationBasis := "FROM_CREATION"
	renewalBasis := "FROM_EXPIRY"
	transferStrategy := "RESET_EXPIRY"
	authStrategy := "TOKEN"
	processLeasing := "PER_MACHINE"
	machineLeasing := "PER_USER"
	overageStrategy := "ALLOW_1_25X_OVERAGE"
	heartbeatCull := "DEACTIVATE_DEAD"
	heartbeatResurrection := "ALWAYS_REVIVE"
	heartbeatBasis := "FROM_CREATION"

	policy.MachineUniquenessStrategy = &machineStrategy
	policy.ComponentUniquenessStrategy = &componentStrategy
	policy.MachineMatchingStrategy = &machineMatching
	policy.ComponentMatchingStrategy = &componentMatching
	policy.ExpirationStrategy = &expirationStrategy
	policy.ExpirationBasis = &expirationBasis
	policy.RenewalBasis = &renewalBasis
	policy.TransferStrategy = &transferStrategy
	policy.AuthenticationStrategy = &authStrategy
	policy.ProcessLeasingStrategy = &processLeasing
	policy.MachineLeasingStrategy = &machineLeasing
	policy.OverageStrategy = &overageStrategy
	policy.HeartbeatCullStrategy = &heartbeatCull
	policy.HeartbeatResurrectionStrategy = &heartbeatResurrection
	policy.HeartbeatBasis = &heartbeatBasis

	err := suite.db.Create(policy).Error
	suite.NoError(err)

	// Test strategy business logic methods
	suite.True(policy.MachineUniquePerAccount())
	suite.False(policy.MachineUniquePerLicense())
	suite.True(policy.ComponentUniquePerProduct())
	suite.False(policy.ComponentUniquePerMachine())
	suite.True(policy.MachineMatchAll())
	suite.False(policy.MachineMatchAny())
	suite.True(policy.ComponentMatchMost())
	suite.False(policy.ComponentMatchAny())
	suite.True(policy.RestrictAccess())
	suite.False(policy.AllowAccess())
	suite.True(policy.ExpireFromCreation())
	suite.False(policy.ExpireFromFirstUse())
	suite.True(policy.RenewFromExpiry())
	suite.False(policy.RenewFromNow())
	suite.True(policy.ResetExpiryOnTransfer())
	suite.False(policy.KeepExpiryOnTransfer())
	suite.True(policy.SupportsTokenAuth())
	suite.False(policy.SupportsLicenseAuth())
	suite.True(policy.ProcessLeasePerMachine())
	suite.False(policy.ProcessLeasePerLicense())
	suite.True(policy.MachineLeasePerUser())
	suite.False(policy.MachineLeasePerLicense())
	suite.True(policy.Allow125xOverage())
	suite.False(policy.NoOverage())
	suite.True(policy.DeactivateDead())
	suite.False(policy.KeepDead())
	suite.True(policy.AlwaysResurrectDead())
	suite.True(policy.ResurrectDead()) // Should be true since strategy is ALWAYS_REVIVE
	suite.True(policy.HeartbeatFromCreation())
	suite.False(policy.HeartbeatFromFirstPing())
}

// TestPolicyDefaults test SetDefaults functionality
func (suite *SimpleAPITestSuite) TestPolicyDefaults() {
	// Test policy without any strategies set
	policy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Default Policy",
		Floating:       false,
		Duration:       &[]int{86400 * 7}[0], // 7 days
	}

	// Before SetDefaults - strategies should be nil
	suite.Nil(policy.MachineUniquenessStrategy)
	suite.Nil(policy.ComponentUniquenessStrategy)
	suite.Nil(policy.MachineMatchingStrategy)
	suite.Nil(policy.ComponentMatchingStrategy)

	// Call SetDefaults
	policy.SetDefaults()

	// After SetDefaults - strategies should have default values
	suite.NotNil(policy.MachineUniquenessStrategy)
	suite.Equal("UNIQUE_PER_LICENSE", *policy.MachineUniquenessStrategy)

	suite.NotNil(policy.ComponentUniquenessStrategy)
	suite.Equal("UNIQUE_PER_MACHINE", *policy.ComponentUniquenessStrategy)

	suite.NotNil(policy.MachineMatchingStrategy)
	suite.Equal("MATCH_ANY", *policy.MachineMatchingStrategy)

	suite.NotNil(policy.ComponentMatchingStrategy)
	suite.Equal("MATCH_ANY", *policy.ComponentMatchingStrategy)

	suite.NotNil(policy.ExpirationStrategy)
	suite.Equal("RESTRICT_ACCESS", *policy.ExpirationStrategy)

	suite.NotNil(policy.ExpirationBasis)
	suite.Equal("FROM_CREATION", *policy.ExpirationBasis)

	suite.NotNil(policy.RenewalBasis)
	suite.Equal("FROM_EXPIRY", *policy.RenewalBasis)

	suite.NotNil(policy.TransferStrategy)
	suite.Equal("RESET_EXPIRY", *policy.TransferStrategy)

	suite.NotNil(policy.AuthenticationStrategy)
	suite.Equal("TOKEN", *policy.AuthenticationStrategy)

	suite.NotNil(policy.ProcessLeasingStrategy)
	suite.Equal("PER_LICENSE", *policy.ProcessLeasingStrategy)

	suite.NotNil(policy.MachineLeasingStrategy)
	suite.Equal("PER_LICENSE", *policy.MachineLeasingStrategy)

	suite.NotNil(policy.OverageStrategy)
	suite.Equal("NO_OVERAGE", *policy.OverageStrategy)

	suite.NotNil(policy.HeartbeatCullStrategy)
	suite.Equal("DEACTIVATE_DEAD", *policy.HeartbeatCullStrategy)

	suite.NotNil(policy.HeartbeatResurrectionStrategy)
	suite.Equal("NO_REVIVE", *policy.HeartbeatResurrectionStrategy)

	suite.NotNil(policy.HeartbeatBasis)
	suite.Equal("FROM_CREATION", *policy.HeartbeatBasis)

	// Test default max machines based on floating
	suite.NotNil(policy.MaxMachines)
	suite.Equal(1, *policy.MaxMachines) // Node-locked default

	// Test floating policy defaults
	floatingPolicy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Floating Policy",
		Floating:       true,
		Duration:       &[]int{86400 * 7}[0],
	}

	floatingPolicy.SetDefaults()
	suite.NotNil(floatingPolicy.MaxMachines)
	suite.Equal(5, *floatingPolicy.MaxMachines) // Floating default
}

// TestPolicyValidation test comprehensive validation
func (suite *SimpleAPITestSuite) TestPolicyValidation() {
	// Test valid policy
	validPolicy := &entities.Policy{
		ID:                uuid.New().String(),
		OrganizationID:    suite.testOrg.ID,
		ProductID:         suite.testProduct.ID,
		Name:              "Valid Policy",
		Floating:          true,
		Duration:          &[]int{86400 * 30}[0], // 30 days
		MaxMachines:       &[]int{10}[0],
		HeartbeatDuration: &[]int{600}[0], // 10 minutes
	}

	validPolicy.SetDefaults()
	validationErrors := validPolicy.ValidatePolicy()
	suite.Empty(validationErrors, "Valid policy should have no validation errors")

	// Test invalid policy - missing name
	invalidPolicy1 := &entities.Policy{
		Name:     "", // Empty name
		Duration: &[]int{86400 * 7}[0],
	}

	validationErrors = invalidPolicy1.ValidatePolicy()
	suite.NotEmpty(validationErrors)
	suite.Contains(validationErrors[0], "name is required")

	// Test invalid policy - duration too short
	invalidPolicy2 := &entities.Policy{
		Name:     "Invalid Duration Policy",
		Duration: &[]int{3600}[0], // 1 hour - less than 1 day
	}

	validationErrors = invalidPolicy2.ValidatePolicy()
	suite.NotEmpty(validationErrors)
	suite.Contains(validationErrors[0], "duration must be greater than or equal to 86400")

	// Test invalid policy - duration too long
	invalidPolicy3 := &entities.Policy{
		Name:     "Invalid Duration Policy",
		Duration: &[]int{2147483648}[0], // Greater than max int32
	}

	validationErrors = invalidPolicy3.ValidatePolicy()
	suite.NotEmpty(validationErrors)
	suite.Contains(validationErrors[0], "duration must be less than or equal to 2147483647")

	// Test invalid policy - heartbeat duration too short
	invalidPolicy4 := &entities.Policy{
		Name:              "Invalid Heartbeat Policy",
		Duration:          &[]int{86400 * 7}[0],
		HeartbeatDuration: &[]int{30}[0], // 30 seconds - less than 1 minute
	}

	validationErrors = invalidPolicy4.ValidatePolicy()
	suite.NotEmpty(validationErrors)
	suite.Contains(validationErrors[0], "heartbeat_duration must be greater than or equal to 60")

	// Test invalid policy - negative max machines
	invalidPolicy5 := &entities.Policy{
		Name:        "Invalid Max Machines Policy",
		Duration:    &[]int{86400 * 7}[0],
		MaxMachines: &[]int{-1}[0],
	}

	validationErrors = invalidPolicy5.ValidatePolicy()
	suite.NotEmpty(validationErrors)
	suite.Contains(validationErrors[0], "max_machines must be greater than or equal to 0")
}

// TestPolicyStrategyRanks test strategy ranking system
func (suite *SimpleAPITestSuite) TestPolicyStrategyRanks() {
	policy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Rank Test Policy",
	}

	// Test machine uniqueness strategy ranks
	accountStrategy := "UNIQUE_PER_ACCOUNT"
	policy.MachineUniquenessStrategy = &accountStrategy
	suite.Equal(4, policy.GetMachineUniquenessStrategyRank()) // Highest rank

	productStrategy := "UNIQUE_PER_PRODUCT"
	policy.MachineUniquenessStrategy = &productStrategy
	suite.Equal(3, policy.GetMachineUniquenessStrategyRank())

	policyStrategy := "UNIQUE_PER_POLICY"
	policy.MachineUniquenessStrategy = &policyStrategy
	suite.Equal(2, policy.GetMachineUniquenessStrategyRank())

	licenseStrategy := "UNIQUE_PER_LICENSE"
	policy.MachineUniquenessStrategy = &licenseStrategy
	suite.Equal(1, policy.GetMachineUniquenessStrategyRank()) // Lowest rank

	// Test component uniqueness strategy ranks
	policy.ComponentUniquenessStrategy = &accountStrategy
	suite.Equal(4, policy.GetComponentUniquenessStrategyRank())

	policy.ComponentUniquenessStrategy = &productStrategy
	suite.Equal(3, policy.GetComponentUniquenessStrategyRank())

	machineStrategy := "UNIQUE_PER_MACHINE"
	policy.ComponentUniquenessStrategy = &machineStrategy
	suite.Equal(2, policy.GetComponentUniquenessStrategyRank())

	policy.ComponentUniquenessStrategy = &licenseStrategy
	suite.Equal(1, policy.GetComponentUniquenessStrategyRank())

	// Test invalid strategy
	invalidStrategy := "INVALID_STRATEGY"
	policy.MachineUniquenessStrategy = &invalidStrategy
	suite.Equal(-1, policy.GetMachineUniquenessStrategyRank())
}

// TestPolicyPoolManagement test pool-related methods
func (suite *SimpleAPITestSuite) TestPolicyPoolManagement() {
	// Test policy with pool
	policy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Pool Policy",
		UsePool:        true,
	}

	err := suite.db.Create(policy).Error
	suite.NoError(err)

	// Test pool methods
	suite.True(policy.UsesPool())
	suite.Equal(0, policy.GetPoolSize()) // No licenses yet
	suite.True(policy.IsPoolEmpty())
	suite.False(policy.IsPoolFull())

	// Test non-pool policy
	nonPoolPolicy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Non-Pool Policy",
		UsePool:        false,
	}

	err = suite.db.Create(nonPoolPolicy).Error
	suite.NoError(err)

	suite.False(nonPoolPolicy.UsesPool())
	suite.Equal(0, nonPoolPolicy.GetPoolSize())
	suite.True(nonPoolPolicy.IsPoolEmpty()) // Non-pool is considered empty
}

// TestPolicyMetadata test metadata functionality
func (suite *SimpleAPITestSuite) TestPolicyMetadata() {
	metadata := entities.Metadata{
		"policy_type":   "enterprise",
		"created_by":    "<EMAIL>",
		"approval_date": "2024-01-15",
		"restrictions": map[string]interface{}{
			"geographic": []string{"US", "CA", "EU"},
			"industries": []string{"tech", "finance"},
		},
	}

	policy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Metadata Policy",
		Metadata:       metadata,
	}

	policy.SetDefaults()
	err := suite.db.Create(policy).Error
	suite.NoError(err)

	// Verify metadata is saved correctly
	var retrievedPolicy entities.Policy
	err = suite.db.Where("id = ?", policy.ID).First(&retrievedPolicy).Error
	suite.NoError(err)

	suite.Equal("enterprise", retrievedPolicy.Metadata["policy_type"])
	suite.Equal("<EMAIL>", retrievedPolicy.Metadata["created_by"])
	suite.Equal("2024-01-15", retrievedPolicy.Metadata["approval_date"])

	// Test nested metadata
	restrictions, ok := retrievedPolicy.Metadata["restrictions"].(map[string]interface{})
	suite.True(ok)

	geographic, ok := restrictions["geographic"].([]interface{})
	suite.True(ok)
	suite.Len(geographic, 3)
	suite.Contains(geographic, "US")
	suite.Contains(geographic, "CA")
	suite.Contains(geographic, "EU")
}
*/
