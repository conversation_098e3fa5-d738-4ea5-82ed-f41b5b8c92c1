package integration

// NOTE: This file is disabled - use sqlite_test.go instead
/*

import (
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"github.com/gokeys/gokeys/internal/domain/entities"
)

// APITestSuite là simplified test suite cho basic API testing
// Tập trung vào entity business logic mà không cần complex HTTP setup
type APITestSuite struct {
	suite.Suite
	db *gorm.DB

	// Test data
	testOrg     *entities.Organization
	testProduct *entities.Product
	testPolicy  *entities.Policy
	testLicense *entities.License
	testMachine *entities.Machine
}

// SetupSuite khởi tạo test suite với database
func (suite *APITestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)

	// Setup in-memory SQLite database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = db

	// Auto migrate all entities
	err = db.AutoMigrate(
		&entities.Organization{},
		&entities.Product{},
		&entities.Policy{},
		&entities.License{},
		&entities.Machine{},
		&entities.User{},
	)
	require.NoError(suite.T(), err)
}

// SetupTest tạo test data mới cho mỗi test case
func (suite *APITestSuite) SetupTest() {
	// Clean database
	suite.db.Exec("DELETE FROM machines")
	suite.db.Exec("DELETE FROM licenses")
	suite.db.Exec("DELETE FROM policies")
	suite.db.Exec("DELETE FROM products")
	suite.db.Exec("DELETE FROM organizations")

	// Create test organization
	suite.testOrg = &entities.Organization{
		ID:   uuid.New().String(),
		Name: "Test Organization",
		Slug: "test-org",
	}
	err := suite.db.Create(suite.testOrg).Error
	require.NoError(suite.T(), err)

	// Create test product
	suite.testProduct = &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Test Product",
		Code:           "test-product",
		Key:            "test-product-key",
	}
	err = suite.db.Create(suite.testProduct).Error
	require.NoError(suite.T(), err)

	// Create test policy
	suite.testPolicy = &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Test Policy",
		Floating:       true,
		MaxMachines:    &[]int{5}[0],
		Duration:       &[]int{86400 * 30}[0], // 30 days
	}
	// Set defaults
	suite.testPolicy.SetDefaults()
	err = suite.db.Create(suite.testPolicy).Error
	require.NoError(suite.T(), err)

	// Create test license
	suite.testLicense = &entities.License{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		PolicyID:       suite.testPolicy.ID,
		Key:            "TEST-LICENSE-KEY-" + uuid.New().String()[:8],
		OwnerType:      "organization",
		OwnerID:        suite.testOrg.ID,
		Status:         "active",
	}
	err = suite.db.Create(suite.testLicense).Error
	require.NoError(suite.T(), err)

	// Create test machine
	suite.testMachine = &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "test-fingerprint-" + uuid.New().String()[:8],
		Name:        &[]string{"Test Machine"}[0],
		Platform:    &[]string{"linux"}[0],
		Cores:       4,
		Status:      "active",
	}
	err = suite.db.Create(suite.testMachine).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleanup sau khi chạy xong tất cả tests
func (suite *APITestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// Helper methods for database testing

// createTestData tạo test data cơ bản
func (suite *APITestSuite) createTestData() {
	// Test data đã được tạo trong SetupTest
}

// TestAPITestSuite chạy test suite
func TestAPITestSuite(t *testing.T) {
	suite.Run(t, new(APITestSuite))
}
*/
