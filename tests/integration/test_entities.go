package integration

import (
	"time"

	"gorm.io/gorm"
)

// Simplified test entities for SQLite compatibility
// These entities avoid complex constraints that SQLite doesn't support

type TestOrganization struct {
	ID        string    `json:"id" gorm:"type:text;primary_key"`
	Name      string    `json:"name" gorm:"size:255;not null"`
	Slug      string    `json:"slug" gorm:"size:255;not null"`
	Email     string    `json:"email" gorm:"size:255;not null"`
	Type      string    `json:"type" gorm:"size:50;default:'vendor'"`
	Status    string    `json:"status" gorm:"size:50;default:'active'"`
	Protected bool      `json:"protected" gorm:"default:false"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

type TestProduct struct {
	ID             string    `json:"id" gorm:"type:text;primary_key"`
	OrganizationID string    `json:"organization_id" gorm:"type:text;not null"`
	Name           string    `json:"name" gorm:"size:255;not null"`
	Code           string    `json:"code" gorm:"size:255;not null"`
	Key            string    `json:"key" gorm:"size:255;not null"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

type TestPolicy struct {
	ID             string    `json:"id" gorm:"type:text;primary_key"`
	OrganizationID string    `json:"organization_id" gorm:"type:text;not null"`
	ProductID      string    `json:"product_id" gorm:"type:text;not null"`
	Name           string    `json:"name" gorm:"size:255;not null"`
	Floating       bool      `json:"floating" gorm:"default:false"`
	UsePool        bool      `json:"use_pool" gorm:"default:false"`
	MaxMachines    *int      `json:"max_machines"`
	MaxCores       *int      `json:"max_cores"`
	MaxUsers       *int      `json:"max_users"`
	Duration       *int      `json:"duration"`
	RequireHeartbeat bool    `json:"require_heartbeat" gorm:"default:false"`
	HeartbeatDuration *int   `json:"heartbeat_duration"`
	
	// Strategy fields (simplified)
	MachineUniquenessStrategy     *string `json:"machine_uniqueness_strategy"`
	ComponentUniquenessStrategy   *string `json:"component_uniqueness_strategy"`
	MachineMatchingStrategy       *string `json:"machine_matching_strategy"`
	ComponentMatchingStrategy     *string `json:"component_matching_strategy"`
	ExpirationStrategy            *string `json:"expiration_strategy"`
	AuthenticationStrategy        *string `json:"authentication_strategy"`
	OverageStrategy               *string `json:"overage_strategy"`
	
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

type TestLicense struct {
	ID             string    `json:"id" gorm:"type:text;primary_key"`
	OrganizationID string    `json:"organization_id" gorm:"type:text;not null"`
	ProductID      string    `json:"product_id" gorm:"type:text;not null"`
	PolicyID       string    `json:"policy_id" gorm:"type:text;not null"`
	Key            string    `json:"key" gorm:"size:255;not null"`
	OwnerType      string    `json:"owner_type" gorm:"size:50;not null"`
	OwnerID        string    `json:"owner_id" gorm:"type:text;not null"`
	Status         string    `json:"status" gorm:"size:50;default:'active'"`
	Suspended      bool      `json:"suspended" gorm:"default:false"`
	Uses           int       `json:"uses" gorm:"default:0"`
	
	// Override fields
	MaxMachinesOverride *int `json:"max_machines_override"`
	MaxCoresOverride    *int `json:"max_cores_override"`
	MaxUsesOverride     *int `json:"max_uses_override"`
	
	// Timestamps
	ExpiresAt   *time.Time `json:"expires_at"`
	LastUsed    *time.Time `json:"last_used"`
	CreatedAt   time.Time  `json:"created_at"`
	UpdatedAt   time.Time  `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

type TestMachine struct {
	ID          string    `json:"id" gorm:"type:text;primary_key"`
	LicenseID   string    `json:"license_id" gorm:"type:text;not null"`
	PolicyID    string    `json:"policy_id" gorm:"type:text;not null"`
	Fingerprint string    `json:"fingerprint" gorm:"size:255;not null"`
	Name        *string   `json:"name"`
	Platform    *string   `json:"platform"`
	Cores       int       `json:"cores" gorm:"default:1"`
	Status      string    `json:"status" gorm:"size:50;default:'active'"`
	
	// Timestamps
	ActivatedAt   *time.Time `json:"activated_at"`
	DeactivatedAt *time.Time `json:"deactivated_at"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// Business logic methods for TestPolicy

func (p *TestPolicy) IsFloating() bool {
	return p.Floating
}

func (p *TestPolicy) IsNodeLocked() bool {
	return !p.Floating
}

func (p *TestPolicy) RequiresHeartbeat() bool {
	return p.RequireHeartbeat
}

func (p *TestPolicy) UsesPool() bool {
	return p.UsePool
}

func (p *TestPolicy) MachineUniquePerLicense() bool {
	return p.MachineUniquenessStrategy == nil || *p.MachineUniquenessStrategy == "UNIQUE_PER_LICENSE"
}

func (p *TestPolicy) MachineUniquePerAccount() bool {
	return p.MachineUniquenessStrategy != nil && *p.MachineUniquenessStrategy == "UNIQUE_PER_ACCOUNT"
}

func (p *TestPolicy) MachineUniquePerPolicy() bool {
	return p.MachineUniquenessStrategy != nil && *p.MachineUniquenessStrategy == "UNIQUE_PER_POLICY"
}

func (p *TestPolicy) ComponentUniquePerMachine() bool {
	return p.ComponentUniquenessStrategy == nil || *p.ComponentUniquenessStrategy == "UNIQUE_PER_MACHINE"
}

func (p *TestPolicy) ComponentUniquePerProduct() bool {
	return p.ComponentUniquenessStrategy != nil && *p.ComponentUniquenessStrategy == "UNIQUE_PER_PRODUCT"
}

func (p *TestPolicy) MachineMatchAny() bool {
	return p.MachineMatchingStrategy == nil || *p.MachineMatchingStrategy == "MATCH_ANY"
}

func (p *TestPolicy) MachineMatchMost() bool {
	return p.MachineMatchingStrategy != nil && *p.MachineMatchingStrategy == "MATCH_MOST"
}

func (p *TestPolicy) MachineMatchAll() bool {
	return p.MachineMatchingStrategy != nil && *p.MachineMatchingStrategy == "MATCH_ALL"
}

func (p *TestPolicy) ComponentMatchAny() bool {
	return p.ComponentMatchingStrategy == nil || *p.ComponentMatchingStrategy == "MATCH_ANY"
}

func (p *TestPolicy) ComponentMatchTwo() bool {
	return p.ComponentMatchingStrategy != nil && *p.ComponentMatchingStrategy == "MATCH_TWO"
}

func (p *TestPolicy) ComponentMatchMost() bool {
	return p.ComponentMatchingStrategy != nil && *p.ComponentMatchingStrategy == "MATCH_MOST"
}

func (p *TestPolicy) RestrictAccess() bool {
	return p.ExpirationStrategy == nil || *p.ExpirationStrategy == "RESTRICT_ACCESS"
}

func (p *TestPolicy) AllowAccess() bool {
	return p.ExpirationStrategy != nil && *p.ExpirationStrategy == "ALLOW_ACCESS"
}

func (p *TestPolicy) SupportsTokenAuth() bool {
	return p.AuthenticationStrategy == nil || *p.AuthenticationStrategy == "TOKEN"
}

func (p *TestPolicy) SupportsLicenseAuth() bool {
	return p.AuthenticationStrategy != nil && *p.AuthenticationStrategy == "LICENSE"
}

func (p *TestPolicy) Allow125xOverage() bool {
	return p.OverageStrategy != nil && *p.OverageStrategy == "ALLOW_1_25X_OVERAGE"
}

func (p *TestPolicy) NoOverage() bool {
	return p.OverageStrategy == nil || *p.OverageStrategy == "NO_OVERAGE"
}

// SetDefaults sets default values for policy
func (p *TestPolicy) SetDefaults() {
	if p.MachineUniquenessStrategy == nil {
		strategy := "UNIQUE_PER_LICENSE"
		p.MachineUniquenessStrategy = &strategy
	}
	
	if p.ComponentUniquenessStrategy == nil {
		strategy := "UNIQUE_PER_MACHINE"
		p.ComponentUniquenessStrategy = &strategy
	}
	
	if p.MachineMatchingStrategy == nil {
		strategy := "MATCH_ANY"
		p.MachineMatchingStrategy = &strategy
	}
	
	if p.ComponentMatchingStrategy == nil {
		strategy := "MATCH_ANY"
		p.ComponentMatchingStrategy = &strategy
	}
	
	if p.ExpirationStrategy == nil {
		strategy := "RESTRICT_ACCESS"
		p.ExpirationStrategy = &strategy
	}
	
	if p.AuthenticationStrategy == nil {
		strategy := "TOKEN"
		p.AuthenticationStrategy = &strategy
	}
	
	if p.OverageStrategy == nil {
		strategy := "NO_OVERAGE"
		p.OverageStrategy = &strategy
	}
	
	if p.MaxMachines == nil {
		maxMachines := 1
		p.MaxMachines = &maxMachines
	}
}

// ValidatePolicy validates policy fields
func (p *TestPolicy) ValidatePolicy() []string {
	var errors []string
	
	if p.Name == "" {
		errors = append(errors, "name is required")
	}
	
	if p.Duration != nil && *p.Duration < 86400 {
		errors = append(errors, "duration must be greater than or equal to 86400 seconds (1 day)")
	}
	
	if p.Duration != nil && *p.Duration > 2147483647 {
		errors = append(errors, "duration must be less than or equal to 2147483647 seconds")
	}
	
	if p.HeartbeatDuration != nil && *p.HeartbeatDuration < 60 {
		errors = append(errors, "heartbeat_duration must be greater than or equal to 60 seconds (1 minute)")
	}
	
	if p.MaxMachines != nil && *p.MaxMachines < 0 {
		errors = append(errors, "max_machines must be greater than or equal to 0")
	}
	
	return errors
}

// Business logic methods for TestMachine

func (m *TestMachine) IsActive() bool {
	return m.Status == "active"
}

func (m *TestMachine) IsInactive() bool {
	return m.Status == "inactive"
}

func (m *TestMachine) Activate() {
	m.Status = "active"
	now := time.Now()
	m.ActivatedAt = &now
	m.DeactivatedAt = nil
}

func (m *TestMachine) Deactivate() {
	m.Status = "inactive"
	now := time.Now()
	m.DeactivatedAt = &now
}

// ValidateMachine validates machine fields
func (m *TestMachine) ValidateMachine() []string {
	var errors []string
	
	if m.Fingerprint == "" {
		errors = append(errors, "fingerprint is required")
	}
	
	if m.Cores <= 0 {
		errors = append(errors, "cores must be greater than 0")
	}
	
	return errors
}
