package integration

// NOTE: This file is disabled due to SQLite constraint compatibility issues
// Use sqlite_test.go instead for working tests

/*

import (
	"bytes"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"github.com/gokeys/gokeys/internal/domain/entities"
)

// SimpleAPITestSuite là simplified test suite cho API testing
// Tập trung vào business logic testing mà không phụ thuộc vào complex dependencies
type SimpleAPITestSuite struct {
	suite.Suite
	db *gorm.DB

	// Test data
	testOrg     *entities.Organization
	testProduct *entities.Product
	testPolicy  *entities.Policy
	testLicense *entities.License
	testMachine *entities.Machine
}

// SetupSuite khởi tạo test suite với database
func (suite *SimpleAPITestSuite) SetupSuite() {
	gin.SetMode(gin.TestMode)

	// Setup in-memory SQLite database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = db

	// Auto migrate all entities (disable foreign key constraints for SQLite)
	db.Exec("PRAGMA foreign_keys = OFF")

	err = db.AutoMigrate(
		&entities.Organization{},
		&entities.Product{},
		&entities.Policy{},
		&entities.License{},
		&entities.Machine{},
		&entities.User{},
	)
	require.NoError(suite.T(), err)
}

// SetupTest tạo test data mới cho mỗi test case
func (suite *SimpleAPITestSuite) SetupTest() {
	// Clean database
	suite.db.Exec("DELETE FROM machines")
	suite.db.Exec("DELETE FROM licenses")
	suite.db.Exec("DELETE FROM policies")
	suite.db.Exec("DELETE FROM products")
	suite.db.Exec("DELETE FROM organizations")

	// Create test organization
	suite.testOrg = &entities.Organization{
		ID:   uuid.New().String(),
		Name: "Test Organization",
		Slug: "test-org",
	}
	err := suite.db.Create(suite.testOrg).Error
	require.NoError(suite.T(), err)

	// Create test product
	suite.testProduct = &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Test Product",
		Code:           "test-product",
		Key:            "test-product-key",
	}
	err = suite.db.Create(suite.testProduct).Error
	require.NoError(suite.T(), err)

	// Create test policy
	suite.testPolicy = &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Test Policy",
		Floating:       true,
		MaxMachines:    &[]int{5}[0],
		Duration:       &[]int{86400 * 30}[0], // 30 days
	}
	// Set defaults
	suite.testPolicy.SetDefaults()
	err = suite.db.Create(suite.testPolicy).Error
	require.NoError(suite.T(), err)

	// Create test license
	suite.testLicense = &entities.License{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		PolicyID:       suite.testPolicy.ID,
		Key:            "TEST-LICENSE-KEY-" + uuid.New().String()[:8],
		OwnerType:      "organization",
		OwnerID:        suite.testOrg.ID,
		Status:         "active",
	}
	err = suite.db.Create(suite.testLicense).Error
	require.NoError(suite.T(), err)

	// Create test machine
	suite.testMachine = &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "test-fingerprint-" + uuid.New().String()[:8],
		Name:        &[]string{"Test Machine"}[0],
		Platform:    &[]string{"linux"}[0],
		Cores:       4,
		Status:      "active",
	}
	err = suite.db.Create(suite.testMachine).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleanup sau khi chạy xong tất cả tests
func (suite *SimpleAPITestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// Helper methods

// makeJSONRequest tạo HTTP request với JSON body
func (suite *SimpleAPITestSuite) makeJSONRequest(method, path string, body interface{}) *httptest.ResponseRecorder {
	var reqBody []byte
	if body != nil {
		var err error
		reqBody, err = json.Marshal(body)
		require.NoError(suite.T(), err)
	}

	req := httptest.NewRequest(method, path, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	return w
}

// parseJSONResponse parse JSON response thành struct
func (suite *SimpleAPITestSuite) parseJSONResponse(w *httptest.ResponseRecorder, target interface{}) {
	err := json.Unmarshal(w.Body.Bytes(), target)
	require.NoError(suite.T(), err)
}

// === ENTITY BUSINESS LOGIC TESTS ===
// Test business logic của entities mà không cần HTTP layer

// TestOrganizationEntity test Organization entity business logic
func (suite *SimpleAPITestSuite) TestOrganizationEntity() {
	// Test organization creation
	org := &entities.Organization{
		ID:   uuid.New().String(),
		Name: "Test Org",
		Slug: "test-org",
	}

	err := suite.db.Create(org).Error
	suite.NoError(err)

	// Test organization retrieval
	var retrievedOrg entities.Organization
	err = suite.db.Where("id = ?", org.ID).First(&retrievedOrg).Error
	suite.NoError(err)
	suite.Equal(org.Name, retrievedOrg.Name)
	suite.Equal(org.Slug, retrievedOrg.Slug)

	// Test organization update
	retrievedOrg.Name = "Updated Org"
	err = suite.db.Save(&retrievedOrg).Error
	suite.NoError(err)

	// Verify update
	var updatedOrg entities.Organization
	err = suite.db.Where("id = ?", org.ID).First(&updatedOrg).Error
	suite.NoError(err)
	suite.Equal("Updated Org", updatedOrg.Name)

	// Test soft delete
	err = suite.db.Delete(&updatedOrg).Error
	suite.NoError(err)

	// Should not find with normal query
	err = suite.db.Where("id = ?", org.ID).First(&entities.Organization{}).Error
	suite.Error(err)

	// Should find with Unscoped
	err = suite.db.Unscoped().Where("id = ?", org.ID).First(&entities.Organization{}).Error
	suite.NoError(err)
}

// TestProductEntity test Product entity business logic
func (suite *SimpleAPITestSuite) TestProductEntity() {
	// Test product creation with organization relationship
	product := &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Test Product",
		Code:           "test-product",
		Key:            "test-product-key-" + uuid.New().String()[:8],
	}

	err := suite.db.Create(product).Error
	suite.NoError(err)

	// Test product retrieval with organization
	var retrievedProduct entities.Product
	err = suite.db.Preload("Organization").Where("id = ?", product.ID).First(&retrievedProduct).Error
	suite.NoError(err)
	suite.Equal(product.Name, retrievedProduct.Name)
	suite.Equal(suite.testOrg.ID, retrievedProduct.OrganizationID)
	suite.Equal(suite.testOrg.Name, retrievedProduct.Organization.Name)

	// Test product update
	retrievedProduct.Name = "Updated Product"
	err = suite.db.Save(&retrievedProduct).Error
	suite.NoError(err)

	// Test organization constraint
	invalidProduct := &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: uuid.New().String(), // Non-existent organization
		Name:           "Invalid Product",
		Code:           "invalid-product",
		Key:            "invalid-product-key",
	}

	err = suite.db.Create(invalidProduct).Error
	// Should succeed in SQLite (no foreign key constraint by default)
	// In production with proper constraints, this would fail
	suite.NoError(err)
}

// TestPolicyEntity test Policy entity business logic
func (suite *SimpleAPITestSuite) TestPolicyEntity() {
	// Test policy creation
	policy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Test Policy",
		Floating:       true,
		MaxMachines:    &[]int{10}[0],
		Duration:       &[]int{86400 * 7}[0], // 7 days
	}

	// Test SetDefaults
	policy.SetDefaults()
	suite.NotNil(policy.MachineUniquenessStrategy)
	suite.Equal("UNIQUE_PER_LICENSE", *policy.MachineUniquenessStrategy)

	err := suite.db.Create(policy).Error
	suite.NoError(err)

	// Test policy business logic methods
	suite.True(policy.IsFloating())
	suite.False(policy.IsNodeLocked())
	suite.True(policy.MachineUniquePerLicense())
	suite.False(policy.MachineUniquePerAccount())

	// Test policy validation
	validationErrors := policy.ValidatePolicy()
	suite.Empty(validationErrors) // Should be valid

	// Test invalid policy
	invalidPolicy := &entities.Policy{
		Name:     "",             // Empty name should be invalid
		Duration: &[]int{100}[0], // Less than 1 day should be invalid
	}
	validationErrors = invalidPolicy.ValidatePolicy()
	suite.NotEmpty(validationErrors)
	suite.Contains(validationErrors[0], "name is required")
}

// TestLicenseEntity test License entity business logic
func (suite *SimpleAPITestSuite) TestLicenseEntity() {
	// Test license creation
	license := &entities.License{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		PolicyID:       suite.testPolicy.ID,
		Key:            "TEST-KEY-" + uuid.New().String()[:8],
		OwnerType:      "organization",
		OwnerID:        suite.testOrg.ID,
		Status:         "active",
		Uses:           5,
	}

	err := suite.db.Create(license).Error
	suite.NoError(err)

	// Test license retrieval with relationships
	var retrievedLicense entities.License
	err = suite.db.Preload("Organization").Preload("Product").Preload("Policy").
		Where("id = ?", license.ID).First(&retrievedLicense).Error
	suite.NoError(err)

	suite.Equal(license.Key, retrievedLicense.Key)
	suite.Equal(suite.testOrg.Name, retrievedLicense.Organization.Name)
	suite.Equal(suite.testProduct.Name, retrievedLicense.Product.Name)
	suite.Equal(suite.testPolicy.Name, retrievedLicense.Policy.Name)

	// Test license status updates
	retrievedLicense.Status = "suspended"
	retrievedLicense.Suspended = true
	err = suite.db.Save(&retrievedLicense).Error
	suite.NoError(err)

	// Test usage increment
	retrievedLicense.Uses += 1
	err = suite.db.Save(&retrievedLicense).Error
	suite.NoError(err)

	// Verify usage update
	var updatedLicense entities.License
	err = suite.db.Where("id = ?", license.ID).First(&updatedLicense).Error
	suite.NoError(err)
	suite.Equal(6, updatedLicense.Uses)
	suite.Equal("suspended", updatedLicense.Status)
	suite.True(updatedLicense.Suspended)
}

// TestMachineEntity test Machine entity business logic
func (suite *SimpleAPITestSuite) TestMachineEntity() {
	// Test machine creation
	machine := &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "test-fingerprint-123",
		Name:        &[]string{"Test Machine"}[0],
		Platform:    &[]string{"windows"}[0],
		Cores:       8,
		Status:      "active",
	}

	err := suite.db.Create(machine).Error
	suite.NoError(err)

	// Test machine retrieval with relationships
	var retrievedMachine entities.Machine
	err = suite.db.Preload("License").Preload("Policy").
		Where("id = ?", machine.ID).First(&retrievedMachine).Error
	suite.NoError(err)

	suite.Equal(machine.Fingerprint, retrievedMachine.Fingerprint)
	suite.Equal(suite.testLicense.Key, retrievedMachine.License.Key)
	suite.Equal(suite.testPolicy.Name, retrievedMachine.Policy.Name)

	// Test machine business logic methods (cần load policy relationship)
	retrievedMachine.Policy = *suite.testPolicy
	suite.True(retrievedMachine.IsActive())
	suite.False(retrievedMachine.IsInactive())

	// Test uniqueness strategies (delegate to policy)
	suite.True(retrievedMachine.UniquePerLicense()) // Default policy strategy
	suite.False(retrievedMachine.UniquePerAccount())

	// Test leasing strategies (delegate to policy)
	suite.True(retrievedMachine.LeasePerLicense()) // Default policy strategy
	suite.False(retrievedMachine.LeasePerUser())

	// Test machine activation/deactivation
	retrievedMachine.Deactivate()
	suite.True(retrievedMachine.IsInactive())
	suite.NotNil(retrievedMachine.DeactivatedAt)

	retrievedMachine.Activate()
	suite.True(retrievedMachine.IsActive())
	suite.NotNil(retrievedMachine.ActivatedAt)
	suite.Nil(retrievedMachine.DeactivatedAt)

	// Test machine validation
	validationErrors := retrievedMachine.ValidateMachine()
	suite.Empty(validationErrors) // Should be valid

	// Test invalid machine
	invalidMachine := &entities.Machine{
		Fingerprint: "", // Empty fingerprint should be invalid
		Cores:       0,  // Zero cores should be invalid
	}
	validationErrors = invalidMachine.ValidateMachine()
	suite.NotEmpty(validationErrors)
	suite.Contains(validationErrors[0], "fingerprint is required")
}

// TestMachineComponents test MachineComponents JSONB functionality
func (suite *SimpleAPITestSuite) TestMachineComponents() {
	// Test machine with components
	components := entities.MachineComponents{
		"cpu_id":         "Intel-i7-12700K",
		"motherboard_id": "ASUS-Z690-A",
		"disk_id":        "Samsung-980-PRO",
		"mac_address":    "00:11:22:33:44:55",
	}

	machine := &entities.Machine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "component-fingerprint-123",
		Components:  components,
		Status:      "active",
	}

	err := suite.db.Create(machine).Error
	suite.NoError(err)

	// Test component retrieval
	var retrievedMachine entities.Machine
	err = suite.db.Where("id = ?", machine.ID).First(&retrievedMachine).Error
	suite.NoError(err)

	// Test component methods
	suite.True(retrievedMachine.Components.HasComponent("cpu_id"))
	suite.False(retrievedMachine.Components.HasComponent("gpu_id"))
	suite.Equal("Intel-i7-12700K", retrievedMachine.Components.GetComponent("cpu_id"))
	suite.Equal("", retrievedMachine.Components.GetComponent("non_existent"))

	// Test fingerprint generation
	fingerprint := retrievedMachine.Components.GetFingerprint()
	suite.NotEmpty(fingerprint)
	suite.Contains(fingerprint, "Intel-i7-12700K")
	suite.Contains(fingerprint, "|") // Should contain separator

	// Test component update
	newComponents := entities.MachineComponents{
		"cpu_id":      "AMD-Ryzen-9-5900X",
		"gpu_id":      "RTX-4090",
		"mac_address": "00:11:22:33:44:66",
	}

	retrievedMachine.UpdateComponents(newComponents)
	err = suite.db.Save(&retrievedMachine).Error
	suite.NoError(err)

	// Verify component update
	var updatedMachine entities.Machine
	err = suite.db.Where("id = ?", machine.ID).First(&updatedMachine).Error
	suite.NoError(err)

	suite.Equal("AMD-Ryzen-9-5900X", updatedMachine.Components.GetComponent("cpu_id"))
	suite.Equal("RTX-4090", updatedMachine.Components.GetComponent("gpu_id"))
	suite.False(updatedMachine.Components.HasComponent("motherboard_id")) // Should be removed
}

// TestEntityRelationships test relationships between entities
func (suite *SimpleAPITestSuite) TestEntityRelationships() {
	// Test organization -> products relationship
	var org entities.Organization
	err := suite.db.Preload("Products").Where("id = ?", suite.testOrg.ID).First(&org).Error
	suite.NoError(err)
	suite.GreaterOrEqual(len(org.Products), 1)

	// Test product -> policies relationship
	var product entities.Product
	err = suite.db.Preload("Policies").Where("id = ?", suite.testProduct.ID).First(&product).Error
	suite.NoError(err)
	suite.GreaterOrEqual(len(product.Policies), 1)

	// Test license -> machines relationship
	var license entities.License
	err = suite.db.Preload("Machines").Where("id = ?", suite.testLicense.ID).First(&license).Error
	suite.NoError(err)
	suite.GreaterOrEqual(len(license.Machines), 1)

	// Test policy -> licenses relationship (if exists)
	var policy entities.Policy
	err = suite.db.Where("id = ?", suite.testPolicy.ID).First(&policy).Error
	suite.NoError(err)
	suite.Equal(suite.testProduct.ID, policy.ProductID)
	suite.Equal(suite.testOrg.ID, policy.OrganizationID)
}

// TestCascadeDelete test cascade delete behavior
func (suite *SimpleAPITestSuite) TestCascadeDelete() {
	// Create additional test data
	additionalProduct := &entities.Product{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Product to Delete",
		Code:           "product-to-delete",
		Key:            "product-to-delete-key",
	}
	err := suite.db.Create(additionalProduct).Error
	suite.NoError(err)

	additionalPolicy := &entities.Policy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      additionalProduct.ID,
		Name:           "Policy to Delete",
	}
	additionalPolicy.SetDefaults()
	err = suite.db.Create(additionalPolicy).Error
	suite.NoError(err)

	// Delete product
	err = suite.db.Delete(additionalProduct).Error
	suite.NoError(err)

	// Verify product is deleted
	var deletedProduct entities.Product
	err = suite.db.Where("id = ?", additionalProduct.ID).First(&deletedProduct).Error
	suite.Error(err) // Should not find

	// In a real application with proper foreign key constraints,
	// associated policies would also be deleted or the delete would fail
	// For now, we just verify the product deletion worked
}

// TestSimpleAPITestSuite chạy test suite
func TestSimpleAPITestSuite(t *testing.T) {
	suite.Run(t, new(SimpleAPITestSuite))
}
*/
