package integration

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// SQLiteTestSuite là test suite sử dụng simplified entities cho SQLite
// Tránh các complex constraints mà SQLite không support
type SQLiteTestSuite struct {
	suite.Suite
	db *gorm.DB
	
	// Test data
	testOrg     *TestOrganization
	testProduct *TestProduct
	testPolicy  *TestPolicy
	testLicense *TestLicense
	testMachine *TestMachine
}

// SetupSuite khởi tạo test suite với SQLite database
func (suite *SQLiteTestSuite) SetupSuite() {
	// Setup in-memory SQLite database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = db
	
	// Auto migrate test entities
	err = db.AutoMigrate(
		&TestOrganization{},
		&TestProduct{},
		&TestPolicy{},
		&TestLicense{},
		&TestMachine{},
	)
	require.NoError(suite.T(), err)
}

// SetupTest tạo test data mới cho mỗi test case
func (suite *SQLiteTestSuite) SetupTest() {
	// Clean database
	suite.db.Exec("DELETE FROM test_machines")
	suite.db.Exec("DELETE FROM test_licenses")
	suite.db.Exec("DELETE FROM test_policies")
	suite.db.Exec("DELETE FROM test_products")
	suite.db.Exec("DELETE FROM test_organizations")
	
	// Create test organization
	suite.testOrg = &TestOrganization{
		ID:    uuid.New().String(),
		Name:  "Test Organization",
		Slug:  "test-org",
		Email: "<EMAIL>",
		Type:  "vendor",
		Status: "active",
	}
	err := suite.db.Create(suite.testOrg).Error
	require.NoError(suite.T(), err)
	
	// Create test product
	suite.testProduct = &TestProduct{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "Test Product",
		Code:           "test-product",
		Key:            "test-product-key",
	}
	err = suite.db.Create(suite.testProduct).Error
	require.NoError(suite.T(), err)
	
	// Create test policy
	suite.testPolicy = &TestPolicy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Test Policy",
		Floating:       true,
		MaxMachines:    &[]int{5}[0],
		Duration:       &[]int{86400 * 30}[0], // 30 days
	}
	// Set defaults
	suite.testPolicy.SetDefaults()
	err = suite.db.Create(suite.testPolicy).Error
	require.NoError(suite.T(), err)
	
	// Create test license
	suite.testLicense = &TestLicense{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		PolicyID:       suite.testPolicy.ID,
		Key:            "TEST-LICENSE-KEY-" + uuid.New().String()[:8],
		OwnerType:      "organization",
		OwnerID:        suite.testOrg.ID,
		Status:         "active",
	}
	err = suite.db.Create(suite.testLicense).Error
	require.NoError(suite.T(), err)
	
	// Create test machine
	suite.testMachine = &TestMachine{
		ID:          uuid.New().String(),
		LicenseID:   suite.testLicense.ID,
		PolicyID:    suite.testPolicy.ID,
		Fingerprint: "test-fingerprint-" + uuid.New().String()[:8],
		Name:        &[]string{"Test Machine"}[0],
		Platform:    &[]string{"linux"}[0],
		Cores:       4,
		Status:      "active",
	}
	err = suite.db.Create(suite.testMachine).Error
	require.NoError(suite.T(), err)
}

// TearDownSuite cleanup sau khi chạy xong tất cả tests
func (suite *SQLiteTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

// === BASIC ENTITY TESTS ===

// TestOrganizationCRUD test Organization CRUD operations
func (suite *SQLiteTestSuite) TestOrganizationCRUD() {
	// Test organization creation
	org := &TestOrganization{
		ID:    uuid.New().String(),
		Name:  "New Test Org",
		Slug:  "new-test-org",
		Email: "<EMAIL>",
		Type:  "customer",
		Status: "active",
	}
	
	err := suite.db.Create(org).Error
	suite.NoError(err)
	
	// Test organization retrieval
	var retrievedOrg TestOrganization
	err = suite.db.Where("id = ?", org.ID).First(&retrievedOrg).Error
	suite.NoError(err)
	suite.Equal(org.Name, retrievedOrg.Name)
	suite.Equal(org.Slug, retrievedOrg.Slug)
	suite.Equal(org.Email, retrievedOrg.Email)
	suite.Equal(org.Type, retrievedOrg.Type)
	
	// Test organization update
	retrievedOrg.Name = "Updated Org"
	err = suite.db.Save(&retrievedOrg).Error
	suite.NoError(err)
	
	// Verify update
	var updatedOrg TestOrganization
	err = suite.db.Where("id = ?", org.ID).First(&updatedOrg).Error
	suite.NoError(err)
	suite.Equal("Updated Org", updatedOrg.Name)
	
	// Test soft delete
	err = suite.db.Delete(&updatedOrg).Error
	suite.NoError(err)
	
	// Should not find with normal query
	err = suite.db.Where("id = ?", org.ID).First(&TestOrganization{}).Error
	suite.Error(err)
	
	// Should find with Unscoped
	err = suite.db.Unscoped().Where("id = ?", org.ID).First(&TestOrganization{}).Error
	suite.NoError(err)
}

// TestProductCRUD test Product CRUD operations
func (suite *SQLiteTestSuite) TestProductCRUD() {
	// Test product creation
	product := &TestProduct{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		Name:           "New Test Product",
		Code:           "new-test-product",
		Key:            "new-test-product-key",
	}
	
	err := suite.db.Create(product).Error
	suite.NoError(err)
	
	// Test product retrieval
	var retrievedProduct TestProduct
	err = suite.db.Where("id = ?", product.ID).First(&retrievedProduct).Error
	suite.NoError(err)
	suite.Equal(product.Name, retrievedProduct.Name)
	suite.Equal(product.Code, retrievedProduct.Code)
	suite.Equal(suite.testOrg.ID, retrievedProduct.OrganizationID)
	
	// Test product update
	retrievedProduct.Name = "Updated Product"
	err = suite.db.Save(&retrievedProduct).Error
	suite.NoError(err)
	
	// Verify update
	var updatedProduct TestProduct
	err = suite.db.Where("id = ?", product.ID).First(&updatedProduct).Error
	suite.NoError(err)
	suite.Equal("Updated Product", updatedProduct.Name)
}

// TestPolicyBusinessLogic test Policy business logic methods
func (suite *SQLiteTestSuite) TestPolicyBusinessLogic() {
	// Test policy with specific strategies
	policy := &TestPolicy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Business Logic Policy",
		Floating:       true,
		MaxMachines:    &[]int{10}[0],
		Duration:       &[]int{86400 * 30}[0], // 30 days
	}
	
	// Set specific strategies
	machineStrategy := "UNIQUE_PER_ACCOUNT"
	componentStrategy := "UNIQUE_PER_PRODUCT"
	machineMatching := "MATCH_ALL"
	componentMatching := "MATCH_MOST"
	expirationStrategy := "RESTRICT_ACCESS"
	authStrategy := "TOKEN"
	overageStrategy := "ALLOW_1_25X_OVERAGE"
	
	policy.MachineUniquenessStrategy = &machineStrategy
	policy.ComponentUniquenessStrategy = &componentStrategy
	policy.MachineMatchingStrategy = &machineMatching
	policy.ComponentMatchingStrategy = &componentMatching
	policy.ExpirationStrategy = &expirationStrategy
	policy.AuthenticationStrategy = &authStrategy
	policy.OverageStrategy = &overageStrategy
	
	err := suite.db.Create(policy).Error
	suite.NoError(err)
	
	// Test business logic methods
	suite.True(policy.IsFloating())
	suite.False(policy.IsNodeLocked())
	suite.True(policy.MachineUniquePerAccount())
	suite.False(policy.MachineUniquePerLicense())
	suite.True(policy.ComponentUniquePerProduct())
	suite.False(policy.ComponentUniquePerMachine())
	suite.True(policy.MachineMatchAll())
	suite.False(policy.MachineMatchAny())
	suite.True(policy.ComponentMatchMost())
	suite.False(policy.ComponentMatchAny())
	suite.True(policy.RestrictAccess())
	suite.False(policy.AllowAccess())
	suite.True(policy.SupportsTokenAuth())
	suite.False(policy.SupportsLicenseAuth())
	suite.True(policy.Allow125xOverage())
	suite.False(policy.NoOverage())
}

// TestPolicyDefaults test SetDefaults functionality
func (suite *SQLiteTestSuite) TestPolicyDefaults() {
	// Test policy without any strategies set
	policy := &TestPolicy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Default Policy",
		Floating:       false,
		Duration:       &[]int{86400 * 7}[0], // 7 days
	}
	
	// Before SetDefaults - strategies should be nil
	suite.Nil(policy.MachineUniquenessStrategy)
	suite.Nil(policy.ComponentUniquenessStrategy)
	
	// Call SetDefaults
	policy.SetDefaults()
	
	// After SetDefaults - strategies should have default values
	suite.NotNil(policy.MachineUniquenessStrategy)
	suite.Equal("UNIQUE_PER_LICENSE", *policy.MachineUniquenessStrategy)
	
	suite.NotNil(policy.ComponentUniquenessStrategy)
	suite.Equal("UNIQUE_PER_MACHINE", *policy.ComponentUniquenessStrategy)
	
	suite.NotNil(policy.MaxMachines)
	suite.Equal(1, *policy.MaxMachines) // Default
}

// TestPolicyValidation test validation rules
func (suite *SQLiteTestSuite) TestPolicyValidation() {
	// Test valid policy
	validPolicy := &TestPolicy{
		ID:             uuid.New().String(),
		OrganizationID: suite.testOrg.ID,
		ProductID:      suite.testProduct.ID,
		Name:           "Valid Policy",
		Floating:       true,
		Duration:       &[]int{86400 * 30}[0], // 30 days
		MaxMachines:    &[]int{10}[0],
		HeartbeatDuration: &[]int{600}[0], // 10 minutes
	}
	
	validPolicy.SetDefaults()
	validationErrors := validPolicy.ValidatePolicy()
	suite.Empty(validationErrors, "Valid policy should have no validation errors")
	
	// Test invalid policy - missing name
	invalidPolicy := &TestPolicy{
		Name:     "", // Empty name
		Duration: &[]int{86400 * 7}[0],
	}
	
	validationErrors = invalidPolicy.ValidatePolicy()
	suite.NotEmpty(validationErrors)
	suite.Contains(validationErrors[0], "name is required")
}

// TestMachineBusinessLogic test Machine business logic methods
func (suite *SQLiteTestSuite) TestMachineBusinessLogic() {
	// Test machine business logic
	suite.True(suite.testMachine.IsActive())
	suite.False(suite.testMachine.IsInactive())
	
	// Test machine activation/deactivation
	suite.testMachine.Deactivate()
	suite.True(suite.testMachine.IsInactive())
	suite.NotNil(suite.testMachine.DeactivatedAt)
	
	suite.testMachine.Activate()
	suite.True(suite.testMachine.IsActive())
	suite.NotNil(suite.testMachine.ActivatedAt)
	suite.Nil(suite.testMachine.DeactivatedAt)
	
	// Test machine validation
	validationErrors := suite.testMachine.ValidateMachine()
	suite.Empty(validationErrors) // Should be valid
}

// TestSQLiteTestSuite chạy test suite
func TestSQLiteTestSuite(t *testing.T) {
	suite.Run(t, new(SQLiteTestSuite))
}
